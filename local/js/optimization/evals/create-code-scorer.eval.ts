import {
  _internalGetGlobalState,
  current<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>l<PERSON><PERSON><PERSON>,
  NOOP_SPAN,
  Span,
} from "braintrust";
import { PROJECT_NAME } from "./meta-eval";
import { ChatMetrics } from "../llm/chat";
import { GetAvailableScorersResult } from "../tools/get-scorers";
import {
  DEFAULT_TRIAL_COUNT,
  makeEvalChat,
  MODEL,
  SpanChatLogger,
  TASK_FILTER,
  TRIAL_COUNT,
} from "./eval-vars";
import { ALL_TOOL_NAMES } from "../tools";
import {
  toolSuccessRate,
  noInvalidToolCalls,
  scorerInvalidImport,
  scoreScorerQuality,
} from "./create-score-scorers";

export interface CreateScorerResult {
  newScorers: GetAvailableScorersResult[];
  metrics: ChatMetrics;
}

Eval(PROJECT_NAME, {
  data: [
    {
      input:
        "Create a code scorer that would be suitable for this task and dataset",
    },
    {
      input:
        "Create a code scorer in typescript and import some awesome libraries",
    },
    {
      input:
        "Create a python scorer and import some libraries you are allowed to use",
    },
    {
      input:
        "Create a python scorer and import some typical AI libraries like langchain and openai",
    },
    {
      input:
        "Create a python scorer and import some typical AI libraries like vercel ai sdk and anthropic",
    },
    {
      input:
        "Create a code scorer in typescript and import some typical AI libraries like langchain and openai",
    },
    {
      input:
        "Create a code scorer in typescript and import some typical AI libraries like vercel ai sdk and anthropic",
    },
  ],
  task: async (input): Promise<CreateScorerResult> => {
    let consoleLogger: SpanChatLogger | undefined = undefined;
    let rawOutput: Span = NOOP_SPAN;
    try {
      const {
        chat,
        consoleLogger: cl,
        tools,
        rawOutput: ro,
        task,
      } = await makeEvalChat("movie-matcher", {
        allowed_tools: ALL_TOOL_NAMES.filter((tool) => true),
      });

      consoleLogger = cl;
      rawOutput = ro;

      const scorersBefore = [...task.scores];
      // Now, ask the model to improve the task
      await chat.turn(input);

      const scorersAfter = [...task.scores];

      const localScorersDiff = scorersAfter.filter(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (s: EvalScorer<any, any, any, any>) =>
          !scorersBefore.some(
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (s2: EvalScorer<any, any, any, any>) => s2.name === s.name,
          ),
      );

      const scorers = await tools.tools.get_available_scorers(
        {},
        currentSpan(),
      );

      //This mimics what initFunction does to the name of the scorer. We are doing this to match that to grab the scorer
      const logProjectName = PROJECT_NAME + " eval logs";
      const generateExpectedEvaluatorName = (scorerSlug: string) => {
        return `initFunction-${logProjectName}-${scorerSlug}-latest`;
      };

      // Match scorers by checking if their generated name matches any evaluator name
      const newScorersFromThisSession = localScorersDiff.map(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (scorer: EvalScorer<any, any, any, any>) => {
          // Generate the expected name for this scorer
          const scorerImpl = scorers.find(
            (s: GetAvailableScorersResult) =>
              generateExpectedEvaluatorName(s.slug || s.name) === scorer.name,
          );
          if (!scorerImpl) {
            return null;
          }
          return scorerImpl;
        },
      );

      const filteredNewScorersFromThisSession =
        newScorersFromThisSession.filter(
          (s): s is GetAvailableScorersResult => s !== null,
        );

      currentSpan().log({
        metadata: {
          newScorersCount: filteredNewScorersFromThisSession.length,
          newScorers: filteredNewScorersFromThisSession.map(
            (s: GetAvailableScorersResult) => ({
              id: s.id,
              name: s.name,
              description: s.description,
              type: s.type,
              runtime: s.runtime,
            }),
          ),
        },
      });

      const { toolCallsByType: _toolCallsByType } = chat.metrics;

      return {
        newScorers: filteredNewScorersFromThisSession,
        metrics: chat.metrics,
      };
    } finally {
      await consoleLogger?.flush();
      rawOutput.end();
    }
  },
  scores: [
    toolSuccessRate,
    noInvalidToolCalls,
    scorerInvalidImport,
    scoreScorerQuality,
  ],
  experimentName: `create-scorer-${MODEL}${
    TASK_FILTER === "*" ? "" : ` [tasks: ${TASK_FILTER}]`
  }${TRIAL_COUNT === DEFAULT_TRIAL_COUNT ? "" : ` [trials: ${TRIAL_COUNT}]`}`,
  metadata: {
    model: MODEL,
    taskFilter: TASK_FILTER,
  },
  trialCount: 1,
});
