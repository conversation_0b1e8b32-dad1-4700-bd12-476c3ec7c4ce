import { Score, LLMClassifierFromTemplate } from "autoevals";
import { GetResultsToolResult } from "../tools";
import { CreateScorerResult } from "./create-code-scorer.eval";

export function toolSuccessRate({
  output,
}: {
  output: CreateScorerResult;
}): Score {
  return {
    name: "tool_success_rate",
    score:
      output.metrics.toolCalls > 0
        ? output.metrics.completed / output.metrics.toolCalls
        : null,
    metadata: {
      metrics: output.metrics,
    },
  };
}

export function noInvalidToolCalls({
  output: { metrics },
}: {
  output: CreateScorerResult;
}): Score {
  return {
    name: "no_invalid_tool_calls",
    score: metrics.invalidToolCalls === 0 ? 1 : 0,
    metadata: {
      metrics,
    },
  };
}

//You want the model to use get_results, get_summary, and get_available_scorers before creating a scorer
export function scoreToolUsage({
  output,
}: {
  output: {
    synthetic_dataset: GetResultsToolResult[];
    initial_dataset: GetResultsToolResult[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    metrics: any;
    codeScorer: boolean;
  };
}): Score {
  const editDataCalls = output.metrics?.toolCallsByType?.edit_data;
  const getResultsCalls = output.metrics?.toolCallsByType?.get_results;
  const getSummaryCalls = output.metrics?.toolCallsByType?.get_summary;
  const getAvailableScorersCalls =
    output.metrics?.toolCallsByType?.get_available_scorers;

  // Calculate expected edit_data calls based on batches of 10
  const syntheticCount = output.synthetic_dataset?.length || 0;
  const expectedEditCalls = Math.ceil(syntheticCount / 10);

  // Check if all required tools were called before creating scorers
  const requiredToolsUsed =
    getResultsCalls >= 1 &&
    getSummaryCalls >= 1 &&
    getAvailableScorersCalls >= 1;

  // Perfect score if all required tools are used and edit_data calls are correct
  const score =
    requiredToolsUsed && editDataCalls === expectedEditCalls
      ? 1
      : requiredToolsUsed &&
          (editDataCalls > expectedEditCalls ||
            editDataCalls < expectedEditCalls)
        ? 0.7 // Good if required tools used but edit_data calls not optimal
        : !requiredToolsUsed && editDataCalls === expectedEditCalls
          ? 0.5 // Missing required tool calls
          : 0;

  return {
    name: "tool_usage_efficiency",
    score: score,
    metadata: {
      editDataCalls,
      idealEditCalls: expectedEditCalls,
      getResultsCalls,
      idealGetResultsCalls: 1,
      getSummaryCalls,
      idealGetSummaryCalls: 1,
      getAvailableScorersCalls,
      idealGetAvailableScorersCalls: 1,
      batchSize: 10,
      requiredToolsUsed,
    },
  };
}

export async function scoreScorerQuality({
  output,
}: {
  output: CreateScorerResult;
}): Promise<Score> {
  // Check if any scorers were created
  if (!output.newScorers || output.newScorers.length === 0) {
    return {
      name: "scorer_quality",
      score: 0,
      error: "No scorers were created",
    };
  }

  // Get the first scorer (typically there's only one)
  const scorer = output.newScorers[0];

  // Format scorer information for evaluation
  const scorerInfo = {
    name: scorer.name,
    description: scorer.description,
    type: scorer.type || "unknown",
    runtime: scorer.runtime || "N/A",
    hasCode: !!scorer.code,
    hasPrompt: !!scorer.prompt,
    codeLength: scorer.code?.length || 0,
  };

  // If it's a code-based scorer, include the code for evaluation
  const formattedOutput = scorer.code
    ? `Scorer Name: ${scorerInfo.name}
Description: ${scorerInfo.description}
Type: ${scorerInfo.type}
Runtime: ${scorerInfo.runtime}

Code Implementation:
\`\`\`${scorerInfo.runtime}
${scorer.code}
\`\`\`
`
    : `Scorer Name: ${scorerInfo.name}
Description: ${scorerInfo.description}
Type: ${scorerInfo.type}
${scorer.prompt ? "This is an LLM-based scorer with a prompt template." : "No implementation details available."}
`;

  return await ScorerQualityClassifier({
    output: formattedOutput,
    expected: "", // Not used in this context
  });
}

const ScorerQualityClassifier = LLMClassifierFromTemplate({
  name: "Scorer Quality Classifier",
  model: "claude-3-5-sonnet-latest",
  promptTemplate: `
You are an expert code reviewer evaluating the quality of a newly created scorer function for an evaluation framework.

You will receive information about a scorer including its name, description, type, and if it's a code-based scorer, its implementation.

Evaluate the scorer based on these criteria:

**For Code-Based Scorers:**
1. **Correctness**: Does the code correctly implement what the description promises? Are there any logical errors?
2. **Robustness**: Does it handle edge cases (null values, empty inputs, type mismatches)?
3. **Clarity**: Is the code readable and well-structured? Are variable names meaningful?
4. **Efficiency**: Is the implementation reasonably efficient without unnecessary complexity?
5. **Return Value**: Does it properly return a Score object with name, score, and appropriate metadata?

**For LLM-Based Scorers:**
1. **Alignment**: Does the scorer name and description clearly indicate what it evaluates?
2. **Appropriateness**: Is an LLM judge appropriate for this type of evaluation?

**General Criteria:**
1. **Naming**: Is the scorer name descriptive and follows conventions?
2. **Description**: Is the description clear and accurately reflects what the scorer does?

After reviewing the scorer, pick ONE overall rating:
(a) **Excellent** - Well-implemented, robust, clear, and production-ready
(b) **Good** - Solid implementation with minor improvements possible
(c) **Fair** - Functional but has notable issues or missing important edge cases
(d) **Poor** - Significant problems with correctness, robustness, or clarity

Return ONLY the letter **a**, **b**, **c**, or **d**.

Here is the scorer to evaluate:

{{{output}}}
`,
  choiceScores: {
    a: 1.0,
    b: 0.75,
    c: 0.5,
    d: 0.25,
  },
});

// Helper function to check if a module is a Python standard library module
function isPythonStandardLibrary(module: string): boolean {
  // Comprehensive list of Python standard library modules
  const pythonStandardLibraries = new Set([
    // Core built-in modules
    "__builtin__",
    "__future__",
    "__main__",
    "builtins",

    // Text processing
    "string",
    "re",
    "difflib",
    "textwrap",
    "unicodedata",
    "stringprep",
    "readline",
    "rlcompleter",

    // Binary data services
    "struct",
    "codecs",

    // Data types
    "datetime",
    "zoneinfo",
    "calendar",
    "collections",
    "heapq",
    "bisect",
    "array",
    "weakref",
    "types",
    "copy",
    "pprint",
    "reprlib",
    "enum",
    "graphlib",

    // Numeric and mathematical
    "numbers",
    "math",
    "cmath",
    "decimal",
    "fractions",
    "random",
    "statistics",

    // Functional programming
    "itertools",
    "functools",
    "operator",

    // File and directory access
    "pathlib",
    "os",
    "fileinput",
    "stat",
    "filecmp",
    "tempfile",
    "glob",
    "fnmatch",
    "linecache",
    "shutil",

    // Data persistence
    "pickle",
    "copyreg",
    "shelve",
    "marshal",
    "dbm",
    "sqlite3",

    // Data compression and archiving
    "zlib",
    "gzip",
    "bz2",
    "lzma",
    "zipfile",
    "tarfile",

    // File formats
    "csv",
    "configparser",
    "tomllib",
    "netrc",
    "plistlib",

    // Cryptographic services
    "hashlib",
    "hmac",
    "secrets",

    // Generic OS services
    "os",
    "io",
    "time",
    "argparse",
    "getopt",
    "logging",
    "getpass",
    "curses",
    "platform",
    "errno",
    "ctypes",

    // Concurrent execution
    "threading",
    "multiprocessing",
    "concurrent",
    "subprocess",
    "sched",
    "queue",
    "_thread",
    "_dummy_thread",
    "dummy_threading",

    // Contextvars
    "contextvars",

    // Networking and interprocess
    "asyncio",
    "socket",
    "ssl",
    "select",
    "selectors",
    "signal",
    "mmap",

    // Internet data handling
    "email",
    "json",
    "mailbox",
    "mimetypes",
    "base64",
    "binascii",
    "quopri",

    // Structured markup
    "html",
    "xml",

    // Internet protocols
    "webbrowser",
    "wsgiref",
    "urllib",
    "http",
    "ftplib",
    "poplib",
    "imaplib",
    "smtplib",
    "uuid",
    "socketserver",
    "xmlrpc",
    "ipaddress",

    // Multimedia
    "wave",
    "colorsys",

    // Internationalization
    "gettext",
    "locale",

    // Program frameworks
    "cmd",
    "shlex",

    // Development tools
    "typing",
    "pydoc",
    "doctest",
    "unittest",
    "test",
    "bdb",
    "faulthandler",
    "pdb",
    "timeit",
    "trace",
    "traceback",
    "tracemalloc",

    // Debugging and profiling
    "cProfile",
    "profile",
    "pstats",

    // Software packaging
    "ensurepip",
    "venv",
    "zipapp",

    // Python runtime
    "sys",
    "sysconfig",
    "__main__",
    "warnings",
    "dataclasses",
    "contextlib",
    "abc",
    "atexit",
    "traceback",
    "gc",
    "inspect",
    "site",
    "code",
    "codeop",

    // Custom interpreters
    "ast",
    "symtable",
    "token",
    "keyword",
    "tokenize",
    "tabnanny",
    "pyclbr",
    "py_compile",
    "compileall",
    "dis",
    "pickletools",

    // Import related
    "importlib",
    "pkgutil",
    "modulefinder",
    "runpy",
    "imp",

    // Python language
    "parser",
    "nis",
    "fcntl",
    "resource",
    "grp",
    "crypt",
    "termios",
    "tty",
    "pty",
    "pwd",
    "spwd",
    "syslog",
    "posix",

    // Windows specific
    "msilib",
    "msvcrt",
    "winreg",
    "winsound",

    // Unix specific
    "posix",
    "pwd",
    "grp",
    "termios",
    "tty",
    "pty",
    "fcntl",
    "resource",
    "syslog",

    // Misc
    "formatter",
    "tabnanny",
    "pyclbr",
    "turtle",
    "turtledemo",
    "antigravity",
    "this",
    "nntplib",
    "nis",
  ]);

  return pythonStandardLibraries.has(module);
}

export function scorerInvalidImport({
  output,
}: {
  output: CreateScorerResult;
}): Score {
  // Check if any scorers were created
  if (!output.newScorers || output.newScorers.length === 0) {
    return {
      name: "no_invalid_imports",
      score: null,
      metadata: {
        reason: "No scorers were created",
      },
    };
  }

  const scorer = output.newScorers[0];
  const language = scorer.runtime === "node" ? "typescript" : "python";

  // Define language-specific allowed libraries
  const allowedLibrariesByLanguage = {
    typescript: new Set<string>([
      // TypeScript scorers CANNOT import any libraries at all
    ]),
    python: new Set([
      "autoevals",
      "braintrust",
      "openai",
      "anthropic",
      "requests",
    ]),
  };

  const allowedLibraries = allowedLibrariesByLanguage[language];

  const invalidImports: string[] = [];
  const allImports: string[] = [];

  // Check each scorer's code for imports
  for (const scorer of output.newScorers) {
    if (scorer.code) {
      // Language-specific import patterns
      const importPatterns =
        language === "typescript"
          ? [
              // ES6 imports: import ... from 'library'
              /import\s+(?:[\w\s{},*]+)\s+from\s+['"]([^'"]+)['"]/g,
              // CommonJS: require('library')
              /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
              // Dynamic imports: await import('library')
              /import\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
            ]
          : [
              // Python imports: import module
              /^import\s+([\w.]+)(?:\s+as\s+\w+)?$/gm,
              // Python from imports: from module import ...
              /^from\s+([\w.]+)\s+import\s+/gm,
              // Python __import__: __import__('module')
              /__import__\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
              // importlib: importlib.import_module('module')
              /importlib\.import_module\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
            ];

      for (const pattern of importPatterns) {
        let match;
        while ((match = pattern.exec(scorer.code)) !== null) {
          const library = match[1];
          allImports.push(library);

          let baseLibrary: string;
          let isRelativeImport: boolean;

          if (language === "typescript") {
            // Extract the base library name (handle scoped packages and sub-paths)
            baseLibrary = library.startsWith("@")
              ? library.split("/").slice(0, 2).join("/") // @scope/package
              : library.split("/")[0]; // regular package

            // Check if it's a relative import (starts with . or /)
            isRelativeImport =
              library.startsWith(".") || library.startsWith("/");
          } else {
            // Python: get the top-level module
            baseLibrary = library.split(".")[0];

            // Python relative imports start with .
            isRelativeImport = library.startsWith(".");
          }

          // Only check non-relative imports against the allowed list
          if (!isRelativeImport) {
            // TypeScript scorers cannot import ANY libraries
            if (language === "typescript") {
              invalidImports.push(library);
            } else {
              // Python: Check if it's an allowed external library or a standard library module
              if (
                !allowedLibraries.has(baseLibrary) &&
                !isPythonStandardLibrary(baseLibrary)
              ) {
                invalidImports.push(library);
              }
            }
          }
        }
      }
    }
  }

  const hasInvalidImports = invalidImports.length > 0;

  return {
    name: "no_invalid_imports",
    score: hasInvalidImports ? 0 : 1,
    metadata: {
      invalidImports,
      allImports,
      allowedLibraries:
        language === "typescript"
          ? "No imports allowed for TypeScript scorers"
          : {
              external: Array.from(allowedLibraries),
              note: "All Python standard library modules are also allowed",
            },
      scorersChecked: output.newScorers.length,
      language,
    },
  };
}

export function choiceScoreConciseness({
  output,
}: {
  output: CreateScorerResult;
}): Score {
  const choiceScores = output.newScorers.map(
    (scorer) => scorer.prompt?.parser?.choice_scores,
  );

  const choiceScoreKeys = choiceScores.map((cs) => (cs ? Object.keys(cs) : []));
  const allKeysAreOneChar = choiceScoreKeys.every((keys) =>
    keys.every((key) => key.length === 1),
  );

  return {
    name: "choice_score_conciseness",
    score: allKeysAreOneChar ? 1 : 0,
    metadata: {
      choiceScores,
      choiceScoreKeys,
      allKeysAreOneChar,
    },
  };
}

export async function LLMScorerQuality({
  output,
}: {
  output: CreateScorerResult;
}): Promise<Score> {
  // Check if any scorers were created
  if (!output.newScorers || output.newScorers.length === 0) {
    return {
      name: "llm_scorer_quality",
      score: 0,
      error: "No scorers were created",
    };
  }

  // Find LLM-based scorers (those with prompts)
  const llmScorers = output.newScorers.filter((scorer) => scorer.prompt);

  if (llmScorers.length === 0) {
    return {
      name: "llm_scorer_quality",
      score: null,
      metadata: {
        reason: "No LLM-based scorers found",
      },
    };
  }

  // Get the first LLM scorer for evaluation
  const scorer = llmScorers[0];

  // Format the prompt for evaluation
  const formattedOutput = `
LLM Scorer Name: ${scorer.name}
Description: ${scorer.description}

Prompt Configuration:
${JSON.stringify(scorer.prompt, null, 2)}
`;

  return await LLMScorerQualityJudge({
    output: formattedOutput,
    expected: "", // Not used in this context
  });
}

const LLMScorerQualityJudge = LLMClassifierFromTemplate({
  name: "LLM Scorer Quality Judge",
  model: "claude-3-5-sonnet-latest",
  promptTemplate: `
You are an expert in evaluating the quality of LLM-based scorer prompts for evaluation frameworks.

You will receive information about an LLM scorer including its name, description, prompt template, and analysis of key quality indicators.

Evaluate the LLM scorer based on these critical quality criteria:

1. **Few-Shot Examples** (Critical): Does the prompt include concrete examples showing the expected input/output format? Good prompts have 2-3 clear examples.

2. **Clear Evaluation Criteria** (Critical): Are the evaluation criteria explicitly stated? The prompt should clearly define what aspects to evaluate and how to judge them.

3. **Output Format Instructions** (Important): Does the prompt specify exactly how to format the response? This ensures consistent, parseable outputs.

4. **Scoring Rubric/Scale** (Important): Is there a clear scoring scale or rubric? For choice-based scorers, are the choices well-defined with appropriate scores?

5. **Context and Purpose** (Important): Does the prompt clearly explain the task context and what the scorer is trying to measure?

6. **Edge Case Handling** (Good to have): Does the prompt address how to handle edge cases, missing data, or ambiguous inputs?

7. **Conciseness vs Completeness**: Is the prompt comprehensive enough without being overly verbose? (Ideal length: 500-2000 characters for most tasks)

After reviewing the LLM scorer, pick ONE overall rating:

(A) **Excellent** - Has few-shot examples, clear criteria, proper output format, and scoring rubric. Production-ready.
(B) **Good** - Has most critical elements (criteria and output format) but may lack examples or rubric.
(C) **Fair** - Has some important elements but missing critical components like clear criteria or examples.
(D) **Poor** - Lacks most quality indicators, vague criteria, no examples, unclear output format.

Focus especially on whether the prompt has:
- At least 1-2 few-shot examples
- Explicitly stated evaluation criteria
- Clear instructions on how to score/rate

Return ONLY the letter **A**, **B**, **C**, or **D**.

Here is the LLM scorer to evaluate:

{{{output}}}
`,
  choiceScores: {
    A: 1.0,
    B: 0.75,
    C: 0.5,
    D: 0.25,
  },
});
