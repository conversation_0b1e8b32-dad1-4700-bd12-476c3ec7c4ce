import { Tool } from "./types";
import { z } from "zod";

export const PLACEHOLDER_TS = `// Enter handler function that returns a numeric score between 0 and 1,
// or null to skip scoring
function handler({
  input,
  output,
  expected,
  metadata,
}: {
  input: any;
  output: any;
  expected: any;
  metadata: Record<string, any>;
}): {score: number | null, name: string, metadata: Record<string, any>} {
  return {
    score: expected === null ? null : output === expected ? 1 : 0,
    name: "My scorer",
    metadata: {
      stringEquality: output.toString() === expected.toString(),
    },
  };
}`;

export const PLACEHOLDER_PY = `from typing import Any

# Enter handler function that returns a numeric score between 0 and 1,
# or None to skip scoring
def handler(input: Any, output: Any, expected: Any, metadata: dict[str, Any]):
    return {
        "score": 1.0 if output == expected else 0.0 if expected is not None else None,
        "name": "My scorer",
        "metadata": {
            "stringEquality": str(output) == str(expected),
        },
    }`;

export const createCodeScorerToolResultSchema = z.object({
  ok: z.boolean(),
});
export type CreateCodeScorerToolResult = z.infer<
  typeof createCodeScorerToolResultSchema
>;

export const createCodeScorerParamsSchema = z.object({
  id: z
    .string()
    .optional()
    .describe("Optionally specify the id of an existing scorer to edit it"),
  name: z.string().describe("The name of the scorer."),
  description: z.string().describe("The description of the scorer."),
  runtime: z.enum(["typescript", "python"]),
  code: z.string().describe("The code of the scorer."),
});
export type CreateCodeScorerParams = z.infer<
  typeof createCodeScorerParamsSchema
>;

export const CreateCodeScorerTool: Tool<
  CreateCodeScorerParams,
  CreateCodeScorerToolResult
> = {
  parameters: createCodeScorerParamsSchema,
  description: `Create a new, or edit an existing, scorer written in code. Code scorers are useful for tasks that
require heuristic checks (like comparing strings or validating JSON data), or for very complex
LLM-based scorers that require creating dynamic prompts and parsing them (in which case, you should
use the openai library in Python). You should only create scorers if explicitly asked by the user or
the functionality is not already covered by an existing scorer from get_available_scorers().

DO NOT try to create a code scorer without a very precise understanding of the shape of the user's data. Use
the get_results tool to get a sample of the data.

You can either write a scorer in Typescript or Python. A scorer is a function that takes input, output, expected,
and metadata as arguments, and returns a score object which contains a name (should be the same as the function
name), metadata (which can contain information useful for debugging), and a score, which is a number between 0 and 1.

Here are some examples of scorers. The function must always be named "handler".

\`\`\`typescript
${PLACEHOLDER_TS}
\`\`\`

and an example Python function:

\`\`\`python
${PLACEHOLDER_PY}
\`\`\`

Here are some rules for writing scorers:
- It is generally better to write a concise scorer that is simpler rather than overly complex and comprehensive. A user will
ask you if they need it to cover more cases or add more complex logic.
- If you are unsure of the shape of the data, use the get_results tool to get a sample of the data.
- In Typescript, you cannot import any libraries. In Python, you can import: openai, anthropic, requests, and any of the
built-in libraries.
- If you are editing an existing scorer, use the get_available_scorers tool to get the id of the scorer you want to edit.`,
};

export const PLACEHOLDER_LLM = `{
  "prompt": {
    "type": "chat",
    "messages": [
      {
        "content": "You are assessing a submitted answer on a given task based on a criterion. Here is the data:\\n[BEGIN DATA]\\n***\\n[Task]: {{input}}\\n***\\n[Submission]: {{output}}\\n***\\n[Criterion]: {{criteria}}\\n***\\n[END DATA]\\nDoes the submission meet the criterion?
        The choice scores are:
(a) **Yes** - this answer meets the criterion
(b) **No** - this answer does not meet the criterion

Return ONLY the letter **A** or **B**.",
        "role": "user"
      }
    ],
    "tools": ""
  },
  "options": {
    "model": "claude-3-5-sonnet-latest"
  },
  "parser": {
    "type": "llm_classifier",
    "use_cot": true,
    "choice_scores": {
      "a": 0,
      "b": 1
    }
  }
}`;

export const createLLMScorerToolResultSchema = z.object({
  ok: z.boolean(),
});
export type CreateLLMScorerToolResult = z.infer<
  typeof createLLMScorerToolResultSchema
>;

export const createLLMScorerParamsSchema = z.object({
  id: z
    .string()
    .optional()
    .describe("Optionally specify the id of an existing scorer to edit it"),
  name: z.string().describe("The name of the scorer."),
  description: z.string().describe("The description of the scorer."),
  prompt: z
    .string()
    .describe(
      "The evaluation prompt content (will be wrapped as a user message in chat format)",
    ),
  model: z.string().describe("The model to use for the scorer"),
  params: z
    .object({
      temperature: z.number().min(0).max(2).optional(),
      max_tokens: z.number().positive().optional(),
      top_p: z.number().min(0).max(1).optional(),
    })
    .passthrough()
    .optional()
    .describe("Model parameters"),
  use_cot: z.boolean().describe("Whether to use chain-of-thought reasoning"),
  choice_scores: z
    .record(z.string(), z.number().min(0).max(1))
    .describe("Mapping of choices to scores (e.g., {'Yes': 1, 'No': 0})"),
});

export type CreateLLMScorerParams = z.infer<typeof createLLMScorerParamsSchema>;

export const CreateLLMScorerTool: Tool<
  CreateLLMScorerParams,
  CreateLLMScorerToolResult
> = {
  parameters: createLLMScorerParamsSchema,
  description: `Create a new, or edit an existing, LLM as a judge scorer. LLM as a judge scorers are useful for tasks that are hard to codify in code. They are useful for tasks that require holistic evaluation,
domain-specific expertise, complex semantic understanding, subjective judgment and/or nuanced reasoning.

Here is an example of a LLM as a judge scorer:

\`\`\`json
${PLACEHOLDER_LLM}
\`\`\`

### Rules
- prefer choice_scores to be at most one letter long to discourage semantic bias and increase consistency.
  Good examples:
   {
    "a": 0,
    "b": 1
   },
   {
    "a": 0,
    "b": 0.25
    "c": 0.5
    "d": 0.75
    "e": 1
   }
  Bad examples:
   {
   "yes": 0,
   "no": 1
    },
    {
    "a": 0,
    "b": .25
    "c": 0.5
    "d": 0.75
    "e": 1
   }

  - Use get_results to understand the shape of the data you'll be scoring
- Before writing a LLM as a judge scorer, use the get_available_scorers tool to understand existing scorer patterns users use and to also see if there is an existing scorer that can be used or edited
- Keep scorers focused on a single evaluation criterion
- It is generally better to write a concise scorer that is simpler rather than overly complex and comprehensive. A user will ask you if they need it to cover more cases or add more complex evaluation criteria.
`,
};
