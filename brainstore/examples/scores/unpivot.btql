unpivot: scores AS (score, value) | select: score, value;
unpivot: scores AS (score, value) | select: score;
unpivot: scores AS (score, value) | select: value;
unpivot: scores AS (score, value) | select: id;

unpivot: scores AS (score, value) | dimensions: score | measures: sum(value);

unpivot: tags AS tag | select: tag;
unpivot: tags AS tag | dimensions: tag | measures: count(1);

unpivot: scores AS (score, value), tags AS tag | select: score, tag;
unpivot: scores AS (score, value), tags AS tag | select: score;
unpivot: scores AS (score, value), tags AS tag | dimensions: score, tag | measures: sum(value), count(1);

unpivot: scores AS (score, value) | measures: count(value);

-- Test exists optimization
unpivot: scores AS (score, value) | dimensions: score | measures: count(value);
