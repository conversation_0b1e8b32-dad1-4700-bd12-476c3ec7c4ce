[{"error": null, "query": "unpivot: scores AS (score, value) | select: score, value", "result_rows": [{"score": "bar", "value": 0.5}, {"score": "foo", "value": 0.25}, {"score": "foo", "value": 0}, {"score": "foo", "value": 1}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | select: score", "result_rows": [{"score": "bar"}, {"score": "foo"}, {"score": "foo"}, {"score": "foo"}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | select: value", "result_rows": [{"value": 0.25}, {"value": 0.5}, {"value": 0}, {"value": 1}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | select: id", "result_rows": [{"id": "asf-dfa"}, {"id": "dfa-asf"}, {"id": "uuid3"}, {"id": "uuid4"}, {"id": "uuid5"}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | dimensions: score | measures: sum(value)", "result_rows": [{"score": "bar", "sum(value)": 0.5}, {"score": "foo", "sum(value)": 1.25}], "skip": false}, {"error": null, "query": "unpivot: tags AS tag | select: tag", "result_rows": [{"tag": "a"}, {"tag": "a"}, {"tag": "b"}, {"tag": "c"}, {"tag": "d"}], "skip": false}, {"error": null, "query": "unpivot: tags AS tag | dimensions: tag | measures: count(1)", "result_rows": [{"count(1)": 1, "tag": "b"}, {"count(1)": 1, "tag": "c"}, {"count(1)": 1, "tag": "d"}, {"count(1)": 2, "tag": "a"}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value), tags AS tag | select: score, tag", "result_rows": [{"score": "bar", "tag": "a"}, {"score": "bar", "tag": "c"}, {"score": "foo", "tag": "a"}, {"score": "foo", "tag": "a"}, {"score": "foo", "tag": "b"}, {"score": "foo", "tag": "c"}, {"score": "foo", "tag": "d"}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value), tags AS tag | select: score", "result_rows": [{"score": "bar"}, {"score": "foo"}, {"score": "foo"}, {"score": "foo"}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value), tags AS tag | dimensions: score, tag | measures: sum(value), count(1)", "result_rows": [{"count(1)": 1, "score": "bar", "sum(value)": 0.5, "tag": "a"}, {"count(1)": 1, "score": "bar", "sum(value)": 0.5, "tag": "c"}, {"count(1)": 1, "score": "foo", "sum(value)": 0, "tag": "c"}, {"count(1)": 1, "score": "foo", "sum(value)": 0.25, "tag": "d"}, {"count(1)": 1, "score": "foo", "sum(value)": 1, "tag": "b"}, {"count(1)": 2, "score": "foo", "sum(value)": 1, "tag": "a"}], "skip": false}, {"error": null, "query": "unpivot: scores AS (score, value) | measures: count(value)", "result_rows": [{"count(value)": 4}], "skip": false}, {"error": null, "query": "-- Test exists optimization\nunpivot: scores AS (score, value) | dimensions: score | measures: count(value)", "result_rows": [{"count(value)": 1, "score": "bar"}, {"count(value)": 3, "score": "foo"}], "skip": false}]