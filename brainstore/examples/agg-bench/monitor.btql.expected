[{"error": null, "query": "-- token query\n/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0) AS prompt_tokens, sum(metrics.prompt_cached_tokens) AS prompt_cached_tokens, sum(metrics.prompt_cache_creation_tokens) AS prompt_cache_creation_tokens, sum(metrics.completion_tokens) AS completion_tokens, count(1) AS count", "result_rows": [{"completion_tokens": 513, "count": 3, "prompt_tokens": 1432, "time": "2025-07-31T00:00:00Z"}, {"completion_tokens": 692, "count": 4, "prompt_tokens": 1282, "time": "2025-07-29T00:00:00Z"}, {"completion_tokens": 739, "count": 2, "prompt_tokens": 239, "time": "2025-08-04T00:00:00Z"}, {"count": 1, "time": "2025-08-03T00:00:00Z"}], "skip": false}, {"error": null, "query": "-- cost query\n/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time, metadata.model AS model | measures: sum(metrics.prompt_tokens) - COALESCE(sum(metrics.prompt_cached_tokens), 0) - COALESCE(sum(metrics.prompt_cache_creation_tokens), 0) AS prompt_tokens, sum(metrics.prompt_cached_tokens) AS prompt_cached_tokens, sum(metrics.prompt_cache_creation_tokens) AS prompt_cache_creation_tokens, sum(metrics.completion_tokens) AS completion_tokens, count(1) AS count", "result_rows": [{"completion_tokens": 155, "count": 2, "model": "gpt-3.5-turbo", "prompt_tokens": 803, "time": "2025-07-31T00:00:00Z"}, {"completion_tokens": 358, "count": 1, "model": "gpt-4", "prompt_tokens": 629, "time": "2025-07-31T00:00:00Z"}, {"completion_tokens": 692, "count": 1, "model": "gpt-3.5-turbo", "prompt_tokens": 1282, "time": "2025-07-29T00:00:00Z"}, {"completion_tokens": 739, "count": 1, "model": "gpt-3.5-turbo", "prompt_tokens": 239, "time": "2025-08-04T00:00:00Z"}, {"count": 1, "time": "2025-08-03T00:00:00Z"}, {"count": 1, "time": "2025-08-04T00:00:00Z"}, {"count": 3, "time": "2025-07-29T00:00:00Z"}], "skip": false}, {"error": null, "query": "-- latency query\n/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: sum(is_root) AS c, percentile(metrics.end-metrics.start, 0.5) AS p50, percentile(is_root ? metrics.end-metrics.start : null, 0.5) AS p50_root, percentile(is_root ? metrics.end-metrics.start : null, 0.95) AS p95_root", "result_rows": [{"c": 0, "p50": 1.665236, "time": "2025-07-31T00:00:00Z"}, {"c": 1, "p50": 2.203344, "time": "2025-08-04T00:00:00Z"}, {"c": 1, "time": "2025-08-03T00:00:00Z"}, {"c": 3, "p50": 0.612586, "time": "2025-07-29T00:00:00Z"}], "skip": false}, {"error": null, "query": "-- ttft query\n/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: percentile(metrics.time_to_first_token, 0.5) AS p50_time_to_first_token, percentile(metrics.time_to_first_token, 0.95) AS p95_time_to_first_token", "result_rows": [{"time": "2025-07-29T00:00:00Z"}, {"time": "2025-07-31T00:00:00Z"}, {"time": "2025-08-03T00:00:00Z"}, {"time": "2025-08-04T00:00:00Z"}], "skip": false}, {"error": null, "query": "-- request count query\n/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: sum(is_root) AS count, sum(is_root ? 1 : 0) as ternary_count, count(1) AS spans, sum(span_attributes.type = 'llm' ? 1 : 0) AS llm_spans, sum(span_attributes.type = 'tool' ? 1 : 0) AS tool_count", "result_rows": [{"count": 0, "llm_spans": 0, "spans": 3, "ternary_count": 0, "time": "2025-07-31T00:00:00Z", "tool_count": 0}, {"count": 1, "llm_spans": 0, "spans": 1, "ternary_count": 1, "time": "2025-08-03T00:00:00Z", "tool_count": 0}, {"count": 1, "llm_spans": 0, "spans": 2, "ternary_count": 1, "time": "2025-08-04T00:00:00Z", "tool_count": 0}, {"count": 3, "llm_spans": 0, "spans": 4, "ternary_count": 3, "time": "2025-07-29T00:00:00Z", "tool_count": 0}], "skip": false}]