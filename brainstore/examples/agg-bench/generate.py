#!/usr/bin/env python3

import argparse
import json
import random
import shutil
import subprocess
import time
import uuid
from datetime import datetime, timedelta
from pathlib import Path

_XACT_ID = 100
_ROOT_SPAN_IDS = []  # Keep track of root span IDs for child spans


def generate_row(include_xact_id, force_root=False, parent_span_id=None):
    """Generate a single row with id, created timestamp, and is_root flag"""
    global _XACT_ID
    # Generate timestamps for metrics
    start_time = time.time() - random.uniform(0, 10)  # Start 0-10 seconds ago
    end_time = start_time + random.uniform(0.1, 5.0)  # Duration 0.1-5 seconds

    # List of popular LLM models with weighted distribution
    models = [
        "gpt-4",
        "gpt-3.5-turbo",
        "claude-3-opus",
        "claude-3-sonnet",
        "claude-3-haiku",
        "llama-3-70b",
        "mixtral-8x7b",
    ]

    # Make gpt-3.5-turbo much more popular (60% of the time)
    def choose_model():
        if random.random() < 0.6:
            return "gpt-3.5-turbo"
        else:
            # Choose randomly from all models for the remaining 40%
            return random.choice(models)

    # Generate span_id
    span_id = str(uuid.uuid4())

    # Determine if this is a root span
    is_root_span = force_root or (parent_span_id is None)

    # Available tags
    all_tags = ["production", "testing", "evaluation", "experiment"]

    error = None
    error_rand = random.random()
    if error_rand < 0.1:  # 10% chance of string error
        error = "error!"
    elif error_rand < 0.2:  # 10% chance of object error
        error = {"error": "error!", "stack": "stack!"}

    # Base structure common to all spans
    ret = {
        "id": str(uuid.uuid4()),
        "span_id": span_id,
        "created": (datetime.now() - timedelta(days=random.uniform(0, 7))).isoformat() + "Z",
        "is_root": is_root_span,  # Set is_root based on whether this is actually a root span
        "experiment_id": "singleton",
        "error": error,
    }

    if is_root_span:
        # Root spans get root_span_id set to their own span_id and tags
        ret["root_span_id"] = span_id
        # Select 1-3 random tags for root spans only
        num_tags = random.randint(1, 3)
        selected_tags = random.sample(all_tags, num_tags)
        ret["tags"] = selected_tags

        # Root spans get different metrics (not LLM-related)
        ret["metrics"] = {
            "duration_ms": (end_time - start_time) * 1000,
            "request_count": random.randint(1, 100),
            "success_rate": random.uniform(0.8, 1.0),
            "latency_p50": random.uniform(10, 100),
            "latency_p99": random.uniform(100, 1000),
        }

        # Root spans get different metadata (not LLM-related)
        ret["metadata"] = {
            "service": random.choice(["api-gateway", "auth-service", "data-processor", "workflow-engine"]),
            "environment": random.choice(["production", "staging", "development"]),
            "version": f"v{random.randint(1, 3)}.{random.randint(0, 9)}.{random.randint(0, 20)}",
            "region": random.choice(["us-east-1", "us-west-2", "eu-west-1", "ap-southeast-1"]),
        }

    else:
        # Child spans are LLM spans
        if parent_span_id:
            ret["span_parents"] = [parent_span_id]
            ret["root_span_id"] = parent_span_id
            # NOTE: Child spans should NOT have root_span_id - they inherit it from their parent

            # Generate token counts for LLM spans
            prompt_tokens = random.randint(50, 2000)
            completion_tokens = random.randint(20, 1000)
            total_tokens = prompt_tokens + completion_tokens

            # LLM spans get token metrics
            ret["metrics"] = {
                "tokens": total_tokens,
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
                "start": start_time,
                "end": end_time,
            }

            # LLM spans get model metadata
            ret["metadata"] = {
                "model": choose_model(),
                "request_id": str(uuid.uuid4()),
                "temperature": random.uniform(0.0, 2.0),
                "max_tokens": random.choice([100, 500, 1000, 2000, 4000]),
            }

    if include_xact_id:
        ret["_xact_id"] = _XACT_ID
        _XACT_ID += 1

    return ret


def generate_files(num_files, rows_per_file, output_dir="data"):
    """Generate N files with M rows each as JSONL files"""
    global _ROOT_SPAN_IDS

    # Create output directory if it doesn't exist
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    print(f"Generating {num_files} files with {rows_per_file} rows each...")

    for file_idx in range(num_files):
        filename = f"data_{file_idx:04d}.jsonl"
        filepath = output_path / filename

        # Reset root span IDs for each file to ensure good distribution
        _ROOT_SPAN_IDS = []

        with filepath.open("w") as f:
            row_count = 0
            while row_count < rows_per_file:
                # Generate a root span
                root = generate_row(include_xact_id=False, force_root=True)
                f.write(json.dumps(root) + "\n")
                row_count += 1

                # Generate 1-2 children for this root
                num_children = min(random.randint(1, 2), rows_per_file - row_count)
                for _ in range(num_children):
                    child = generate_row(include_xact_id=False, force_root=False, parent_span_id=root["span_id"])
                    f.write(json.dumps(child) + "\n")
                    row_count += 1

        print(f"Created {filepath}")

    print(f"Done! Generated {num_files} files with {rows_per_file} rows each in {output_dir}/")


def main():
    parser = argparse.ArgumentParser(description="Generate N JSONL files with M rows each for Braintrust data testing")
    parser.add_argument("-n", "--num-files", type=int, default=4000, help="Number of files to generate")
    parser.add_argument("-m", "--rows-per-file", type=int, default=1000, help="Number of rows per file")
    parser.add_argument("-o", "--output-dir", type=str, default="data", help="Output directory (default: data)")
    parser.add_argument("--test", action="store_true", help="Generate only 10 test events and save to events.jsonl")

    args = parser.parse_args()

    if args.test:
        global _ROOT_SPAN_IDS
        # Reset root span IDs for test generation
        _ROOT_SPAN_IDS = []
        # Generate test events in streaming fashion
        with open("events.jsonl", "w") as f:
            event_count = 0
            while event_count < 10:
                # Generate a root span
                root = generate_row(include_xact_id=True, force_root=True)
                f.write(json.dumps(root) + "\n")
                event_count += 1

                # Generate 1-2 children for this root
                num_children = min(random.randint(1, 2), 10 - event_count)
                for _ in range(num_children):
                    child = generate_row(include_xact_id=True, force_root=False, parent_span_id=root["span_id"])
                    f.write(json.dumps(child) + "\n")
                    event_count += 1
        print("Generated test events in events.jsonl")
        return

    try:
        shutil.rmtree(args.output_dir)
    except FileNotFoundError:
        pass

    generate_files(args.num_files, args.rows_per_file, args.output_dir)

    try:
        shutil.rmtree("index")
    except FileNotFoundError:
        pass

    # cargo run --release --bin brainstore -- wal load data/* --normalize
    subprocess.run(
        "cargo run --release --bin brainstore -- wal insert -c bench.yaml data/* --normalize --default-object-id experiment:singleton",
        shell=True,
    )
    subprocess.run(
        "cargo run --release --bin brainstore -- wal process -c bench.yaml experiment:singleton", shell=True
    )

    # Open the global store data and gut out the row id index to make the queries faster
    with open("index/bench/metadata/global_store_data", "r") as f:
        d = json.load(f)

    d["row_id_to_segment_id"] = {}
    d["root_span_id_to_segment_id"] = {}

    with open("index/bench/metadata/global_store_data", "w") as f:
        json.dump(d, f)

    subprocess.run("cargo run --release --bin brainstore -- wal compact -c bench.yaml --all", shell=True)
    subprocess.run("cargo run --release --bin brainstore -- index merge -c bench.yaml --all", shell=True)


if __name__ == "__main__":
    main()
