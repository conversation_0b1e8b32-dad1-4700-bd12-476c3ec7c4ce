[{"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | measures: count(1)", "result_rows": [{"count(1)": 10}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | measures: count(is_root)", "result_rows": [{"count(is_root)": 10}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) | measures: count(1)", "result_rows": [{"count(1)": 1, "day(created)": "2025-08-03T00:00:00Z"}, {"count(1)": 2, "day(created)": "2025-08-04T00:00:00Z"}, {"count(1)": 3, "day(created)": "2025-07-31T00:00:00Z"}, {"count(1)": 4, "day(created)": "2025-07-29T00:00:00Z"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) | measures: count(1) | filter: created >= '2025-07-10'", "result_rows": [{"count(1)": 1, "day(created)": "2025-08-03T00:00:00Z"}, {"count(1)": 2, "day(created)": "2025-08-04T00:00:00Z"}, {"count(1)": 3, "day(created)": "2025-07-31T00:00:00Z"}, {"count(1)": 4, "day(created)": "2025-07-29T00:00:00Z"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: 'foo' as d | measures: count(1) AS c | sort: d", "result_rows": [{"c": 10, "d": "foo"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: 'foo' as d | measures: count(1) AS c, sum(is_root) AS s, avg(metrics.end-metrics.start) as duration | sort: d", "result_rows": [{"c": 10, "d": "foo", "duration": 1.927157, "s": 5}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: experiment_id as d | measures: count(1) AS c, sum(is_root) AS s, avg(metrics.end-metrics.start) as duration | sort: d", "result_rows": [{"c": 10, "d": "singleton", "duration": 1.927157, "s": 5}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: metrics.model as model | measures: count(1) AS c, sum(is_root) AS s, avg(metrics.end-metrics.start) as duration | sort: model", "result_rows": [{"c": 10, "duration": 1.927157, "s": 5}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: metadata.model as model | measures: count(error) AS c | sort: model", "result_rows": [{"c": 0, "model": "gpt-3.5-turbo"}, {"c": 0, "model": "gpt-4"}, {"c": 0}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: sum(metadata.model = 'claude-3-haiku' ? 1 : 0) AS haiku_spans", "result_rows": [{"haiku_spans": 0, "time": "2025-07-29T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-31T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-08-03T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-08-04T00:00:00Z"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\nfrom: experiment('singleton') | dimensions: day(created) AS time | measures: sum('claude-3-haiku' = metadata.model ? 1 : 0) AS haiku_spans", "result_rows": [{"haiku_spans": 0, "time": "2025-07-29T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-07-31T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-08-03T00:00:00Z"}, {"haiku_spans": 0, "time": "2025-08-04T00:00:00Z"}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\n-- Test a field that doesn't exist\nfrom: experiment('singleton') | dimensions: metadata.foo as model | measures: count(1) AS c | sort: model", "result_rows": [{"c": 10}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\n-- Test a field that doesn't exist\nfrom: experiment('singleton') | measures: sum(metrics.prompt_cache_creation_tokens + metadata.foo)", "result_rows": [null], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 1\n*/\n-- Test an object field. Since this falls back to dynamic group by, it should have a dynamic node\nfrom: experiment('singleton') | dimensions: metadata as model | measures: count(1) AS c", "result_rows": [{"c": 1, "model": {"environment": "development", "region": "us-east-1", "service": "auth-service", "version": "v1.9.7"}}, {"c": 1, "model": {"environment": "development", "region": "us-east-1", "service": "auth-service", "version": "v3.9.20"}}, {"c": 1, "model": {"environment": "development", "region": "us-west-2", "service": "auth-service", "version": "v3.8.0"}}, {"c": 1, "model": {"environment": "production", "region": "eu-west-1", "service": "auth-service", "version": "v1.9.20"}}, {"c": 1, "model": {"environment": "staging", "region": "us-west-2", "service": "api-gateway", "version": "v1.2.1"}}, {"c": 1, "model": {"max_tokens": 100, "model": "gpt-3.5-turbo", "request_id": "b18b7ab1-42c2-4b9f-a9f4-caae4b999ef2", "temperature": 1.407858}}, {"c": 1, "model": {"max_tokens": 1000, "model": "gpt-3.5-turbo", "request_id": "c98ba23e-905e-4bae-98a1-feeb3aa953fa", "temperature": 0.479226}}, {"c": 1, "model": {"max_tokens": 500, "model": "gpt-3.5-turbo", "request_id": "23afaeb3-d064-4b39-8ece-700f259dbf6e", "temperature": 0.094909}}, {"c": 1, "model": {"max_tokens": 500, "model": "gpt-3.5-turbo", "request_id": "3e379ef3-302d-4b6e-ad36-0ee8bbf1b6c3", "temperature": 1.439921}}, {"c": 1, "model": {"max_tokens": 500, "model": "gpt-4", "request_id": "616f1edf-81c0-49ae-8eb8-fb13609f30bc", "temperature": 0.033728}}], "skip": false}, {"error": null, "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\n-- Test pagination key\nmeasures: max(_pagination_key) | from: experiment(\"singleton\")", "result_rows": [{"max(_pagination_key)": "p00000000000007143433"}], "skip": false}, {"error": "btql bind failed: Error: Failed to parse query (expected 'dimensions, pivot, unpivot, measures, select, infer, filter, from, sort, limit, cursor, comparison_key, weighted_scores, custom_columns, preview_length') at line 5, col 1 at dimension near '\ndimension: _paginat'", "query": "/*!execution -- Expect no Dynamic segment collector nodes\n  [.. | objects | select(.name == \"Dynamic segment collector\")] | length == 0\n*/\n-- Test pagination key\ndimension: _pagination_key | measures: count(1) | from: experiment(\"singleton\") | sort: _pagination_key", "result_rows": [], "skip": false}]