-- No match
/*!result -- No match should be null
  .[0].s == -1
*/
measures: COALESCE(sum(is_root), -1) AS s | filter: input='foo' | from: experiment('singleton') traces;

-- This should return 4 rows
/*!result -- This should return 4 spans across 2 traces
  length == 4
 */
from: experiment('singleton') traces | filter: _xact_id=100 OR _xact_id=109 | select: id;

/*!result -- The count should corroborate that
  .[0].c == 2
*/
from: experiment('singleton') spans | filter: _xact_id=100 OR _xact_id=109 | measures: count(1) as c;

/*!result -- In traces mode, it should be 4 though
  .[0].c == 4
*/
from: experiment('singleton') traces | filter: _xact_id=100 OR _xact_id=109 | measures: count(1) as c;
