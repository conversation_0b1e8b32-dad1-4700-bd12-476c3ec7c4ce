use btql::binder::json_ast::UnpivotSourceType;
use btql::binder::types::weakest_scalar_type;
use lazy_static::lazy_static;
use std::borrow::Cow;
use std::collections::HashSet;
use std::ops::Bound;
use storage::process_wal::{PAGINATION_KEY_FIELD, XACT_ID_FIELD};
use util::xact::PaginationKey;
use util::Value;

use crate::interpreter::tantivy::expand::{ID_FIELD, IS_ROOT_FIELD, ROOT_SPAN_ID_FIELD};
use crate::interpreter::tantivy::summary::{SUMMARY_FULL_FIELDS, SUMMARY_PREVIEW_FIELDS};
use crate::optimizer::tantivy_fields::{
    find_required_tantivy_fields, find_sort_item_tantivy_fields, RequiredTantivy<PERSON>ield,
    RequiredTantivyFieldMap,
};
use btql::interpreter::context::ExprContext;
use btql::typesystem::coerce::{
    normalize_to_scalar_type, normalize_value_for_comparison, HasScalarType,
};
use btql::typesystem::CastInto;

use super::ast::{
    BooleanQueryOp, CursorField, CursorFilter, ExpansionProjection, FilterPlan, GroupByPlan,
    NoopPlan, OptimizedPlan, OptimizedSortItem, OptimizedTantivyAggregatePlan,
    OptimizedTantivyAggregatePlanArgs, OptimizedTantivyExpandTracesPlan,
    OptimizedTantivySchemaInferencePlan, OptimizedTantivySchemaInferencePlanArgs,
    OptimizedTantivySearch, OptimizedTantivySearchPlan, OptimizedTantivySearchPlanArgs,
    ProjectPlan, RealtimeWALSearchQuery, TantivyProjectedField, TermQuery, UnpivotPlan,
    UnpivotProjectedField, UnpivotProjectionExpr,
};
use super::error::{OptimizerError, Result};
use super::optimizer::OptimizerContext;
use super::util::constant_fold_expr;
use super::util::is_agg_function;
use btql::binder::ast::{
    flatten_boolean_expr, Alias, ComparisonOp, Field, Function, Projection, Shape, SortDirection,
    SortItem, SummaryExprs, TransformExpr, UnaryOp, UnpivotSource, UnpivotType,
};
use btql::binder::{
    self,
    ast::{Expr, Literal},
};
use btql::schema::ScalarType;
use util::json::{extract_key, field_name_to_json_path, PathPiece};
use util::schema::{BaseOptions, TantivyFieldFilterOpts, TantivyType, TextOptions};

lazy_static! {
    static ref PAGINATED_SORT: Vec<SortItem> = vec![SortItem {
        dir: SortDirection::Desc,
        expr: binder::ast::SortExpr::Expr(Box::new(new_alias_field(
            PAGINATION_KEY_FIELD.to_string()
        ))),
    }];
    static ref DEFAULT_SUMMARY: SummaryExprs = SummaryExprs {
        comparison_key: Box::new(Expr::Field(Field::new(
            vec![PathPiece::Key("input".to_string())],
            Value::Object(serde_json::Map::new()),
            None,
        ))),
        weighted_scores: vec![],
        custom_columns: vec![],
        preview_length: None,
    };
}

const SEARCH_EXPANSION_FACTOR: usize = 10;
pub const AVERAGE_SPANS_PER_TRACE: usize = 100;

pub fn create_tantivy_pushdown_query(
    ctx: &mut OptimizerContext,
    query: &binder::ast::Query,
    expr_ctx: &ExprContext,
) -> Result<Box<OptimizedPlan>> {
    let mut can_pushdown_sort = true;
    let mut can_pushdown_limit = true;

    // Constant fold the projection and filter
    let projection = constant_fold_projection(expr_ctx, query.projection.clone())?;
    let filter = match &query.filter {
        Some(filter) => Some(
            constant_fold_expr(expr_ctx, filter).map_err(|e| OptimizerError::Anyhow(e.into()))?,
        ),
        None => None,
    };

    let cursor_filter = match (&query.cursor, &query.sort.as_slice()) {
        (
            cursor,
            &[SortItem {
                dir,
                expr: binder::ast::SortExpr::Alias(alias),
            }],
        ) => {
            let cursor_field = CursorField::from_sort_alias(&alias);
            if matches!(cursor, Some(_)) && cursor_field.is_none() {
                return Err(OptimizerError::InvalidBindExpr {
                    msg: format!(
                        "Cursor can only be used when sorting by _pagination_key or xact_id (not {})",
                        alias
                    ),
                    op: None,
                });
            }
            cursor_field.map(|field| CursorFilter {
                field,
                dir: (*dir).into(),
                value: cursor.clone(),
                collect: true,
            })
        }
        (
            cursor,
            &[SortItem {
                dir,
                expr: binder::ast::SortExpr::Expr(e),
            }],
        ) => {
            let cursor_field = match e.as_ref() {
                binder::ast::Expr::Field(field) => match &field.name[0] {
                    PathPiece::Key(name) => CursorField::from_sort_alias(name),
                    _ => None,
                },
                _ => None,
            };

            if matches!(cursor, Some(_)) && cursor_field.is_none() {
                return Err(OptimizerError::InvalidBindExpr {
                    msg: format!(
                        "Cursor can only be used when sorting by _pagination_key or xact_id",
                    ),
                    op: Some(e.clone()),
                });
            }

            cursor_field.map(|field| CursorFilter {
                field,
                dir: (*dir).into(),
                value: cursor.clone(),
                collect: true,
            })
        }
        (Some(_), _) => {
            return Err(OptimizerError::InvalidBindExpr {
                msg: "Cursor can only be used when sorting by _pagination_key".to_string(),
                op: None,
            });
        }
        (None, _) => None,
    };

    let (filter, post_aggregation_filter) = match &query.from.shape {
        Shape::Summary => {
            let weighted_scores = query
                .summary
                .as_ref()
                .iter()
                .flat_map(|s| s.weighted_scores.iter().map(|e| e.alias.clone()))
                .collect::<HashSet<String>>();
            // We could choose to include custom columns here, but we don't because they are often metadata fields
            // that people want to filter on (and so we want to search for them in Tantivy).
            let (filter, post_aggregation_filter) =
                super::optimization::summary_filters::split_post_aggregation_filters(
                    ctx,
                    filter,
                    cursor_filter.as_ref().map(|c| c.field),
                    &weighted_scores,
                )?;
            (
                filter
                    .map(|f| super::optimization::summary_filters::mark_negative_is_root_filters(f))
                    .transpose()?,
                post_aggregation_filter,
            )
        }
        _ => (filter, None),
    };

    let (tantivy_search, extra_filter) = match (ctx.opts.no_pushdown, &filter) {
        (false, Some(filter)) => push_tantivy_search_plan(ctx, &filter)?,
        (true, _) | (_, None) => (
            Box::new(OptimizedTantivySearch::AllQuery),
            filter.as_ref().map(|f| Box::new(f.clone())),
        ),
    };

    can_pushdown_limit &= !ctx.opts.no_pushdown;
    can_pushdown_limit &= extra_filter.is_none();

    if matches!(query.limit, Some(0)) {
        return Ok(Box::new(OptimizedPlan::Noop(NoopPlan {})));
    }

    // If we have a limit, then we can use a more precise batch size in the search plan. The
    // SEARCH_EXPANSION_FACTOR helps us optimize for cases where the same string might match multiple
    // spans in the same trace. Even if it's too small or large, the search will complete correctly, it
    // just may take more batches OR require a larger search.
    //
    // Furthermore, if we're doing a trace/span expansion, and the filter is not selective (e.g. created <= 1h),
    // then we can further up this batch size to pull more spans up front. When the filter is not selective, searching
    // itself is also pretty cheap, so it's ok to make this number quite large (100)
    let search_batch_size = query.limit.map(|l| {
        let selectivity_factor = match (
            &query.from.shape,
            super::optimization::selectivity::is_selective(&tantivy_search),
        ) {
            (Shape::Traces | Shape::Summary, false) => AVERAGE_SPANS_PER_TRACE,
            _ => 1,
        };
        l * SEARCH_EXPANSION_FACTOR * selectivity_factor
    });

    let mut required_tantivy_fields = RequiredTantivyFieldMap::new();
    if let Some(filter) = &extra_filter {
        required_tantivy_fields.extend(find_required_tantivy_fields(filter));
    }

    let mut tantivy_sort = None;
    let mut tantivy_sort_fields = RequiredTantivyFieldMap::new();
    if query.sort.len() > 0 {
        match (
            ctx.opts.no_pushdown,
            push_tantivy_sort_plan(&ctx.schema, &query.sort, &projection)?,
        ) {
            (false, Some((dir, tantivy_field, top_level_fields))) => {
                tantivy_sort = Some((dir, tantivy_field));
                tantivy_sort_fields.extend(top_level_fields.clone());
                required_tantivy_fields.extend(top_level_fields);
            }
            (true, _) | (_, None) => {
                for sort_expr in query.sort.iter() {
                    required_tantivy_fields
                        .extend(find_sort_item_tantivy_fields(&sort_expr, &projection)?);
                }
                can_pushdown_sort = false;
                can_pushdown_limit = false;
            }
        }
    }

    let realtime_sort = match &tantivy_sort {
        None => vec![],
        Some((dir, _)) => {
            assert!(tantivy_sort_fields.len() == 1);
            vec![OptimizedSortItem {
                dir: dir.clone(),
                expr: Box::new(Expr::Field(binder::ast::Field::new(
                    vec![PathPiece::Key(make_field_name(
                        &tantivy_sort_fields.keys().next().unwrap(),
                    ))],
                    util::Value::Object(serde_json::Map::new()),
                    None,
                ))),
            }]
        }
    };

    // Additionally, dig through the projection to find fields that are required
    let (aliases, tantivy_search_plan) = match &projection {
        Projection::GroupBy {
            dimensions,
            pivot,
            measures,
        } => {
            if query.from.shape == Shape::Summary {
                return Err(OptimizerError::InvalidBindExpr {
                    msg: "summary is not supported in group by queries".to_string(),
                    op: None,
                });
            }

            if query.cursor.is_some() {
                return Err(OptimizerError::Unsupported {
                    name: "cursor + group by".to_string(),
                    op: None,
                });
            }

            let mut required_groupby_fields = RequiredTantivyFieldMap::new();
            let mut unpivot_fields = vec![vec![]; query.unpivot.len()];
            let mut add_unpivot_field = |expr: &Expr| {
                if let Expr::Field(field) = expr {
                    if let Some(source) = &field.source {
                        unpivot_fields[source.unpivot as usize].push(field.clone());
                    }
                }
            };

            for alias in dimensions.iter().chain(pivot.iter()) {
                required_groupby_fields.extend(find_required_tantivy_fields(&alias.expr));
                alias.expr.traverse(&mut add_unpivot_field);
            }

            // This is a little duplicated with the equivalent logic below for Flat projections
            let mut unpivot_tantivy_projection_names = Vec::new();
            let mut required_unpivot_fields = RequiredTantivyFieldMap::new();
            for unpivot in query.unpivot.iter() {
                let fields = find_required_tantivy_fields(&unpivot.expr);
                if fields.len() != 1 {
                    return Err(OptimizerError::Unsupported {
                        name: "unpivot other than a single field".to_string(),
                        op: Some(unpivot.expr.clone()),
                    });
                } else {
                    unpivot_tantivy_projection_names
                        .push(make_field_name(fields.keys().next().unwrap()));
                    required_unpivot_fields.extend(fields);
                }
            }

            let mut all_agg_functions = Vec::new();
            let mut agg_counter = 0;
            let mut transformed_measures = Vec::new();
            for measure in measures.iter() {
                measure.expr.traverse(&mut add_unpivot_field);
                let (alias, agg_functions) =
                    transform_measure(measure, &mut required_groupby_fields, &mut agg_counter)?;
                all_agg_functions.extend(agg_functions);
                transformed_measures.push(alias);
            }

            // Can only push down group by if all of the fields are fast fields
            // and don't involve array indexing
            let can_push_groupby = !ctx.opts.no_pushdown
                && required_groupby_fields
                    .keys()
                    .chain(required_unpivot_fields.keys())
                    .all(|f| {
                        f.iter().all(|c| matches!(c, PathPiece::Key(_)))
                            && ctx
                                .schema
                                .must_find(extract_key(&f[0]).unwrap())
                                .map_or_else(
                                    |_e| false,
                                    |f| {
                                        f.find_tantivy_field(
                                            TantivyFieldFilterOpts::new().is_fast(),
                                        )
                                        .is_some()
                                    },
                                )
                    });

            required_tantivy_fields.extend(required_groupby_fields);

            let transformed_dimensions = dimensions
                .iter()
                .map(|d| d.clone().transform(&mut replace_fields_with_aliases))
                .collect();
            let transformed_pivot = pivot
                .iter()
                .map(|p| p.clone().transform(&mut replace_fields_with_aliases))
                .collect();

            let unpivot = make_unpivot_exprs(
                &query.unpivot,
                &unpivot_fields,
                &unpivot_tantivy_projection_names,
            )?;

            let groupby_plan = if can_push_groupby && extra_filter.is_none() {
                let tantivy_projection = make_tantivy_projection(ctx, &required_tantivy_fields)?;
                Box::new(OptimizedPlan::TantivyAggregate(
                    OptimizedTantivyAggregatePlan::new(OptimizedTantivyAggregatePlanArgs {
                        object_ids: query.from.objects.clone(),
                        search: tantivy_search,
                        realtime_search: RealtimeWALSearchQuery {
                            filter: filter.map(|f| Box::new(f)),
                            projection: make_realtime_projection(&required_tantivy_fields)?
                                .into_iter()
                                .chain(unpivot.iter().flat_map(|n| match &n.projected_field {
                                    UnpivotProjectedField::Array { item } => {
                                        vec![Alias {
                                            alias: item.clone(),
                                            expr: Box::new(Expr::Field(binder::ast::Field::new(
                                                vec![PathPiece::Key(item.clone())],
                                                Value::Object(serde_json::Map::new()),
                                                None,
                                            ))),
                                        }]
                                    }
                                    UnpivotProjectedField::Object { key, value } => {
                                        vec![
                                            Alias {
                                                alias: key.clone(),
                                                expr: Box::new(Expr::Field(
                                                    binder::ast::Field::new(
                                                        vec![PathPiece::Key(key.clone())],
                                                        Value::Object(serde_json::Map::new()),
                                                        None,
                                                    ),
                                                )),
                                            },
                                            Alias {
                                                alias: value.clone(),
                                                expr: Box::new(Expr::Field(
                                                    binder::ast::Field::new(
                                                        vec![PathPiece::Key(value.clone())],
                                                        Value::Object(serde_json::Map::new()),
                                                        None,
                                                    ),
                                                )),
                                            },
                                        ]
                                    }
                                }))
                                .collect(),
                            unpivot: unpivot.clone(),
                            sort: vec![],
                        },
                        projection: tantivy_projection,
                        unpivot,
                        dimensions: transformed_dimensions,
                        pivot: transformed_pivot,
                        measures: transformed_measures,
                        aggregates: all_agg_functions,
                        expand_traces: matches!(query.from.shape, Shape::Traces | Shape::Summary),
                    }),
                ))
            } else {
                // If we cannot push the unpivot, then we need to project each of the referenced fields.
                required_tantivy_fields.extend(required_unpivot_fields);

                let tantivy_projection = make_tantivy_projection(ctx, &required_tantivy_fields)?;

                // We can't push down the group by so run it in the local interpreter, but still push as much of the filter as possible.
                let search_plan = Box::new(OptimizedPlan::TantivySearch(
                    OptimizedTantivySearchPlan::new(OptimizedTantivySearchPlanArgs {
                        object_ids: query.from.objects.clone(),
                        search: tantivy_search,
                        realtime_search: RealtimeWALSearchQuery {
                            filter: filter.map(|f| Box::new(f)),
                            projection: make_realtime_projection(&required_tantivy_fields)?,
                            unpivot: vec![], // Since we're not pushing it down, we'll unpivot in the outer operator.
                            sort: vec![],
                        },
                        projection: tantivy_projection,
                        sort: None,
                        limit: None,
                        batch_size: search_batch_size,
                        cursor: None,
                        include_reader_batch: false,
                    }),
                ));

                let with_unpivot = if query.unpivot.len() > 0 {
                    Box::new(OptimizedPlan::Unpivot(UnpivotPlan {
                        from: search_plan,
                        unpivot,
                    }))
                } else {
                    search_plan
                };

                let filter_plan = match extra_filter {
                    Some(filter) => Box::new(OptimizedPlan::Filter(FilterPlan {
                        from: with_unpivot,
                        filter: Some(Box::new(
                            super::local::optimize_expr(Cow::Owned(*filter))?
                                .into_owned()
                                .transform(&mut replace_fields_with_aliases),
                        )),
                    })),
                    None => with_unpivot,
                };

                if matches!(query.from.shape, Shape::Traces | Shape::Summary) {
                    return Err(OptimizerError::Unsupported {
                        name: format!("non-primitive group by on {:?}", query.from.shape),
                        op: None,
                    });
                }

                Box::new(OptimizedPlan::GroupBy(GroupByPlan {
                    from: filter_plan,
                    dimensions: transformed_dimensions,
                    pivot: transformed_pivot,
                    aggregates: all_agg_functions,
                    measures: transformed_measures,
                }))
            };

            (None, groupby_plan)
        }
        Projection::Flat { select } => {
            let mut required_projection_fields = RequiredTantivyFieldMap::new();
            for alias in select.iter() {
                required_projection_fields.extend(find_required_tantivy_fields(&alias.expr));
            }

            let mut unpivot_tantivy_projection_names = Vec::new();
            for unpivot in query.unpivot.iter() {
                let fields = find_required_tantivy_fields(&unpivot.expr);
                if fields.len() != 1 {
                    return Err(OptimizerError::Unsupported {
                        name: "unpivot other than a single field".to_string(),
                        op: Some(unpivot.expr.clone()),
                    });
                } else {
                    unpivot_tantivy_projection_names
                        .push(make_field_name(fields.keys().next().unwrap()));
                }
                required_projection_fields.extend(fields);
            }

            let (mut base_search_projection, include_reader_batch) = match query.from.shape {
                Shape::Spans => (required_projection_fields.clone(), false),
                Shape::Traces | Shape::Summary => {
                    ctx.schema.must_find(ROOT_SPAN_ID_FIELD)?; // Just validate that the field exists

                    let mut projected_fields = RequiredTantivyFieldMap::new();
                    projected_fields.insert(
                        vec![PathPiece::Key(ROOT_SPAN_ID_FIELD.to_string())],
                        RequiredTantivyField::new(ScalarType::String),
                    );

                    match &cursor_filter {
                        Some(CursorFilter { field, .. }) => {
                            let field_name = field.get_field_name();
                            ctx.schema.must_find(field_name)?; // Just validate that the field exists
                            projected_fields.insert(
                                vec![PathPiece::Key(field_name.to_string())],
                                RequiredTantivyField::new(ScalarType::String),
                            );
                        }
                        _ => {}
                    };

                    (projected_fields, true)
                }
            };

            // This pulls in the fields we need for filtering/sorting
            base_search_projection.extend(required_tantivy_fields);
            let base_search_plan_fields = make_tantivy_projection(ctx, &base_search_projection)?;

            let tantivy_search_plan = Box::new(OptimizedPlan::TantivySearch(
                OptimizedTantivySearchPlan::new(OptimizedTantivySearchPlanArgs {
                    object_ids: query.from.objects.clone(),
                    search: tantivy_search,
                    realtime_search: RealtimeWALSearchQuery {
                        filter: filter.map(|f| Box::new(f)),
                        projection: make_realtime_projection(&base_search_projection)?,
                        unpivot: vec![],
                        sort: realtime_sort,
                    },
                    projection: base_search_plan_fields,
                    sort: tantivy_sort,
                    limit: if can_pushdown_limit && matches!(query.from.shape, Shape::Spans) {
                        query.limit
                    } else {
                        None
                    },
                    batch_size: search_batch_size,
                    cursor: if matches!(query.from.shape, Shape::Spans) {
                        cursor_filter.clone()
                    } else {
                        None
                    },
                    include_reader_batch,
                }),
            ));

            let with_unpivot =
                if query.unpivot.len() > 0 && matches!(query.from.shape, Shape::Spans) {
                    make_unpivot_plan(
                        tantivy_search_plan,
                        &query.unpivot,
                        &select,
                        &unpivot_tantivy_projection_names,
                    )?
                } else {
                    tantivy_search_plan
                };

            let with_filter = match extra_filter {
                Some(filter) => Box::new(OptimizedPlan::Filter(FilterPlan {
                    from: with_unpivot,
                    filter: Some(Box::new(
                        super::local::optimize_expr(Cow::Owned(*filter))?
                            .into_owned()
                            .transform(&mut replace_fields_with_aliases),
                    )),
                })),
                None => with_unpivot,
            };

            let perform_expansion = match query.from.shape {
                Shape::Spans => with_filter,
                Shape::Traces | Shape::Summary => {
                    let with_sort =
                        if can_pushdown_limit && (can_pushdown_sort || query.sort.len() == 0) {
                            with_filter
                        } else {
                            Box::new(OptimizedPlan::Project(ProjectPlan {
                                from: with_filter,
                                sort: if can_pushdown_sort {
                                    vec![]
                                } else {
                                    query
                                        .sort
                                        .iter()
                                        .map(|s| {
                                            apply_replace_fields_with_aliases_to_sort_expr(
                                                s.clone(),
                                                None,
                                            )
                                        })
                                        .collect::<Result<Vec<_>>>()?
                                },
                                limit: None,
                                projection: None,
                                cursor_field: cursor_filter.as_ref().map(|c| c.field.clone()),
                                is_top_level_limiter: false,
                            }))
                        };
                    can_pushdown_limit = true;
                    can_pushdown_sort = true;

                    let projection = if matches!(query.from.shape, Shape::Summary) {
                        let mut required_projection_fields = RequiredTantivyFieldMap::new();
                        for field in SUMMARY_PREVIEW_FIELDS
                            .iter()
                            .chain(SUMMARY_FULL_FIELDS.iter())
                        {
                            if ctx.schema.find_field(field).is_some() {
                                required_projection_fields.insert(
                                    vec![PathPiece::Key(field.to_string())],
                                    RequiredTantivyField::new(ScalarType::Unknown),
                                );
                            }
                        }

                        let summary_exprs = if let Some(summary) = &query.summary {
                            summary
                        } else {
                            &DEFAULT_SUMMARY
                        };

                        required_projection_fields
                            .extend(find_required_tantivy_fields(&summary_exprs.comparison_key));

                        let tantivy_projection =
                            make_tantivy_projection(ctx, &required_projection_fields)?;

                        ExpansionProjection::Summary {
                            root_projection: tantivy_projection,
                            comparison_key: summary_exprs.comparison_key.clone(),
                            // These scores are in terms of the output score values, so do not need to be considered
                            // in the projection.
                            weighted_scores: summary_exprs.weighted_scores.clone(),
                            // Custom columns also use the fields that are already projected (before they get transformed into
                            // preview values).
                            custom_columns: summary_exprs.custom_columns.clone(),
                            post_aggregation_filter: post_aggregation_filter.map(|f| Box::new(f)),
                            preview_length: summary_exprs.preview_length.clone(),
                        }
                    } else {
                        let default_projection =
                            make_tantivy_projection(ctx, &required_projection_fields)?;

                        let (mut root_projection, mut span_projection) =
                            super::optimization::root_projection::split_root_projection(
                                ctx,
                                default_projection,
                                &select,
                                &query.unpivot,
                            )?;

                        let cursor_field_name = match &cursor_filter {
                            Some(CursorFilter { field, .. }) => Some(field.get_field_name()),
                            None => None,
                        };

                        for p in [&mut root_projection, &mut span_projection] {
                            p.push(TantivyProjectedField {
                                alias: make_field_name(&vec![PathPiece::Key(
                                    ROOT_SPAN_ID_FIELD.to_string(),
                                )]),
                                top_level_field: ROOT_SPAN_ID_FIELD.to_string(),
                                json_path: vec![],
                                repeated: false,
                                field_type: TantivyType::Str(TextOptions {
                                    stored: true,
                                    fast: true,
                                    tokenize: false,
                                }),
                                lossy_fast_field: false,
                                only_exists: false,
                            });
                            p.push(TantivyProjectedField {
                                alias: make_field_name(&vec![PathPiece::Key(ID_FIELD.to_string())]),
                                top_level_field: ID_FIELD.to_string(),
                                json_path: vec![],
                                repeated: false,
                                field_type: TantivyType::Str(TextOptions {
                                    stored: true,
                                    fast: true,
                                    tokenize: false,
                                }),
                                lossy_fast_field: false,
                                only_exists: false,
                            });

                            match &cursor_field_name {
                                Some(field_name) => {
                                    p.push(TantivyProjectedField {
                                        alias: make_field_name(&vec![PathPiece::Key(
                                            field_name.to_string(),
                                        )]),
                                        top_level_field: field_name.to_string(),
                                        json_path: vec![],
                                        repeated: false,
                                        field_type: TantivyType::U64(BaseOptions {
                                            stored: true,
                                            fast: true,
                                        }),
                                        lossy_fast_field: false,
                                        only_exists: false,
                                    });
                                }
                                _ => {}
                            };
                        }

                        let mut realtime_projection =
                            make_realtime_projection(&required_projection_fields)?;
                        for system_field in [ROOT_SPAN_ID_FIELD, ID_FIELD]
                            .into_iter()
                            .chain(cursor_field_name.into_iter())
                        {
                            realtime_projection.push(Alias {
                                alias: make_field_name(&vec![PathPiece::Key(
                                    system_field.to_string(),
                                )]),
                                expr: Box::new(Expr::Field(binder::ast::Field::new(
                                    vec![PathPiece::Key(system_field.to_string())],
                                    util::Value::Object(serde_json::Map::new()),
                                    None,
                                ))),
                            });
                        }
                        ExpansionProjection::Spans {
                            is_root_projection: vec![TantivyProjectedField {
                                alias: make_field_name(&vec![PathPiece::Key(
                                    IS_ROOT_FIELD.to_string(),
                                )]),
                                top_level_field: IS_ROOT_FIELD.to_string(),
                                json_path: vec![],
                                repeated: false,
                                field_type: TantivyType::Bool(BaseOptions {
                                    stored: true,
                                    fast: true,
                                }),
                                lossy_fast_field: false,
                                only_exists: false,
                            }],
                            root_projection,
                            span_projection,
                            realtime_projection,
                        }
                    };

                    let expand_traces_plan = Box::new(OptimizedPlan::TantivyExpandTraces(
                        OptimizedTantivyExpandTracesPlan {
                            from: with_sort,
                            projection,
                            limit: query.limit,
                            cursor: cursor_filter.clone(),
                            is_top_level_limiter: true,
                        },
                    ));

                    let with_unpivot = if query.unpivot.len() > 0 {
                        make_unpivot_plan(
                            expand_traces_plan,
                            &query.unpivot,
                            &select,
                            &unpivot_tantivy_projection_names,
                        )?
                    } else {
                        expand_traces_plan
                    };

                    with_unpivot
                }
            };

            (
                Some(
                    select
                        .clone()
                        .into_iter()
                        .map(|alias| alias.transform(&mut replace_fields_with_aliases))
                        .collect(),
                ),
                perform_expansion,
            )
        }
        Projection::Infer { infer } => {
            let mut required_projection_fields = RequiredTantivyFieldMap::new();
            for field in infer.iter() {
                required_projection_fields.insert(
                    field.name.clone(),
                    RequiredTantivyField::new(weakest_scalar_type(&field.expr_type)),
                );
            }

            if extra_filter.is_some() {
                return Err(OptimizerError::Unsupported {
                    name: "Inference with complex filter".to_string(),
                    op: extra_filter,
                });
            }

            (
                None,
                Box::new(OptimizedPlan::TantivySchemaInference(
                    OptimizedTantivySchemaInferencePlan::new(
                        OptimizedTantivySchemaInferencePlanArgs {
                            object_ids: query.from.objects.clone(),
                            search: tantivy_search,
                            realtime_search: RealtimeWALSearchQuery {
                                filter: filter.map(|f| Box::new(f)),
                                projection: make_realtime_projection(&required_projection_fields)?,
                                unpivot: vec![],
                                sort: realtime_sort,
                            },
                            infer: make_tantivy_projection(ctx, &required_projection_fields)?,
                            sort: tantivy_sort,
                            limit: query.limit,
                            batch_size: search_batch_size,
                            cursor: query.cursor.clone(),
                        },
                    ),
                )),
            )
        }
    };

    if matches!(query.from.shape, Shape::Summary) || matches!(projection, Projection::Infer { .. })
    {
        // No need to wrap this in a projection plan
        return Ok(tantivy_search_plan);
    }

    Ok(Box::new(OptimizedPlan::Project(ProjectPlan {
        from: tantivy_search_plan,
        limit: if can_pushdown_limit {
            None
        } else {
            query.limit
        },
        sort: if can_pushdown_sort {
            vec![]
        } else if matches!(aliases, None) {
            // If we aren't projecting, then we just use the existing aliases.
            query
                .sort
                .iter()
                .map(|expr| OptimizedSortItem {
                    dir: expr.dir.clone(),
                    expr: match expr.expr.clone() {
                        binder::ast::SortExpr::Alias(alias) => {
                            Box::new(Expr::Field(binder::ast::Field::new(
                                vec![PathPiece::Key(alias.clone())],
                                util::Value::Object(serde_json::Map::new()),
                                None,
                            )))
                        }
                        binder::ast::SortExpr::Expr(expr) => expr,
                    },
                })
                .collect()
        } else {
            query
                .sort
                .iter()
                .map(|s| {
                    apply_replace_fields_with_aliases_to_sort_expr(s.clone(), aliases.as_ref())
                })
                .collect::<Result<Vec<_>>>()?
        },
        projection: aliases,
        cursor_field: cursor_filter.map(|c| c.field.clone()),
        // XXX(aphinx): this is probably not quite right
        is_top_level_limiter: !can_pushdown_limit,
    })))
}

pub fn make_tantivy_projection(
    ctx: &OptimizerContext,
    required_tantivy_fields: &RequiredTantivyFieldMap,
) -> Result<Vec<TantivyProjectedField>> {
    Ok(required_tantivy_fields
        .into_iter()
        .map(|(f, required_tantivy_field)| {
            let schema_field = ctx.schema.must_find(extract_top_level_key(&f)?)?;
            let tantivy_field =
                schema_field.must_find(TantivyFieldFilterOpts::new().is_stored())?;

            let lossy_fast_field = tantivy_field.lossy_fast_field
                && required_tantivy_field.field_type.is_object_like();

            Ok(TantivyProjectedField {
                alias: make_field_name(&f),
                top_level_field: tantivy_field.name.clone(),
                json_path: f[1..].to_vec(),
                repeated: tantivy_field.repeated && f.len() == 1,
                field_type: tantivy_field.field_type.clone(),
                lossy_fast_field,
                only_exists: required_tantivy_field.only_exists,
            })
        })
        .collect::<Result<Vec<_>>>()?)
}

pub fn make_realtime_projection(
    required_tantivy_fields: &RequiredTantivyFieldMap,
) -> Result<Vec<Alias>> {
    Ok(required_tantivy_fields
        .into_iter()
        .map(|(alias, _)| {
            Ok(Alias {
                alias: make_field_name(&alias),
                expr: Box::new(Expr::Field(Field::new(
                    alias.clone(),
                    // NOTE: We could use the scalar (or maybe propagate along the full logical type) here
                    util::Value::Object(serde_json::Map::new()),
                    None,
                ))),
            })
        })
        .collect::<Result<Vec<_>>>()?)
}

// When we transform a measure, we need to:
// - Add the fields it depends on to the set of required tantivy fields
// - Extract out the aggregate functions, and make them expressions in terms of those fields, with aliases.
// - Extract out the expressions that are in terms of the aggregates
//
// TODO:
// - Deduplicate aggregates
fn transform_measure(
    alias: &Alias,
    required_tantivy_fields: &mut RequiredTantivyFieldMap,
    agg_counter: &mut usize,
) -> Result<(Alias, Vec<(String, Function)>)> {
    required_tantivy_fields.extend(find_required_tantivy_fields(&alias.expr));
    let base_expr = alias
        .expr
        .clone()
        .transform(&mut replace_fields_with_aliases);

    let mut agg_functions = Vec::new();
    let mut find_and_replace_aggregates = |expr: Expr| {
        if let Expr::Function(Function { name, args }) = expr {
            if is_agg_function(&name) {
                let next_agg_name = format!("__agg_{}", *agg_counter);
                *agg_counter += 1;

                agg_functions.push((next_agg_name.clone(), Function { name, args }));

                new_alias_field(next_agg_name)
            } else {
                Expr::Function(Function { name, args })
            }
        } else {
            expr
        }
    };

    let final_expr = base_expr.transform(&mut find_and_replace_aggregates);

    Ok((
        Alias {
            alias: alias.alias.clone(),
            expr: Box::new(final_expr),
        },
        agg_functions,
    ))
}

fn new_alias_field(name: String) -> Expr {
    Expr::Field(binder::ast::Field::new(
        vec![PathPiece::Key(name)],
        util::Value::Object(serde_json::Map::new()),
        None,
    ))
}

// Returns a tantivy search and an optional extra filter that cannot be pushed down.
fn push_tantivy_search_plan(
    ctx: &OptimizerContext,
    filter: &binder::ast::Expr,
) -> Result<(Box<OptimizedTantivySearch>, Option<Box<binder::ast::Expr>>)> {
    Ok(match filter {
        Expr::Literal(Literal { value, .. }) => {
            let value = value.as_bool().ok_or(OptimizerError::Unsupported {
                name: format!("non-boolean literal in filter"),
                op: Some(Box::new(filter.clone())),
            })?;
            if value {
                (Box::new(OptimizedTantivySearch::AllQuery), None)
            } else {
                (Box::new(OptimizedTantivySearch::EmptyQuery), None)
            }
        }
        Expr::Interval { .. } => {
            return Err(OptimizerError::Unsupported {
                name: format!("interval is not a valid filter"),
                op: Some(Box::new(filter.clone())),
            });
        }
        Expr::Function { .. } => {
            return Err(OptimizerError::Unsupported {
                name: format!("function is not a valid filter"),
                op: Some(Box::new(filter.clone())),
            });
        }
        Expr::Comparison { op, left, right } => {
            use btql::binder::ast::ComparisonOp::*;
            match op {
                Eq | Match | Like | Ilike => {
                    // TODO: Technically if we aren't able to extract a field and literal, we can bubble
                    // this query up to the planner and run it locally.
                    let (field, mut literal, _) = match extract_field_and_literal(left, right) {
                        Some(x) => x,
                        None => {
                            return Ok((
                                Box::new(OptimizedTantivySearch::AllQuery),
                                Some(Box::new(filter.clone())),
                            ));
                        }
                    };
                    if matches!(literal.expr_type, ScalarType::Null) {
                        return Ok((Box::new(OptimizedTantivySearch::EmptyQuery), None));
                    }

                    let schema_field = ctx.schema.must_find(extract_top_level_key(&field.name)?)?;
                    let (json_path, field_name_contains_array_index) =
                        field_name_to_json_path(&field.name[1..]);

                    let matching_tantivy_field = schema_field.tantivy.iter().find(|f| {
                        // This is a proxy for whether the match represents a tokenizable operation in the first place. It'd be slightly
                        // more accurate to check the logical schema of the field, but this is simpler.
                        !tantivy_type_can_be_tokenized(&f.field_type)
                        // Or, if it's an equality, it's not tokenized
                            || (*op == btql::binder::ast::ComparisonOp::Eq
                                && !f.field_type.tokenized()
                                && !matches!(literal.value, Value::Object(_) | Value::Array(_)))
                        // Or, it's a match/like/ilike, and it's tokenized, but not JSON. We can't use JSON because we want match to work on nested fields.
                            || ((*op == btql::binder::ast::ComparisonOp::Match
                                || *op == btql::binder::ast::ComparisonOp::Like
                                || *op == btql::binder::ast::ComparisonOp::Ilike)
                                && f.field_type.tokenized()
                                && !matches!(f.field_type, util::schema::TantivyType::Json(_)))
                    });

                    let is_nested_match = (*op == btql::binder::ast::ComparisonOp::Match
                        || *op == btql::binder::ast::ComparisonOp::Like
                        || *op == btql::binder::ast::ComparisonOp::Ilike)
                        && field.name.len() > 1;

                    let mut must_rerun = false;
                    let stored_tantivy_field =
                        schema_field.must_find(TantivyFieldFilterOpts::new().is_stored())?;

                    let use_match = match matching_tantivy_field {
                        None => false,
                        Some(_) => match *op {
                            btql::binder::ast::ComparisonOp::Match => {
                                match CastInto::<String>::cast(&literal.value) {
                                    Ok(_) => true,
                                    Err(_) => false,
                                }
                            }
                            btql::binder::ast::ComparisonOp::Eq => true,
                            _ => false,
                        },
                    };

                    // Normalize the datetime into a format that's amenable to tantivy if it's a JSON field
                    // matching a datetime. This kind of thing would also be solved by having a datetime value...
                    let mut is_datetime = false;
                    match (&mut literal.value, &stored_tantivy_field.field_type) {
                        (util::Value::String(s), util::schema::TantivyType::Json(_)) => {
                            match CastInto::<tantivy::DateTime>::cast(s) {
                                Ok(dt) => {
                                    literal.value = util::Value::String(format!("{:?}", dt));
                                    is_datetime = true;
                                }
                                Err(_) => {}
                            }
                        }
                        _ => {}
                    }

                    // E.g. foo.bar MATCH 'baz' will run a term query on `foo`, but then we need to rerun
                    // the query on `foo.bar`. This also is relevant for LIKE/MATCH on arrays, since tantivy
                    // flattens them in the inverted index. We need to rerun the query on the array index after.
                    if field_name_contains_array_index || is_nested_match {
                        must_rerun = true;
                    }

                    let matching_tantivy_field = match use_match {
                        true => matching_tantivy_field.unwrap(),
                        false => {
                            if (*op == btql::binder::ast::ComparisonOp::Match
                                || *op == btql::binder::ast::ComparisonOp::Like
                                || *op == btql::binder::ast::ComparisonOp::Ilike)
                                && !is_datetime
                                // Cannot do partial MATCH on JSON fields that are untokenized, but we can
                                // do regexes on them.
                                && (
                                    *op != btql::binder::ast::ComparisonOp::Match
                                    || !matches!(
                                    stored_tantivy_field.field_type,
                                    util::schema::TantivyType::Json(_)
                                ))
                                && (matches!(
                                    stored_tantivy_field.field_type,
                                    util::schema::TantivyType::Str(_)
                                ) || matches!(literal.expr_type, ScalarType::String))
                            {
                                let literal_string: String = literal.value.cast()?;

                                let tokenizer = ctx.tokenizer_manager()?.get("default");
                                let mut regex_pieces: Vec<String> = Vec::new();

                                match *op {
                                    btql::binder::ast::ComparisonOp::Match => {
                                        if let Some(mut tokenizer) = tokenizer {
                                            let mut token_stream = tokenizer.token_stream(&literal_string);
                                            let mut terms = Vec::new();
                                            token_stream.process(&mut |token| {
                                                let escaped_literal = regex::escape(token.text.as_str());
                                                terms.push(escaped_literal);
                                            });
                                            for term in terms {
                                                regex_pieces.push(term);
                                            }
                                        } else {
                                            regex_pieces.push(regex::escape(&literal_string));
                                        }

                                        if matches!(stored_tantivy_field.field_type, util::schema::TantivyType::Json(..)) && like_value_is_not_a_string_value(&literal_string) {
                                            return Ok((
                                                Box::new(OptimizedTantivySearch::AllQuery),
                                                Some(Box::new(filter.clone())),
                                            ));
                                        }

                                        let pattern = format!(".*{}.*", regex_pieces.join(".*"));
                                        return Ok((
                                            Box::new(OptimizedTantivySearch::RegexQuery {
                                                field: stored_tantivy_field.clone(),
                                                json_path: if is_nested_match {
                                                    json_path
                                                } else {
                                                    vec![]
                                                },
                                                pattern,
                                                case_insensitive: true, // MATCH is case insensitive
                                            }),
                                            // Always rerun for MATCH because the regex is a partial filter
                                            Some(Box::new(filter.clone())),
                                        ));
                                    },
                                    btql::binder::ast::ComparisonOp::Like | btql::binder::ast::ComparisonOp::Ilike => {
                                        if stored_tantivy_field.field_type.tokenized() {
                                            // Can't run regex on a tokenized field
                                            return Ok((
                                                Box::new(OptimizedTantivySearch::AllQuery),
                                                Some(Box::new(filter.clone())),
                                            ));
                                        }

                                        let pattern = regex::escape(&literal_string)
                                            .replace("%", ".*");

                                        // This is a hack that essentially says, if you're trying to do LIKE/ILIKE on a number or datetime,
                                        // we can't use a regex on it, because the JSON value may have been tokenized into something else.
                                        // It isn't perfect, because an invalid number or date like '2024-' will not satisfy this constraint,
                                        // but it will not match a tokenized '2024-01-01'
                                        if matches!(stored_tantivy_field.field_type, util::schema::TantivyType::Json(..)) && like_value_is_not_a_string_value(&literal_string) {
                                            return Ok((
                                                Box::new(OptimizedTantivySearch::AllQuery),
                                                Some(Box::new(filter.clone())),
                                            ));
                                        }

                                        return Ok((
                                            Box::new(OptimizedTantivySearch::RegexQuery {
                                                field: stored_tantivy_field.clone(),
                                                json_path: if is_nested_match {
                                                    json_path
                                                } else {
                                                    vec![]
                                                },
                                                case_insensitive: *op == btql::binder::ast::ComparisonOp::Ilike,
                                                pattern,
                                            }),
                                            if must_rerun {
                                                Some(Box::new(filter.clone()))
                                            } else {
                                                None
                                            },
                                        ));
                                    },
                                    _ => unreachable!("Only MATCH, LIKE and ILIKE operators should reach this code")
                                };
                            } else {
                                return Ok((
                                    Box::new(OptimizedTantivySearch::AllQuery),
                                    Some(Box::new(filter.clone())),
                                ));
                            }
                        }
                    };

                    let mut terms = create_terms_query(
                        &matching_tantivy_field,
                        if is_nested_match { &[] } else { &json_path },
                        &literal,
                    )?;

                    let terms_query = if terms.len() == 1 {
                        Box::new(OptimizedTantivySearch::TermQuery(terms.remove(0)))
                    } else {
                        Box::new(OptimizedTantivySearch::BooleanQuery(
                            terms
                                .into_iter()
                                .map(|t| {
                                    (BooleanQueryOp::Should, OptimizedTantivySearch::TermQuery(t))
                                })
                                .collect(),
                        ))
                    };

                    (
                        terms_query,
                        if must_rerun {
                            Some(Box::new(filter.clone()))
                        } else {
                            None
                        },
                    )
                }
                Ne => push_tantivy_search_plan(
                    ctx,
                    &Expr::Unary {
                        op: UnaryOp::Not,
                        expr: Box::new(Expr::Comparison {
                            op: Eq,
                            left: left.clone(),
                            right: right.clone(),
                        }),
                    },
                )?,
                Lt | Le | Gt | Ge => {
                    let (field, literal, left_is_field) =
                        match extract_field_and_literal(left, right) {
                            Some(x) => x,
                            None => {
                                return Ok((
                                    Box::new(OptimizedTantivySearch::AllQuery),
                                    Some(Box::new(filter.clone())),
                                ))
                            }
                        };

                    let op = match (op, left_is_field) {
                        (_, true) => op.clone(),
                        (Lt, false) => Ge,
                        (Le, false) => Gt,
                        (Gt, false) => Le,
                        (Ge, false) => Lt,
                        _ => unreachable!("Only Lt, Le, Gt, Ge should reach this code"),
                    };

                    let (lower, upper) = match op {
                        Lt => (Bound::Unbounded, Bound::Excluded(literal.value.clone())),
                        Le => (Bound::Unbounded, Bound::Included(literal.value.clone())),
                        Gt => (Bound::Excluded(literal.value.clone()), Bound::Unbounded),
                        Ge => (Bound::Included(literal.value.clone()), Bound::Unbounded),
                        _ => unreachable!("Only Lt, Le, Gt, Ge should reach this code"),
                    };

                    let schema_field = ctx.schema.must_find(extract_top_level_key(&field.name)?)?;
                    let (json_path, field_name_contains_array_index) =
                        field_name_to_json_path(&field.name[1..]);

                    if let Some(fast_field_query) = make_fast_field_range_query(
                        &schema_field,
                        &json_path,
                        literal.expr_type,
                        &lower,
                        &upper,
                    )? {
                        return Ok((
                            fast_field_query,
                            if field_name_contains_array_index {
                                Some(Box::new(filter.clone()))
                            } else {
                                None
                            },
                        ));
                    }

                    if let Some(indexed_field_query) =
                        make_inverted_index_range_query(&schema_field, &json_path, &literal, op)?
                    {
                        return Ok((
                            indexed_field_query,
                            if field_name_contains_array_index {
                                Some(Box::new(filter.clone()))
                            } else {
                                None
                            },
                        ));
                    }

                    // We were not able to push down the range
                    (
                        Box::new(OptimizedTantivySearch::AllQuery),
                        Some(Box::new(filter.clone())),
                    )
                }
            }
        }
        Expr::Includes { haystack, needle } => {
            let (field, literal, left_is_field) = match extract_field_and_literal(haystack, needle)
            {
                Some(x) => x,
                None => {
                    return Ok((
                        Box::new(OptimizedTantivySearch::AllQuery),
                        Some(Box::new(filter.clone())),
                    ));
                }
            };

            match (&literal.value, left_is_field) {
                // NOTE(austin): `includes []` returns true because it's an AND op across each
                // element of the needle array. For consistency, `includes null` must also return
                // true since `[]` and `null` are semantically equivalent.
                (Value::Null, true) => {
                    return Ok((Box::new(OptimizedTantivySearch::AllQuery), None));
                }
                (Value::Array(array), true) => {
                    if array.is_empty() {
                        return Ok((Box::new(OptimizedTantivySearch::AllQuery), None));
                    }

                    let (queries, extra_filters) = array
                        .iter()
                        .map(|item| {
                            push_tantivy_search_plan(
                                ctx,
                                &Expr::Comparison {
                                    op: btql::binder::ast::ComparisonOp::Match,
                                    left: Box::new(Expr::Field(field.clone())),
                                    right: Box::new(Expr::Literal(Literal {
                                        value: item.clone(),
                                        expr_type: item.scalar_type(),
                                    })),
                                },
                            )
                        })
                        .collect::<Result<Vec<_>>>()?
                        .into_iter()
                        .fold(
                            (Vec::with_capacity(array.len()), Vec::new()),
                            |(mut queries, mut extra_filters), (query, extra_filter)| {
                                queries.push((BooleanQueryOp::Must, *query));
                                if let Some(filter) = extra_filter {
                                    extra_filters.push(*filter);
                                }
                                (queries, extra_filters)
                            },
                        );

                    let combined_extra_filter =
                        flatten_boolean_expr(btql::binder::ast::BooleanOp::And, extra_filters);

                    (
                        Box::new(OptimizedTantivySearch::BooleanQuery(queries)),
                        combined_extra_filter.map(Box::new),
                    )
                }
                (_, true) => push_tantivy_search_plan(
                    ctx,
                    &Expr::Comparison {
                        op: btql::binder::ast::ComparisonOp::Match,
                        left: Box::new(Expr::Field(field.clone())),
                        right: Box::new(Expr::Literal(literal)),
                    },
                )?,
                // Tantivy can't help us if the LHS is a literal.
                (_, false) => (
                    Box::new(OptimizedTantivySearch::AllQuery),
                    Some(Box::new(filter.clone())),
                ),
            }
        }
        Expr::Boolean { op, children } => {
            let queries_and_filters = children
                .iter()
                .map(|child| push_tantivy_search_plan(ctx, child))
                .collect::<Result<Vec<_>>>()?;

            // If it's an OR, and there are filters that couldn't be pushed down, we don't push down the filter at all.
            if matches!(op, &btql::binder::ast::BooleanOp::Or)
                && queries_and_filters
                    .iter()
                    .any(|(_, filter)| filter.is_some())
            {
                return Ok((
                    Box::new(OptimizedTantivySearch::AllQuery),
                    Some(Box::new(filter.clone())),
                ));
            }

            // Otherwise, accumulate both sets of filters
            let mut pushed_queries = Vec::new();
            let mut extra_filters = Vec::new();

            for (pushed_query, extra_filter) in queries_and_filters {
                pushed_queries.push(*pushed_query);
                if let Some(extra_filter) = extra_filter {
                    extra_filters.push(*extra_filter);
                }
            }

            let combined_extra_filter = flatten_boolean_expr(*op, extra_filters);

            let boolean_op = match op {
                btql::binder::ast::BooleanOp::And => BooleanQueryOp::Must,
                btql::binder::ast::BooleanOp::Or => BooleanQueryOp::Should,
            };

            (
                Box::new(OptimizedTantivySearch::BooleanQuery(
                    pushed_queries
                        .into_iter()
                        .map(|query| (boolean_op, query))
                        .collect(),
                )),
                combined_extra_filter.map(Box::new),
            )
        }
        Expr::Field(f) => {
            let top_level_field = ctx.schema.must_find(extract_top_level_key(&f.name)?)?;
            let stored_field =
                top_level_field.must_find(TantivyFieldFilterOpts::new().is_stored())?;

            match stored_field.field_type {
                util::schema::TantivyType::Bool(..) => {
                    // If it's a boolean field, we can do the same thing as field=true, which will get optimized better
                    // This is important to make is_root queries fast.
                    return push_tantivy_search_plan(
                        ctx,
                        &Expr::Comparison {
                            op: btql::binder::ast::ComparisonOp::Eq,
                            left: Box::new(Expr::Field(f.clone())),
                            right: Box::new(Expr::Literal(Literal {
                                value: Value::Bool(true),
                                expr_type: ScalarType::Boolean,
                            })),
                        },
                    );
                }
                _ => {}
            }
            let (query, _) = field_is_not_null(ctx.schema, f)?;
            // Always re-run the filter in the local planner, because tantivy won't distinguish between non-null and a
            // truthy value
            (query, Some(Box::new(filter.clone())))
        }
        Expr::Unary { op, expr } if matches!(op, UnaryOp::IsNotNull | UnaryOp::IsNull) => {
            if let Expr::Field(f) = expr.as_ref() {
                match (op, field_is_not_null(ctx.schema, f)?) {
                    (UnaryOp::IsNull, (pushed, None)) => (
                        Box::new(OptimizedTantivySearch::BooleanQuery(vec![
                            (BooleanQueryOp::MustNot, *pushed),
                            // https://github.com/quickwit-oss/tantivy/issues/2317
                            // This is a bug/quirk in tantivy -- for MustNot to work, we need to add
                            // a Must clause as well.
                            (BooleanQueryOp::Must, OptimizedTantivySearch::AllQuery),
                        ])),
                        None,
                    ),
                    (UnaryOp::IsNotNull, (pushed, None)) => (pushed, None),
                    _ => (
                        Box::new(OptimizedTantivySearch::AllQuery),
                        Some(Box::new(filter.clone())),
                    ),
                }
            } else {
                // Fallback to running the query locally
                return Ok((
                    Box::new(OptimizedTantivySearch::AllQuery),
                    Some(Box::new(filter.clone())),
                ));
            }
        }
        Expr::Unary {
            op: UnaryOp::Not,
            expr,
        } => {
            let (query, extra_filter) = push_tantivy_search_plan(ctx, expr)?;
            match (query.as_ref(), extra_filter.as_ref()) {
                (_, Some(_)) => {
                    // If we couldn't push down the full query, we can't prove that the part we are pushing down is
                    // constrained enough. If it's too lax of a filter, the negating it will be too constraining.
                    return Ok((
                        Box::new(OptimizedTantivySearch::AllQuery),
                        Some(Box::new(filter.clone())),
                    ));
                }
                _ => {}
            };

            // Otherwise, can negate both sides
            // This is a tantivy quirk (https://github.com/quickwit-oss/tantivy/issues/2317), explained above too
            let negated_pushed = Box::new(OptimizedTantivySearch::BooleanQuery(vec![
                (BooleanQueryOp::MustNot, *query),
                (BooleanQueryOp::Must, OptimizedTantivySearch::AllQuery),
            ]));

            // Always re-run the negative filter in the local planner, because tantivy won't distinguish between null and a
            // falsey value
            let negated_extra = Some(Box::new(filter.clone()));

            (negated_pushed, negated_extra)
        }
        _ => (
            Box::new(OptimizedTantivySearch::AllQuery),
            Some(Box::new(filter.clone())),
        ),
    })
}

fn field_is_not_null(
    schema: &util::schema::Schema,
    field: &btql::binder::ast::Field,
) -> Result<(Box<OptimizedTantivySearch>, Option<Box<binder::ast::Expr>>)> {
    let top_level_field = schema.must_find(extract_top_level_key(&field.name)?)?;
    let stored_field = top_level_field.must_find(TantivyFieldFilterOpts::new().is_stored())?;

    // If it's a non-json fast field, we can just run an exists query on it
    match &stored_field.field_type {
        util::schema::TantivyType::Json(..) => {
            // If it's a json field, we need to run a JSONExistsQuery
            if matches!(
                top_level_field
                    .must_find(TantivyFieldFilterOpts::new().is_stored())?
                    .field_type,
                util::schema::TantivyType::Json(TextOptions {
                    tokenize: false,
                    ..
                })
            ) {
                let mut must_rerun = false;
                let mut json_path = Vec::new();

                // Process path components until we hit a negative index, since paths
                // containing negative indices won't match the JSON_PATHS field.
                for component in field.name[1..].iter() {
                    match component {
                        PathPiece::Key(s) => json_path.push(s.clone()),
                        PathPiece::Index(i) => {
                            if *i < 0 {
                                must_rerun = true;
                                break;
                            }
                            json_path.push(i.to_string());
                        }
                    }
                }

                let extra_filter = if must_rerun {
                    Some(Box::new(Expr::Unary {
                        op: UnaryOp::IsNotNull,
                        expr: Box::new(Expr::Field(field.clone())),
                    }))
                } else {
                    None
                };

                return Ok((
                    Box::new(OptimizedTantivySearch::JSONExistsQuery {
                        field: stored_field.clone(),
                        json_path,
                    }),
                    extra_filter,
                ));
            }
        }
        _ if field.name.len() == 1 => {
            // If it's a single-path non-JSON fast-field, we can run an exists query on it
            if let Some(fast_field) =
                top_level_field.find_tantivy_field(TantivyFieldFilterOpts::new().is_fast())
            {
                {
                    return Ok((
                        Box::new(OptimizedTantivySearch::ExistsQuery {
                            field: fast_field.clone(),
                        }),
                        None,
                    ));
                }
            }
        }
        _ => {}
    };

    return Ok((
        Box::new(OptimizedTantivySearch::AllQuery),
        Some(Box::new(Expr::Unary {
            op: UnaryOp::IsNotNull,
            expr: Box::new(Expr::Field(field.clone())),
        })),
    ));
}

fn extract_field_and_literal<'a>(
    left: &'a Expr,
    right: &'a Expr,
) -> Option<(
    &'a btql::binder::ast::Field,
    btql::binder::ast::Literal,
    bool,
)> {
    let mut field: Option<&btql::binder::ast::Field> = None;
    let mut literal: Option<&btql::binder::ast::Literal> = None;
    let mut left_is_field = false;
    match left {
        Expr::Field(f) => {
            field = Some(f);
            left_is_field = true;
        }
        Expr::Literal(l) => literal = Some(l),
        _ => {}
    };
    match right {
        Expr::Field(f) => {
            if let Some(_) = field {
                return None;
            }
            field = Some(f);
            left_is_field = false;
        }
        Expr::Literal(l) => {
            if let Some(_) = literal {
                return None;
            }
            literal = Some(l);
        }
        _ => {}
    }

    let field = field?;
    let mut literal = normalize_literal_for_comparison(literal?).ok()?;

    if field.name.len() == 1
        && field.name[0] == PathPiece::Key(PAGINATION_KEY_FIELD.to_string())
        && matches!(literal.expr_type, ScalarType::String)
    {
        let pagination_key: PaginationKey = match serde_json::from_value(literal.value.clone()) {
            Ok(pagination_key) => pagination_key,
            Err(e) => {
                log::warn!("invalid pagination key {}: {}", literal.value, e);
                return None;
            }
        };
        literal.value = pagination_key.0.into();
    }

    Some((field, literal, left_is_field))
}

fn tantivy_type_can_be_tokenized(t: &util::schema::TantivyType) -> bool {
    matches!(t, util::schema::TantivyType::Str(..))
        || matches!(t, util::schema::TantivyType::Json(..))
}

pub fn schema_type_to_tantivy_type(t: &util::schema::TantivyType) -> tantivy::schema::Type {
    match t {
        util::schema::TantivyType::Str(..) => tantivy::schema::Type::Str,
        util::schema::TantivyType::U64(..) => tantivy::schema::Type::U64,
        util::schema::TantivyType::I64(..) => tantivy::schema::Type::I64,
        util::schema::TantivyType::F64(..) => tantivy::schema::Type::F64,
        util::schema::TantivyType::Bool(..) => tantivy::schema::Type::Bool,
        util::schema::TantivyType::Date(..) => tantivy::schema::Type::Date,
        util::schema::TantivyType::Facet(..) => tantivy::schema::Type::Facet,
        util::schema::TantivyType::Bytes(..) => tantivy::schema::Type::Bytes,
        util::schema::TantivyType::Json(..) => tantivy::schema::Type::Json,
        util::schema::TantivyType::IpAddr(..) => tantivy::schema::Type::IpAddr,
    }
}

fn create_terms_query(
    tantivy_field: &util::schema::TantivyField,
    json_path: &[String],
    literal: &binder::ast::Literal,
) -> Result<Vec<TermQuery>> {
    let mut terms = vec![];
    match tantivy_field.field_type {
        util::schema::TantivyType::Json(..) => {
            // See https://github.com/quickwit-oss/tantivy/blob/0.22.0/src/core/json_utils.rs#L461
            // for how this works. The basic idea is that we have to prepend the type and then the value,
            // and expand non-strings to be OR'd together.

            use util::serde_json::Value;

            terms.push(TermQuery {
                field: tantivy_field.clone(),
                json_path: json_path.to_vec(),
                tantivy_type: tantivy::schema::Type::Str,
                value: literal.value.clone(),
            });
            match &literal.value {
                Value::String(s) => {
                    // Just use the text term
                    if let Ok(datetime_value) = CastInto::<tantivy::DateTime>::cast(s) {
                        terms.push(TermQuery {
                            field: tantivy_field.clone(),
                            json_path: json_path.to_vec(),
                            tantivy_type: tantivy::schema::Type::Date,
                            value: Value::String(datetime_value.cast()?), // Normalize and cast it back to a string
                        });
                    }
                    if let Ok(_) = CastInto::<i64>::cast(s) {
                        terms.push(TermQuery {
                            field: tantivy_field.clone(),
                            json_path: json_path.to_vec(),
                            tantivy_type: tantivy::schema::Type::I64,
                            value: literal.value.clone(),
                        });
                    } else if let Ok(_) = CastInto::<u64>::cast(s) {
                        terms.push(TermQuery {
                            field: tantivy_field.clone(),
                            json_path: json_path.to_vec(),
                            tantivy_type: tantivy::schema::Type::U64,
                            value: literal.value.clone(),
                        });
                    }
                    if let Ok(_) = CastInto::<f64>::cast(s) {
                        terms.push(TermQuery {
                            field: tantivy_field.clone(),
                            json_path: json_path.to_vec(),
                            tantivy_type: tantivy::schema::Type::F64,
                            value: literal.value.clone(),
                        });
                    }
                }
                Value::Number(n) => {
                    if let Some(_int_value) = n.as_i64() {
                        terms.push(TermQuery {
                            field: tantivy_field.clone(),
                            json_path: json_path.to_vec(),
                            tantivy_type: tantivy::schema::Type::I64,
                            value: literal.value.clone(),
                        });
                    } else if let Some(_uint_value) = n.as_u64() {
                        terms.push(TermQuery {
                            field: tantivy_field.clone(),
                            json_path: json_path.to_vec(),
                            tantivy_type: tantivy::schema::Type::U64,
                            value: literal.value.clone(),
                        });
                    }

                    if let Some(_float_value) = n.as_f64() {
                        terms.push(TermQuery {
                            field: tantivy_field.clone(),
                            json_path: json_path.to_vec(),
                            tantivy_type: tantivy::schema::Type::F64,
                            value: literal.value.clone(),
                        });
                    }
                }
                Value::Bool(_b) => {
                    terms.push(TermQuery {
                        field: tantivy_field.clone(),
                        json_path: json_path.to_vec(),
                        tantivy_type: tantivy::schema::Type::Bool,
                        value: literal.value.clone(),
                    });
                }
                _ => {
                    return Err(OptimizerError::Unsupported {
                        name: format!("unsupported value in literal comparison: {:?}", literal),
                        op: None,
                    });
                }
            };
        }
        _ => {
            if json_path.len() > 0 {
                return Err(OptimizerError::InvalidSchema {
                    msg: format!(
                        "json path not supported on non-json fields: {:?}",
                        json_path
                    ),
                });
            }
            terms.push(TermQuery {
                field: tantivy_field.clone(),
                json_path: json_path.to_vec(),
                tantivy_type: schema_type_to_tantivy_type(&tantivy_field.field_type),
                value: literal.value.clone(),
            });
        }
    };

    Ok(terms)
}

// Technically, if the prefix can be pushed down, then we can push the whole thing down, but
// would need to be careful about the limit (e.g. prob cannot push it down and would need to
// fetch multiple batches until we exhaust all values with a given prefix).
fn push_tantivy_sort_plan(
    schema: &util::schema::Schema,
    sort: &Vec<binder::ast::SortItem>,
    projection: &binder::ast::Projection,
) -> Result<
    Option<(
        binder::ast::SortDirection,
        util::schema::TantivyField,
        RequiredTantivyFieldMap,
    )>,
> {
    if sort.len() != 1 {
        return Ok(None);
    }

    let sort_expr = match &sort[0].expr {
        binder::ast::SortExpr::Alias(alias) => {
            match projection {
                Projection::Flat { select } => {
                    let expr = match select.iter().find(|s| s.alias == *alias) {
                        Some(s) => &s.expr,
                        None => {
                            return Err(OptimizerError::InvalidSchema {
                                msg: format!("sort alias not found: {}", alias),
                            });
                        }
                    };

                    Cow::Borrowed(expr)
                }
                Projection::Infer { infer } => {
                    let expr = match infer
                        .iter()
                        .find(|s| s.name.len() == 1 && s.name[0] == PathPiece::Key(alias.clone()))
                    {
                        Some(s) => Box::new(Expr::Field(s.clone())),
                        None => {
                            return Err(OptimizerError::InvalidSchema {
                                msg: format!("sort alias not found: {}", alias),
                            });
                        }
                    };

                    Cow::Owned(expr)
                }
                Projection::GroupBy { .. } => {
                    // TODO: GROUP BY + SORT pushdown
                    log::debug!("pushing down sort on group by is not supported");
                    return Ok(None);
                }
            }
        }
        binder::ast::SortExpr::Expr(expr) => Cow::Borrowed(expr),
    };

    match sort_expr.as_ref().as_ref() {
        binder::ast::Expr::Field(f) => {
            if f.name.len() != 1 {
                log::debug!("sort by nested field cannot be pushed down");
                return Ok(None);
            }
            let field = schema.must_find(extract_top_level_key(&f.name)?)?;
            match field.find_tantivy_field(TantivyFieldFilterOpts::new().is_fast()) {
                Some(tantivy_field) => {
                    if tantivy_field.field_type.fast_field_value_type().is_none() {
                        log::debug!("cannot push down sort on non-fast-value field");
                        return Ok(None);
                    }
                    Ok(Some((
                        sort[0].dir.clone(),
                        tantivy_field.clone(),
                        find_required_tantivy_fields(&sort_expr),
                    )))
                }
                None => {
                    log::debug!("cannot push down sort on non-fast field");
                    Ok(None)
                }
            }
        }
        _ => {
            log::debug!("sort by expression is not supported");
            return Ok(None);
        }
    }
}

pub trait MustFindField {
    fn must_find<'a>(&'a self, name: &str) -> Result<&'a util::schema::Field>;
}

impl MustFindField for util::schema::Schema {
    fn must_find<'a>(&'a self, name: &str) -> Result<&'a util::schema::Field> {
        self.find_field(name).ok_or(OptimizerError::InvalidSchema {
            msg: format!("field not found: {}", name),
        })
    }
}

pub trait MustFindTantivyField {
    fn must_find<'a>(
        &'a self,
        opts: TantivyFieldFilterOpts,
    ) -> Result<&'a util::schema::TantivyField>;
}

impl MustFindTantivyField for util::schema::Field {
    fn must_find<'a>(
        &'a self,
        opts: TantivyFieldFilterOpts,
    ) -> Result<&'a util::schema::TantivyField> {
        self.find_tantivy_field(opts.clone())
            .ok_or(OptimizerError::InvalidSchema {
                msg: format!("tantivy field not found: {:?}", opts),
            })
    }
}

fn replace_fields_with_aliases(expr: Expr) -> Expr {
    match expr {
        Expr::Field(f) => Expr::Field(binder::ast::Field::new(
            vec![PathPiece::Key(make_field_name(&f.name))],
            f.expr_type,
            f.source,
        )),
        _ => expr,
    }
}

// Sorts are a bit tricky because an alias will refer to a field in the projected result, so we need to
// also replace it.
fn apply_replace_fields_with_aliases_to_sort_expr(
    expr: binder::ast::SortItem,
    projection: Option<&Vec<binder::ast::Alias>>,
) -> Result<OptimizedSortItem> {
    Ok(OptimizedSortItem {
        dir: expr.dir,
        expr: match expr.expr {
            binder::ast::SortExpr::Alias(alias) => match projection {
                None => Box::new(Expr::Field(binder::ast::Field::new(
                    vec![PathPiece::Key(make_field_name(&[PathPiece::Key(
                        alias.clone(),
                    )]))],
                    util::Value::Object(serde_json::Map::new()),
                    None,
                ))),
                Some(projection) => projection
                    .iter()
                    .find(|projection| projection.alias == alias.as_str())
                    .ok_or(OptimizerError::InvalidBindExpr {
                        msg: format!("alias not found: {:?}", alias),
                        op: None,
                    })?
                    .expr
                    .clone(),
            },
            binder::ast::SortExpr::Expr(expr) => {
                Box::new((*expr).transform(&mut replace_fields_with_aliases))
            }
        },
    })
}

pub fn make_field_name(field: &[PathPiece]) -> String {
    field
        .iter()
        .map(|s| match s {
            PathPiece::Key(field) => field.clone().replace("_", "__"),
            // TODO(austin): Not sure this is the right way to do this
            PathPiece::Index(index) => format!("[{}]", index),
        })
        .fold(String::new(), |mut acc, s| {
            if !acc.is_empty() {
                acc.push('_');
            }
            acc.push_str(&s);
            acc
        })
}

fn make_unpivot_exprs(
    unpivot: &Vec<binder::ast::UnpivotExpr>,
    unpivot_fields: &Vec<Vec<binder::ast::Field>>,
    unpivot_tantivy_projection_names: &Vec<String>,
) -> Result<Vec<UnpivotProjectionExpr>> {
    // For each unpivot, create the appropriate UnpivotProjectionExpr
    let mut unpivot_exprs = Vec::new();
    for (i, fields) in unpivot_fields.iter().enumerate() {
        if fields.len() == 0 {
            // This unpivot was never referenced
            continue;
        }
        let unpivot_expr = match unpivot[i].unpivot_type {
            UnpivotType::Array => {
                if fields.len() != 1 {
                    continue;
                }
                if !matches!(
                    fields[0].source,
                    Some(UnpivotSource {
                        source_type: UnpivotSourceType::Element,
                        ..
                    })
                ) {
                    return Err(OptimizerError::InvalidBindExpr {
                        msg: "Unpivoted array expected to an Element unpivot source".to_string(),
                        op: Some(unpivot[i].expr.clone()),
                    });
                }
                UnpivotProjectionExpr {
                    base_field: unpivot_tantivy_projection_names[i].clone(),
                    projected_field: UnpivotProjectedField::Array {
                        item: make_field_name(&fields[0].name),
                    },
                }
            }
            UnpivotType::Object => {
                let empty_name = vec![PathPiece::Key("_".to_string())];
                let key_field = fields
                    .iter()
                    .find(|f| {
                        matches!(
                            f.source,
                            Some(UnpivotSource {
                                source_type: UnpivotSourceType::Key,
                                ..
                            })
                        )
                    })
                    .map(|f| &f.name)
                    .unwrap_or(&empty_name);

                let value_field = fields
                    .iter()
                    .find(|f| {
                        matches!(
                            f.source,
                            Some(UnpivotSource {
                                source_type: UnpivotSourceType::Value,
                                ..
                            })
                        )
                    })
                    .map(|f| &f.name)
                    .unwrap_or(&empty_name);

                UnpivotProjectionExpr {
                    base_field: unpivot_tantivy_projection_names[i].clone(),
                    projected_field: UnpivotProjectedField::Object {
                        key: make_field_name(key_field),
                        value: make_field_name(value_field),
                    },
                }
            }
        };
        unpivot_exprs.push(unpivot_expr);
    }

    Ok(unpivot_exprs)
}

fn make_unpivot_plan(
    search_plan: Box<OptimizedPlan>,
    unpivot: &Vec<binder::ast::UnpivotExpr>,
    select: &Vec<binder::ast::Alias>,
    unpivot_tantivy_projection_names: &Vec<String>,
) -> Result<Box<OptimizedPlan>> {
    let mut unpivot_fields = vec![vec![]; unpivot.len()];
    let mut add_unpivot_field = |expr: &Expr| {
        if let Expr::Field(field) = expr {
            if let Some(source) = &field.source {
                unpivot_fields[source.unpivot as usize].push(field.clone());
            }
        }
    };
    for item in select.iter() {
        item.expr.traverse(&mut add_unpivot_field);
    }

    Ok(Box::new(OptimizedPlan::Unpivot(UnpivotPlan {
        from: search_plan,
        unpivot: make_unpivot_exprs(unpivot, &unpivot_fields, unpivot_tantivy_projection_names)?,
    })))
}

fn normalize_literal_for_comparison(literal: &Literal) -> Result<Literal> {
    let value = normalize_value_for_comparison(literal.value.clone());
    let (normalized_value, scalar_type) = normalize_to_scalar_type(Cow::Borrowed(&value))?;
    Ok(Literal {
        value: normalized_value.into_owned(),
        expr_type: scalar_type,
    })
}

fn like_value_is_not_a_string_value(value: &str) -> bool {
    let stripped = value
        .strip_prefix("%")
        .unwrap_or(value)
        .strip_suffix("%")
        .unwrap_or(value);
    phrase_is_not_a_string_value(stripped)
}

// This function mimics https://github.com/quickwit-oss/tantivy/blob/17d5869ad61ea9f1072ef40546ddcada3a14b067/src/core/json_utils.rs#L260
// and attempts to determine if a phrase is a JSON value of a particular type. If so, we can assume it may have been tokenized as that
// type of value.
fn phrase_is_not_a_string_value(phrase: &str) -> bool {
    if let Ok(_) = tantivy::time::OffsetDateTime::parse(
        phrase,
        &tantivy::time::format_description::well_known::Rfc3339,
    ) {
        return true;
    }
    if let Ok(_) = str::parse::<i64>(phrase) {
        return true;
    }
    if let Ok(_) = str::parse::<u64>(phrase) {
        return true;
    }
    if let Ok(_) = str::parse::<f64>(phrase) {
        return true;
    }
    if let Ok(_) = str::parse::<bool>(phrase) {
        return true;
    }
    false
}

fn fold_aliases(expr_ctx: &ExprContext, aliases: Vec<Alias>) -> Result<Vec<Alias>> {
    aliases
        .into_iter()
        .map(|alias| {
            let folded_expr = constant_fold_expr(expr_ctx, &alias.expr)
                .map_err(|e| OptimizerError::Anyhow(e.into()))?;
            Ok(Alias {
                expr: Box::new(folded_expr),
                alias: alias.alias,
            })
        })
        .collect()
}

fn constant_fold_projection(expr_ctx: &ExprContext, projection: Projection) -> Result<Projection> {
    Ok(match projection {
        Projection::GroupBy {
            dimensions,
            pivot,
            measures,
        } => Projection::GroupBy {
            dimensions: fold_aliases(expr_ctx, dimensions)?,
            pivot: fold_aliases(expr_ctx, pivot)?,
            measures: fold_aliases(expr_ctx, measures)?,
        },
        Projection::Flat { select } => Projection::Flat {
            select: fold_aliases(expr_ctx, select)?,
        },
        // Nothing to constant fold here (it's a list of fields)
        Projection::Infer { infer } => Projection::Infer { infer },
    })
}

fn make_fast_field_range_query(
    schema_field: &util::schema::Field,
    json_path: &[String],
    literal_type: ScalarType,
    lower: &Bound<Value>,
    upper: &Bound<Value>,
) -> Result<Option<Box<OptimizedTantivySearch>>> {
    let fast_field = match schema_field.find_tantivy_field(TantivyFieldFilterOpts::new().is_fast())
    {
        Some(f) => f,
        None => {
            return Ok(None);
        }
    };

    // If we're comparing a JSON type to a number, then we need to try both the integer and floating point
    // types. extra_type allows us to track that.
    let mut extra_type = Vec::new();

    let tantivy_type = match fast_field.field_type {
        util::schema::TantivyType::Bool(..) => tantivy::schema::Type::Bool,
        util::schema::TantivyType::U64(..) => tantivy::schema::Type::U64,
        util::schema::TantivyType::I64(..) => tantivy::schema::Type::I64,
        util::schema::TantivyType::F64(..) => tantivy::schema::Type::F64,
        util::schema::TantivyType::Date(..) => tantivy::schema::Type::Date,
        util::schema::TantivyType::Json(..) => match literal_type {
            ScalarType::Boolean => tantivy::schema::Type::Bool,
            ScalarType::Integer => {
                extra_type.push(tantivy::schema::Type::F64);
                tantivy::schema::Type::I64
            }
            ScalarType::Number => {
                extra_type.push(tantivy::schema::Type::I64);
                tantivy::schema::Type::F64
            }
            ScalarType::Date => tantivy::schema::Type::Date,
            ScalarType::DateTime => tantivy::schema::Type::Date,
            _ => return Ok(None),
        },
        _ => return Ok(None),
    };

    let expr = Box::new(OptimizedTantivySearch::RangeQuery {
        field: fast_field.clone(),
        json_path: json_path.to_vec(),
        tantivy_type,
        lower: lower.clone(),
        upper: upper.clone(),
        columnar: true,
    });

    if !extra_type.is_empty() {
        let mut exprs = vec![*expr];
        for type_ in extra_type {
            exprs.push(OptimizedTantivySearch::RangeQuery {
                field: fast_field.clone(),
                json_path: json_path.to_vec(),
                tantivy_type: type_,
                lower: lower.clone(),
                upper: upper.clone(),
                columnar: true,
            });
        }
        return Ok(Some(Box::new(OptimizedTantivySearch::BooleanQuery(
            exprs
                .into_iter()
                .map(|e| (BooleanQueryOp::Should, e))
                .collect(),
        ))));
    }

    Ok(Some(expr))
}

fn make_inverted_index_range_query(
    schema_field: &util::schema::Field,
    json_path: &[String],
    literal: &Literal,
    op: ComparisonOp,
) -> Result<Option<Box<OptimizedTantivySearch>>> {
    // We can't use tokenized fields, because a range query "matches all documents that have at least one term within a defined range",
    // but we only want to match documents where the whole value is within the range.
    let indexed_field =
        match schema_field.find_tantivy_field(TantivyFieldFilterOpts::new().is_not_tokenized()) {
            Some(f) => f,
            None => {
                return Ok(None);
            }
        };

    let terms = create_terms_query(indexed_field, json_path, literal)?;

    if terms.len() > 1 || json_path.len() > 0 {
        // If we more than one type, then we can't check against the inverted index because one of the types
        // will match a lot of rows. SImilarly, JSON doesn't quite work.
        return Ok(None);
    }

    let mut range_queries = terms
        .into_iter()
        .map(|t| {
            let (lower, upper) = match op {
                ComparisonOp::Lt => (Bound::Unbounded, Bound::Excluded(t.value)),
                ComparisonOp::Le => (Bound::Unbounded, Bound::Included(t.value)),
                ComparisonOp::Gt => (Bound::Excluded(t.value), Bound::Unbounded),
                ComparisonOp::Ge => (Bound::Included(t.value), Bound::Unbounded),
                _ => unreachable!("Should not call rane query builder with non-comparison op"),
            };

            OptimizedTantivySearch::RangeQuery {
                field: t.field,
                json_path: t.json_path,
                tantivy_type: t.tantivy_type,
                lower,
                upper,
                columnar: false,
            }
        })
        .collect::<Vec<_>>();

    Ok(if range_queries.len() == 1 {
        Some(Box::new(range_queries.remove(0)))
    } else if true {
        Some(Box::new(range_queries.remove(0)))
    } else {
        Some(Box::new(OptimizedTantivySearch::BooleanQuery(
            range_queries
                .into_iter()
                .map(|e| (BooleanQueryOp::Should, e))
                .collect(),
        )))
    })
}

fn extract_top_level_key(field_path: &[PathPiece]) -> Result<&str> {
    match &field_path[0] {
        PathPiece::Key(name) => Ok(name),
        PathPiece::Index(_) => Err(OptimizerError::InvalidSchema {
            msg: "first component of field name must be a string".to_string(),
        }),
    }
}

impl CursorField {
    pub fn from_sort_alias(alias: &str) -> Option<Self> {
        match alias {
            PAGINATION_KEY_FIELD => Some(CursorField::PaginationKey),
            XACT_ID_FIELD => Some(CursorField::TransactionId),
            _ => None,
        }
    }

    pub fn get_field_name(&self) -> &'static str {
        match self {
            CursorField::PaginationKey => PAGINATION_KEY_FIELD,
            CursorField::TransactionId => XACT_ID_FIELD,
        }
    }
}
