use btql::{interpreter::expr::order_values, schema::ScalarType};
use itertools::Itertools;
use lazy_static::lazy_static;
use std::{
    cmp::{Ordering, Reverse},
    collections::{BTreeMap, BinaryHeap, HashSet, VecDeque},
    sync::Arc,
};

use storage::{
    hash_map::{raw_entry_mut_from_hash, HashMap as HashBrownMap, RawEntryCompatible, RawEntryMut},
    index_wal_reader::{IndexWalReaderInput, IndexWalReaderOptionalInput, IndexWalReaderOpts},
    tantivy_index::{make_tantivy_schema, JSON_ROOT_FIELD},
};
use tantivy::{
    collector::{Collector, SegmentCollector},
    columnar::{DynamicColumn, MonotonicallyMappableToU64},
    directory::DirectoryClone,
    query::EnableScoring,
    schema::{IndexRecordOption, OwnedValue, JSO<PERSON>_END_OF_PATH},
    DateTime, DocSet, Executor, InvertedIndexReader, SegmentOrdinal, SegmentReader,
};
use tokio::{runtime::Handle, sync::mpsc};
use util::{
    anyhow::Context,
    json::PathPiece,
    schema::TantivyType,
    spawn_blocking_util::spawn_blocking_with_async_timeout,
    tracer::{trace_if, trace_if_async, EnterTraceGuard, TracedNode},
    Value,
};

use crate::interpreter::{
    context::InterpreterContext,
    error::{InterpreterError, Result},
    op::{Operator, StreamValue},
};
use crate::{
    interpreter::{
        context::InterpreterContextState,
        op::{send_row, ShouldAbort},
        tantivy::columnstore::collect_column_for_doc_batch,
    },
    optimizer::tantivy::make_field_name,
    planner::{
        ast::{TantivyInferenceField, TantivySchemaInferenceQuery},
        JSON_PATH_SEGMENT_SEP,
    },
};
use btql::typesystem::CastInto;

pub use storage::process_wal::PAGINATION_KEY_FIELD;

use super::{
    aggregate::get_fast_fields_impl,
    columnstore::{column_to_type, get_columns_matching_prefix},
    search::{convert_tantivy_value, StatisticsProvider},
    summary::{make_preview_field, PREVIEW_FIELD_LENGTH},
};
lazy_static! {
    pub static ref PAGINATION_KEY_FLAT: String =
        make_field_name(&[PathPiece::Key(PAGINATION_KEY_FIELD.to_string())]);
}

pub const MAX_INFERENCE_SEGMENTS: usize = 2;

// TODO:
// - Support realtime

#[async_trait::async_trait]
impl Operator for TantivySchemaInferenceQuery {
    fn name(&self) -> &'static str {
        "Index schema inference"
    }

    async fn execute(
        self,
        ctx: Arc<InterpreterContext>,
        tracer: Option<Arc<TracedNode>>,
        tx: mpsc::Sender<StreamValue>,
    ) -> Result<()> {
        let span = tracing::Span::current();

        let index_wal_reader = trace_if_async(
            log::Level::Info,
            &tracer,
            "Open index wal reader",
            |child| {
                storage::index_wal_reader::IndexWalReader::new(
                    IndexWalReaderInput {
                        config_with_store: &ctx.config,
                        full_schema: ctx.schema.clone(),
                        object_ids: &self.object_ids,
                        filters: &self.segment_filters,
                        sort: &self.segment_sort,
                        partition_all: false,
                    },
                    IndexWalReaderOptionalInput {
                        tantivy_executor: Some(ctx.executor.clone()),
                        tracer: child,
                        ..Default::default()
                    },
                    IndexWalReaderOpts {
                        initial_segment_batch_size: Some(MAX_INFERENCE_SEGMENTS),
                        skip_realtime_wal_entries: Some(true),
                        ..ctx.opts.index_wal_reader_opts
                    },
                )
            },
        )
        .await?;

        let has_filter = if let Some(_) = self
            .search
            .as_ref()
            .downcast_ref::<tantivy::query::AllQuery>()
        {
            false
        } else {
            true
        };

        let search_query = index_wal_reader.wrap_exclude_query(self.search);
        let schema = make_tantivy_schema(&index_wal_reader.schema())
            .context("Failed to create tantivy schema from index wal reader schema")?
            .schema;
        let handle = ctx.handle.clone();

        spawn_blocking_with_async_timeout(
            &handle,
            move || {
                let _tracer_guard = tracer.as_ref().map(|tracer| tracer.enter());
                let _guard = span.enter();

                let mut remaining_docs = match &self.limit {
                    Some(limit) => *limit as i64,
                    None => i64::MAX,
                };

                let mut tantivy_readers = vec![];
                let (directory, search_query) = index_wal_reader.directory_with_sort_filters(
                    0,
                    search_query.box_clone(),
                    self.sort.as_ref(),
                );
                directory.load_segment_footer_blocking(tracer.clone())?;

                let mut tantivy_index = tantivy::Index::open(directory.box_clone())?;
                tantivy_index.set_shared_multithread_executor(ctx.executor.clone())?;

                let reader = trace_if(log::Level::Info, &tracer, "Open reader", |child| {
                    child.increment_counter(
                        "num_chunks",
                        tantivy_index.searchable_segment_metas()?.len() as u64,
                    );
                    tantivy_index.reader()
                })?;
                let searcher = reader.searcher();

                tantivy_readers.push(reader);

                ctx.set_index_state(InterpreterContextState {
                    index_wal_reader: index_wal_reader.clone(),
                    tantivy_schema: tantivy_index.schema().clone(),
                    tantivy_index: tantivy_index.clone(),
                    tantivy_readers: tantivy_readers.clone(),
                });

                let tracer = tracer
                    .as_ref()
                    .map(|tracer| tracer.new_child("Run inference"));
                let _guard = tracer.as_ref().map(|tracer| tracer.enter());

                let docs = trace_if(log::Level::Info, &tracer, "Search and infer", |child| {
                    let schema_inference_collector = SchemaInferenceCollector {
                        tracer: child,
                        projected_fields: self.infer.clone(),
                        schema: schema.clone(),
                        // As much as I'd like to parallelize this, it's probably better to parallelize
                        // the segments themselves. If we use the same pool for both, we run the
                        // risk of deadlocking.
                        executor: Arc::new(Executor::SingleThread),
                        has_filter,
                    };

                    let stats_provider = StatisticsProvider(&searcher, &ctx.executor);

                    let enabled_scoring = if schema_inference_collector.requires_scoring() {
                        EnableScoring::enabled_from_statistics_provider(&stats_provider, &searcher)
                    } else {
                        EnableScoring::disabled_from_searcher(&searcher)
                    };

                    searcher.search_with_executor(
                        &search_query,
                        &schema_inference_collector,
                        &ctx.executor,
                        enabled_scoring,
                    )
                })?;

                let mut buckets = Vec::new();
                for (field, (field_type, field_map)) in docs.into_iter() {
                    match field_map {
                        FieldMap::Primitive(values) => {
                            let bucket = SchemaInferenceBucket::new_from_terms(values, field_type)?;
                            buckets.push((vec![field], bucket));
                        }
                        FieldMap::Value(values) => {
                            let bucket =
                                SchemaInferenceBucket::new_from_values(values, vec![field_type])?;
                            buckets.push((vec![field], bucket));
                        }
                        FieldMap::JsonObject(values) => {
                            let mut path_buckets = BTreeMap::new();
                            for (path, heap) in values.into_iter() {
                                let mut field_name = vec![field.to_string()];
                                field_name
                                    .extend(parse_json_path(path.0[..path.0.len() - 2].to_vec()));

                                let field_type = match tantivy::schema::Type::from_code(
                                    path.0[path.0.len() - 1],
                                ) {
                                    Some(field_type) => field_type,
                                    None => {
                                        return Err(InterpreterError::InternalError(format!(
                                            "Invalid field type code: {}",
                                            path.0[path.0.len() - 1]
                                        )))
                                    }
                                };

                                let effective_field_type =
                                    match (field_type, ctx.inference_collapse_numbers) {
                                        (
                                            tantivy::schema::Type::U64 | tantivy::schema::Type::I64,
                                            true,
                                        ) => tantivy::schema::Type::F64,
                                        (field_type, _) => field_type,
                                    };

                                path_buckets
                                    .entry(field_name)
                                    .or_insert_with(|| Vec::new())
                                    .push(
                                        (
                                            effective_field_type,
                                            heap.into_iter()
                                                .map(|Reverse((count, value))| {
                                                    Ok(Reverse((
                                                        count,
                                                        InferenceValue(parse_tantivy_term(
                                                            value, field_type,
                                                        )?),
                                                    )))
                                                })
                                                .collect::<Result<
                                                    BinaryHeap<Reverse<(u64, InferenceValue)>>,
                                                >>(
                                                )?,
                                        ),
                                    );
                            }

                            buckets.extend(merge_path_buckets(path_buckets)?);
                        }
                        FieldMap::JsonValue(values) => {
                            let mut path_buckets = BTreeMap::new();
                            for ((path, field_type), heap) in values.into_iter() {
                                let mut field_name = vec![field.to_string()];
                                field_name.extend(parse_json_path(path.0));

                                let effective_field_type =
                                    match (field_type, ctx.inference_collapse_numbers) {
                                        (tantivy::schema::Type::U64, true) => {
                                            tantivy::schema::Type::F64
                                        }
                                        (tantivy::schema::Type::I64, true) => {
                                            tantivy::schema::Type::F64
                                        }
                                        (field_type, _) => field_type,
                                    };

                                path_buckets
                                    .entry(field_name)
                                    .or_insert_with(|| Vec::new())
                                    .push((effective_field_type, heap));
                            }

                            buckets.extend(merge_path_buckets(path_buckets)?);
                        }
                    }
                }
                let mut rows = Vec::new();
                for (field_name, bucket) in buckets {
                    rows.push(bucket.into_value(field_name));
                }

                let mut final_rows = VecDeque::from(rows);
                tracer.increment_counter("num_output", final_rows.len() as u64);

                match Handle::current().block_on(async {
                    while final_rows.len() > 0 {
                        let row = final_rows.pop_front().unwrap();

                        match send_row(&tx, row).await {
                            ShouldAbort::Continue => {}
                            ShouldAbort::Abort => {
                                return ShouldAbort::Abort;
                            }
                        }
                        remaining_docs -= 1;
                        if remaining_docs <= 0 {
                            return ShouldAbort::Abort;
                        }
                    }
                    ShouldAbort::Continue
                }) {
                    ShouldAbort::Continue => {}
                    ShouldAbort::Abort => {
                        log::debug!(
                            "Aborting tantivy search since the parent stream was cancelled"
                        );
                        return Ok(());
                    }
                }

                Ok(())
            },
            Default::default(),
            || "execute tantivy search".into(),
        )
        .await
        .context("Failed to join")??
    }
}

#[derive(Debug)]
struct SchemaInferenceBucket {
    pub values: Vec<(Value, u64)>,
    pub types: Vec<ScalarType>,
}

impl SchemaInferenceBucket {
    fn new_from_terms(
        values: BinaryHeap<Reverse<(u64, Vec<u8>)>>,
        value_type: tantivy::schema::Type,
    ) -> Result<Self> {
        Ok(Self {
            values: Self::sort_values(values)
                .map(|Reverse((count, value))| Ok((parse_tantivy_term(value, value_type)?, count)))
                .collect::<Result<_>>()?,
            types: vec![tantivy_type_to_scalar_type(value_type)],
        })
    }

    fn new_from_values(
        values: BinaryHeap<Reverse<(u64, InferenceValue)>>,
        value_types: Vec<tantivy::schema::Type>,
    ) -> Result<Self> {
        Ok(Self {
            values: Self::sort_values(values)
                .map(|Reverse((count, value))| Ok((value.0, count)))
                .collect::<Result<_>>()?,
            types: value_types
                .into_iter()
                .collect::<HashSet<_>>()
                .into_iter()
                .map(tantivy_type_to_scalar_type)
                .sorted()
                .collect(),
        })
    }

    fn sort_values<T: Ord>(values: BinaryHeap<Reverse<T>>) -> impl Iterator<Item = Reverse<T>> {
        values.into_iter().sorted()
    }

    fn into_value(self, field_name: Vec<String>) -> Value {
        serde_json::json!({
            "name": field_name,
            "type": if self.types.len() == 1 {
                self.types[0].to_logical_schema()
            } else {
                serde_json::json!({
                    "anyOf": self.types.iter().map(|t| t.to_logical_schema()).collect::<Vec<_>>(),
                })
            },
            "top_values": self.values.into_iter().map(|(value, count)| serde_json::json!({
                "value": value,
                "count": count,
            })).collect::<Vec<_>>(),
        })
    }
}

struct SchemaInferenceCollector {
    pub tracer: Option<Arc<TracedNode>>,
    pub projected_fields: Arc<Vec<TantivyInferenceField>>,
    pub schema: tantivy::schema::Schema,
    pub executor: Arc<tantivy::Executor>,
    pub has_filter: bool,
}

impl Collector for SchemaInferenceCollector {
    // [top level field] -> ([path] -> [values])
    type Fruit = HashBrownMap<String, (tantivy::schema::Type, FieldMap)>;
    type Child = SchemaInferenceSegmentCollector;

    fn for_segment(
        &self,
        _segment_ord: SegmentOrdinal,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<SchemaInferenceSegmentCollector> {
        let mut inverted_index_fields = Vec::new();
        let mut columnstore_fields = Vec::new();

        let fast_fields = segment_reader.fast_fields();
        let fast_fields_impl = get_fast_fields_impl(fast_fields);

        trace_if(
            log::Level::Info,
            &self.tracer,
            "Open field readers",
            |_child| {
                for projected_field in self.projected_fields.iter() {
                    let field = self.schema.get_field(&projected_field.field)?;
                    let field_entry = self.schema.get_field_entry(field);
                    let field_type = field_entry.field_type();

                    let mut use_columnstore = projected_field.is_columnar;
                    if use_columnstore {
                        if matches!(projected_field.field_type, TantivyType::Json(_)) {
                            let mut prefix = projected_field.field.clone();
                            prefix.push(JSON_PATH_SEGMENT_SEP as char);
                            prefix.push_str(JSON_ROOT_FIELD);
                            let matching_cols =
                                get_columns_matching_prefix(&fast_fields_impl.columnar, &prefix)?;

                            let mut columnstore_fields_unopened = Vec::new();
                            for (path, column) in matching_cols.into_iter() {
                                let remaining_path =
                                    path.strip_prefix(&prefix).ok_or_else(|| {
                                        tantivy::TantivyError::InternalError(format!(
                                            "Expected path {} to start with {}",
                                            path, prefix
                                        ))
                                    })?;

                                columnstore_fields_unopened.push((
                                    projected_field.field.clone(),
                                    Some(remaining_path.as_bytes().to_vec()),
                                    field_type.clone(),
                                    column,
                                ));

                                // If there are enough subpaths (which can happen, e.g. with high cardinality metadata),
                                // then just fall back to using the inverted index. Opening tons of columnstore fields is
                                // expensive, and we don't have advanced enough filtering logic to avoid clobbering the
                                // set of paths.
                                if columnstore_fields_unopened.len() > MAX_PATHS {
                                    use_columnstore = false;
                                    break;
                                }
                            }
                            if use_columnstore {
                                columnstore_fields.extend(
                                    columnstore_fields_unopened
                                        .into_iter()
                                        .map(|(field, path, field_type, column)| {
                                            Ok((field, path, field_type, column.open()?))
                                        })
                                        .collect::<Result<Vec<_>, tantivy::TantivyError>>()?,
                                );
                            }
                        } else {
                            columnstore_fields.extend(
                                fast_fields_impl
                                    .columnar
                                    .read_columns(&projected_field.field)?
                                    .into_iter()
                                    .map(|column| {
                                        Ok((
                                            projected_field.field.clone(),
                                            None,
                                            field_type.clone(),
                                            column.open()?,
                                        ))
                                    })
                                    .collect::<Result<Vec<_>, tantivy::TantivyError>>()?,
                            );
                        }
                    }

                    if !use_columnstore {
                        let inverted_index = segment_reader.inverted_index(field)?;
                        inverted_index_fields.push((
                            projected_field.field.clone(),
                            field_type.clone(),
                            inverted_index,
                        ));
                    }
                }
                Ok::<_, tantivy::TantivyError>(())
            },
        )?;
        self.tracer.as_ref().map(|tracer| {
            tracer.increment_counter("num_columnstore_fields", columnstore_fields.len() as u64);
            tracer.increment_counter(
                "num_inverted_index_fields",
                inverted_index_fields.len() as u64,
            );
        });

        let child = self
            .tracer
            .as_ref()
            .map(|tracer| tracer.new_child("Segment collector"));
        Ok(SchemaInferenceSegmentCollector {
            tracer: child,
            has_filter: self.has_filter,
            inverted_index_fields,
            columnstore_fields,
            executor: self.executor.clone(),
            doc_ids: HashSet::new(),
            fields: HashBrownMap::new(),
            error: None,
        })
    }

    fn merge_fruits(
        &self,
        segment_fruits: Vec<<Self::Child as SegmentCollector>::Fruit>,
    ) -> tantivy::Result<Self::Fruit> {
        let mut merged = HashBrownMap::new();
        trace_if(log::Level::Info, &self.tracer, "Merge buckets", |_child| {
            for fruit in segment_fruits {
                let fruit = fruit?;
                for (field, (field_type, field_map)) in fruit.into_iter() {
                    let logical_name = self
                        .projected_fields
                        .iter()
                        .find(|p| p.field == field)
                        .expect("Field not found in projected fields")
                        .alias
                        .clone();

                    match merged.entry(logical_name) {
                        storage::hash_map::Entry::Occupied(entry) => {
                            entry.replace_entry_with(
                            |_, (field_type, old_field_map): (tantivy::schema::Type, FieldMap)| {
                                Some((field_type, old_field_map.merge(field_map)))
                            },
                        );
                        }
                        storage::hash_map::Entry::Vacant(entry) => {
                            entry.insert((field_type, field_map));
                        }
                    }
                }
            }
            Ok::<_, tantivy::TantivyError>(())
        })?;
        Ok(merged)
    }

    fn requires_scoring(&self) -> bool {
        false
    }
}

struct SchemaInferenceSegmentCollector {
    pub tracer: Option<Arc<TracedNode>>,
    pub has_filter: bool,
    pub inverted_index_fields: Vec<(String, tantivy::schema::FieldType, Arc<InvertedIndexReader>)>,
    pub columnstore_fields: Vec<(
        String,
        Option<Vec<u8>>,
        tantivy::schema::FieldType,
        DynamicColumn,
    )>,
    pub executor: Arc<tantivy::Executor>,
    pub doc_ids: HashSet<tantivy::DocId>,
    pub fields: HashBrownMap<String, (tantivy::schema::Type, FieldMap)>,
    pub error: Option<tantivy::TantivyError>,
}

const MAX_PATHS: usize = 100;
const MAX_VALUES: usize = 10;

impl SegmentCollector for SchemaInferenceSegmentCollector {
    type Fruit = tantivy::Result<HashBrownMap<String, (tantivy::schema::Type, FieldMap)>>;

    fn collect_block(&mut self, docs: &[tantivy::DocId]) {
        if self.error.is_some() {
            return;
        }
        match self.collect_block_fallible(docs) {
            Ok(_) => {}
            Err(e) => {
                self.error = Some(e);
            }
        }
        if self.has_filter {
            self.doc_ids.extend(docs.iter().copied());
        }
    }

    fn collect(&mut self, doc: tantivy::DocId, _score: tantivy::Score) {
        self.collect_block(&[doc]);
    }

    fn harvest(self) -> Self::Fruit {
        self.tracer
            .increment_counter("docs_to_filter", self.doc_ids.len() as u64);
        let tracer = self
            .tracer
            .as_ref()
            .map(|tracer| tracer.new_child("Read-index fields"));
        match self.error {
            Some(e) => Err(e),
            None => trace_if(
                log::Level::Info,
                &tracer,
                "Read inverted-index fields",
                |_child| {
                    let mut fields = self.harvest_fallible(if self.has_filter {
                        Some(self.doc_ids.clone())
                    } else {
                        None
                    })?;
                    fields.extend(self.fields.into_iter());
                    Ok::<_, tantivy::TantivyError>(fields)
                },
            ),
        }
    }
}

impl SchemaInferenceSegmentCollector {
    fn harvest_fallible(
        &self,
        doc_ids: Option<HashSet<tantivy::DocId>>,
    ) -> tantivy::Result<HashBrownMap<String, (tantivy::schema::Type, FieldMap)>> {
        let fields = self
            .executor
            .map(
                |(field, field_type, inverted_index)| {
                    if matches!(field_type, tantivy::schema::FieldType::JsonObject(_)) {
                        let json_values = compute_distinct_json_values(
                            &inverted_index,
                            MAX_PATHS,
                            MAX_VALUES,
                            doc_ids.as_ref(),
                        )?;
                        Ok((
                            field,
                            (field_type.value_type(), FieldMap::JsonObject(json_values)),
                        ))
                    } else {
                        let values =
                            compute_distinct_values(&inverted_index, MAX_VALUES, doc_ids.as_ref())?;
                        Ok((
                            field,
                            (field_type.value_type(), FieldMap::Primitive(values)),
                        ))
                    }
                },
                self.inverted_index_fields.clone().into_iter(),
            )?
            .into_iter()
            .collect();

        Ok(fields)
    }

    fn collect_block_fallible(&mut self, docs: &[tantivy::DocId]) -> tantivy::Result<()> {
        for (field, path, field_type, field_map) in self.columnstore_fields.iter() {
            let values = compute_distinct_columnar_values(field_map, MAX_VALUES, docs)?;
            if values.is_empty() {
                continue;
            }
            let field_map = match path {
                Some(path) => {
                    let map = HashBrownMap::from([(
                        (PathKey(path.clone()), column_to_type(field_map)),
                        values,
                    )]);
                    FieldMap::JsonValue(map)
                }
                None => FieldMap::Value(values),
            };
            match raw_entry_mut_from_hash(&mut self.fields, field) {
                RawEntryMut::Vacant(entry) => {
                    entry.insert(field.clone(), (field_type.value_type(), field_map));
                }
                RawEntryMut::Occupied(entry) => {
                    entry.replace_entry_with(|_, (field_type, existing)| {
                        Some((field_type, existing.merge(field_map)))
                    });
                }
            }
        }
        Ok(())
    }
}

#[derive(Debug)]
enum FieldMap {
    JsonObject(HashBrownMap<PathKey, BinaryHeap<Reverse<(u64, Vec<u8>)>>>),
    Primitive(BinaryHeap<Reverse<(u64, Vec<u8>)>>),
    Value(BinaryHeap<Reverse<(u64, InferenceValue)>>),
    JsonValue(
        HashBrownMap<(PathKey, tantivy::schema::Type), BinaryHeap<Reverse<(u64, InferenceValue)>>>,
    ),
}

impl FieldMap {
    fn merge(self, other: FieldMap) -> Self {
        match (self, other) {
            (FieldMap::JsonObject(mut a), FieldMap::JsonObject(b)) => {
                for (path, heap) in b.into_iter() {
                    let num_paths = a.len();
                    match a.entry(path) {
                        storage::hash_map::Entry::Occupied(entry) => {
                            entry.replace_entry_with(|_, old| Some(merge_heaps([old, heap])));
                        }
                        storage::hash_map::Entry::Vacant(entry) => {
                            if num_paths < MAX_PATHS {
                                entry.insert(heap);
                            }
                        }
                    }
                }

                FieldMap::JsonObject(a)
            }
            (FieldMap::Primitive(a), FieldMap::Primitive(b)) => {
                FieldMap::Primitive(merge_heaps([a, b]))
            }
            (FieldMap::Value(a), FieldMap::Value(b)) => FieldMap::Value(merge_heaps([a, b])),
            (FieldMap::JsonValue(mut a), FieldMap::JsonValue(b)) => {
                for ((path, new_type), heap) in b.into_iter() {
                    let num_paths = a.len();
                    match a.entry((path, new_type)) {
                        storage::hash_map::Entry::Occupied(entry) => {
                            entry.replace_entry_with(|_, old_heap| {
                                Some(merge_heaps([old_heap, heap]))
                            });
                        }
                        storage::hash_map::Entry::Vacant(entry) => {
                            if num_paths < MAX_PATHS {
                                entry.insert(heap);
                            }
                        }
                    }
                }

                FieldMap::JsonValue(a)
            }
            (FieldMap::JsonObject(a), FieldMap::JsonValue(b)) => {
                let value_map = match object_map_to_value_map(a) {
                    Ok(value_map) => FieldMap::JsonValue(value_map),
                    Err(e) => {
                        panic!("Error converting json object to value map: {}", e);
                    }
                };
                value_map.merge(FieldMap::JsonValue(b))
            }
            (FieldMap::JsonValue(a), FieldMap::JsonObject(b)) => {
                let value_map = match object_map_to_value_map(b) {
                    Ok(value_map) => FieldMap::JsonValue(value_map),
                    Err(e) => {
                        panic!("Error converting json object to value map: {}", e);
                    }
                };
                FieldMap::JsonValue(a).merge(value_map)
            }
            _ => {
                panic!("Cannot merge json and primitive field maps")
            }
        }
    }
}

fn object_map_to_value_map(
    map: HashBrownMap<PathKey, BinaryHeap<Reverse<(u64, Vec<u8>)>>>,
) -> Result<
    HashBrownMap<(PathKey, tantivy::schema::Type), BinaryHeap<Reverse<(u64, InferenceValue)>>>,
> {
    map.into_iter()
        .map(|(mut path, heap)| {
            let field_type = match tantivy::schema::Type::from_code(path.0[path.0.len() - 1]) {
                Some(field_type) => field_type,
                None => {
                    return Err(InterpreterError::InternalError(format!(
                        "Invalid field type code: {}",
                        path.0[path.0.len() - 1]
                    )));
                }
            };
            let name = path.0.drain(..path.0.len() - 2).collect::<Vec<_>>();
            let value_heap = heap
                .into_iter()
                .map(|Reverse((count, value))| {
                    Ok(Reverse((
                        count,
                        InferenceValue(parse_tantivy_term(value, field_type)?),
                    )))
                })
                .collect::<Result<BinaryHeap<_>>>()?;

            Ok(((PathKey(name), field_type), value_heap))
        })
        .collect::<Result<HashBrownMap<_, _>>>()
}

fn merge_heaps<T: Eq + std::hash::Hash + Clone + Ord + std::fmt::Debug>(
    heaps: impl IntoIterator<Item = BinaryHeap<Reverse<(u64, T)>>>,
) -> BinaryHeap<Reverse<(u64, T)>> {
    let mut all_buckets = HashBrownMap::new();
    for heap in heaps {
        for Reverse((count, value)) in heap.into_iter() {
            match all_buckets.entry(value) {
                storage::hash_map::Entry::Occupied(entry) => {
                    *entry.into_mut() += count;
                }
                storage::hash_map::Entry::Vacant(entry) => {
                    entry.insert(count);
                }
            }
        }
    }
    let mut heap = BinaryHeap::with_capacity(MAX_VALUES);
    for (value, count) in all_buckets.into_iter() {
        if heap.len() < MAX_VALUES {
            heap.push(Reverse((count, value.clone())));
        } else if let Some(Reverse((min_count, _))) = heap.peek() {
            if count > *min_count {
                heap.pop();
                heap.push(Reverse((count, value.clone())));
            }
        }
    }
    heap
}

// TODO: It'd be nice to compute min/max here too
fn compute_distinct_values(
    inverted_index: &InvertedIndexReader,
    max_values: usize,
    doc_ids: Option<&HashSet<tantivy::DocId>>,
) -> tantivy::Result<BinaryHeap<Reverse<(u64, Vec<u8>)>>> {
    // Use Reverse for min-heap behavior (keeps largest values)
    let mut heap = BinaryHeap::with_capacity(max_values);

    let mut stream = inverted_index.terms().stream()?;
    while let Some((term_bytes, term_info)) = stream.next() {
        if heap.len() < max_values {
            if matches_filters(inverted_index, term_info, doc_ids)? {
                heap.push(Reverse((term_info.doc_freq as u64, Vec::from(term_bytes))));
            }
        } else if let Some(Reverse((min_count, _))) = heap.peek() {
            // If new count is larger than smallest in heap
            if term_info.doc_freq as u64 > *min_count
                && matches_filters(inverted_index, term_info, doc_ids)?
            {
                heap.pop(); // Remove smallest
                heap.push(Reverse((term_info.doc_freq as u64, Vec::from(term_bytes))));
            }
        }
    }

    Ok(heap)
}

fn matches_filters(
    inverted_index: &InvertedIndexReader,
    term_info: &tantivy::postings::TermInfo,
    doc_ids: Option<&HashSet<tantivy::DocId>>,
) -> tantivy::Result<bool> {
    match doc_ids {
        Some(doc_ids) => term_intersects_doc_ids(inverted_index, term_info, doc_ids),
        None => Ok(true),
    }
}

fn term_intersects_doc_ids(
    inverted_index: &InvertedIndexReader,
    term_info: &tantivy::postings::TermInfo,
    doc_ids: &HashSet<tantivy::DocId>,
) -> tantivy::Result<bool> {
    let mut postings =
        inverted_index.read_postings_from_terminfo(term_info, IndexRecordOption::Basic)?;
    loop {
        let doc_id = postings.doc();
        if doc_id == tantivy::TERMINATED {
            return Ok(false);
        }
        if doc_ids.contains(&doc_id) {
            return Ok(true);
        }
        postings.advance();
    }
}

fn compute_distinct_json_values(
    inverted_index: &InvertedIndexReader,
    max_paths: usize,
    max_values: usize,
    doc_ids: Option<&HashSet<tantivy::DocId>>,
) -> tantivy::Result<HashBrownMap<PathKey, BinaryHeap<Reverse<(u64, Vec<u8>)>>>> {
    let mut path_to_heap: HashBrownMap<PathKey, BinaryHeap<Reverse<(u64, Vec<u8>)>>> =
        HashBrownMap::new();

    let mut stream = inverted_index.terms().stream()?;
    let mut curr_heap = BinaryHeap::with_capacity(max_values);
    while let Some((term_bytes, term_info)) = stream.next() {
        if path_to_heap.len() >= max_paths {
            // We can't track the top N paths without scanning them or doing some heuristics, so let's just track the
            // first N distinct paths.
            break;
        }

        if term_bytes[0] == b'p' {
            // These are just references to the non-null paths
            continue;
        }

        // Find JSON_END_OF_PATH from the beginning because a value can have \0 in it.
        let json_end_of_path_idx = match term_bytes.iter().position(|b| *b == JSON_END_OF_PATH) {
            // Include the JSON_END_OF_PATH and the type in the path key
            Some(idx) => idx + 2,
            None => {
                log::warn!("No JSON_END_OF_PATH found in term while doing schema inference");
                continue;
            }
        };

        let path = PathKeyRef(&term_bytes[..json_end_of_path_idx]);

        // We don't want to occupy a spot in the path map unless this heap actually ends up getting used. Otherwise, we might end up with
        // a bunch of empty metadata paths. So, pull out the entry if it's not occupied, and then later on, only insert it if we actually
        // end up using the heap.
        let (path_heap, entry) = match raw_entry_mut_from_hash(&mut path_to_heap, &path) {
            RawEntryMut::Occupied(entry) => (entry.into_mut(), None),
            RawEntryMut::Vacant(entry) => (&mut curr_heap, Some(entry)),
        };

        let value_bytes = &term_bytes[json_end_of_path_idx..];
        let mut used_heap = false;
        if path_heap.len() < max_values {
            if matches_filters(inverted_index, term_info, doc_ids)? {
                path_heap.push(Reverse((term_info.doc_freq as u64, value_bytes.to_vec())));
                used_heap = true;
            }
        } else if let Some(Reverse((min_count, _))) = path_heap.peek() {
            if term_info.doc_freq as u64 > *min_count
                && matches_filters(inverted_index, term_info, doc_ids)?
            {
                path_heap.pop();
                path_heap.push(Reverse((term_info.doc_freq as u64, value_bytes.to_vec())));
                used_heap = true;
            }
        }

        match (used_heap, entry) {
            (true, Some(entry)) => {
                let heap = std::mem::replace(&mut curr_heap, BinaryHeap::with_capacity(max_values));
                entry.insert(PathKey(path.0.to_vec()), heap);
            }
            _ => {}
        }
    }
    Ok(path_to_heap)
}

fn parse_json_path(path: Vec<u8>) -> Vec<String> {
    // Split each JSON_PATH_SEPARATOR into a new string
    path.split(|b| *b == JSON_PATH_SEGMENT_SEP)
        .skip(1) // The prefixed 'o'
        .map(|s| String::from_utf8_lossy(s).into_owned())
        .collect::<Vec<_>>()
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct PathKey(Vec<u8>);

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct PathKeyRef<'a>(&'a [u8]);

impl RawEntryCompatible<PathKey> for PathKeyRef<'_> {
    fn convert_to_key(&self) -> PathKey {
        PathKey(self.0.to_vec())
    }

    fn is_match(&self, other: &PathKey) -> bool {
        self.0 == other.0
    }
}

fn parse_tantivy_term(term: Vec<u8>, tantivy_type: tantivy::schema::Type) -> Result<Value> {
    let mut term_slice = &term[..];
    Ok(match tantivy_type {
        tantivy::schema::Type::Str => {
            let value = String::from_utf8(term_slice.to_vec())
                .context("invalid utf-8 while parsing string term")?;

            // NOTE: We could optimize this further by accumulating the trash in a background thread.
            make_preview_field(Value::String(value), PREVIEW_FIELD_LENGTH).0
        }
        tantivy::schema::Type::U64 => {
            let value = parse_numeric_term::<u64>(&mut term_slice)?;
            value.into()
        }
        tantivy::schema::Type::I64 => {
            let value = parse_numeric_term::<i64>(&mut term_slice)?;
            value.into()
        }
        tantivy::schema::Type::F64 => {
            let value = parse_numeric_term::<f64>(&mut term_slice)?;
            value.into()
        }
        tantivy::schema::Type::Bool => {
            let value = parse_numeric_term::<bool>(&mut term_slice)?;
            value.into()
        }
        tantivy::schema::Type::Date => {
            let ns = parse_numeric_term::<i64>(&mut term_slice)?;
            let date_time: DateTime = DateTime::from_timestamp_nanos(ns);
            let date_time_string: String = date_time.cast()?;
            Value::String(date_time_string)
        }
        tantivy::schema::Type::Facet | tantivy::schema::Type::Bytes => {
            let value =
                String::from_utf8_lossy(&term_slice[..term_slice.len().min(PREVIEW_FIELD_LENGTH)]);
            Value::String(value.into_owned())
        }
        tantivy::schema::Type::IpAddr => {
            let mut arr = [0u8; 16];
            arr.copy_from_slice(&term_slice[..16]);
            let value = std::net::Ipv6Addr::from(arr);
            Value::String(value.to_string())
        }
        tantivy::schema::Type::Json => {
            return Err(InterpreterError::InternalError(
                "json value should have been handled elsewhere".to_string(),
            ));
        }
    })
}

fn parse_numeric_term<T: MonotonicallyMappableToU64>(term_slice: &mut &[u8]) -> Result<T> {
    let mut arr = [0u8; 8];
    arr.copy_from_slice(&term_slice[..8]);
    let value = u64::from_be_bytes(arr);
    Ok(T::from_u64(value))
}

fn tantivy_type_to_scalar_type(tantivy_type: tantivy::schema::Type) -> ScalarType {
    match tantivy_type {
        tantivy::schema::Type::Str => ScalarType::String,
        tantivy::schema::Type::U64 => ScalarType::Integer,
        tantivy::schema::Type::I64 => ScalarType::Integer,
        tantivy::schema::Type::F64 => ScalarType::Number,
        tantivy::schema::Type::Bool => ScalarType::Boolean,
        tantivy::schema::Type::Date => ScalarType::Date,
        tantivy::schema::Type::Facet => ScalarType::String,
        tantivy::schema::Type::Bytes => ScalarType::String,
        tantivy::schema::Type::Json => ScalarType::Object,
        tantivy::schema::Type::IpAddr => ScalarType::String,
    }
}

fn compute_distinct_columnar_values(
    column: &DynamicColumn,
    max_values: usize,
    doc_ids: &[u32],
) -> tantivy::Result<BinaryHeap<Reverse<(u64, InferenceValue)>>> {
    let values = collect_column_for_doc_batch(column, doc_ids)?;
    let mut counts = HashBrownMap::new();
    for value in values {
        match value {
            None => {}
            Some(OwnedValue::Array(values)) => {
                for value in values {
                    let value_mut = counts.entry(HashableOwnedValue(value)).or_insert(0);
                    *value_mut += 1;
                }
            }
            Some(value) => {
                let value_mut = counts.entry(HashableOwnedValue(value)).or_insert(0);
                *value_mut += 1;
            }
        }
    }
    let mut heap = BinaryHeap::with_capacity(max_values);
    for (owned_value, count) in counts.into_iter() {
        if heap.len() < max_values {
            // NOTE: convert_tantivy_value should take an owned value
            let value = convert_tantivy_value(&owned_value.0, &[], false)
                .map_err(|e| tantivy::TantivyError::InternalError(e.to_string()))?;
            heap.push(Reverse((count, InferenceValue(value))));
        } else if let Some(Reverse((min_count, _))) = heap.peek() {
            if count > *min_count {
                let value = convert_tantivy_value(&owned_value.0, &[], false)
                    .map_err(|e| tantivy::TantivyError::InternalError(e.to_string()))?;
                heap.pop();
                heap.push(Reverse((count, InferenceValue(value))));
            }
        }
    }
    Ok(heap)
}

#[derive(Debug, Clone, PartialEq, Eq)]
struct HashableOwnedValue(OwnedValue);

impl std::hash::Hash for HashableOwnedValue {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        match &self.0 {
            OwnedValue::Null => {
                // Hash a specific value for null
                0u8.hash(state);
            }
            OwnedValue::Str(value) => {
                1u8.hash(state);
                value.hash(state);
            }
            OwnedValue::PreTokStr(value) => {
                2u8.hash(state);
                value.text.hash(state);
            }
            OwnedValue::U64(value) => {
                3u8.hash(state);
                value.hash(state);
            }
            OwnedValue::I64(value) => {
                4u8.hash(state);
                value.hash(state);
            }
            OwnedValue::F64(value) => {
                5u8.hash(state);
                value.to_bits().hash(state);
            }
            OwnedValue::Bool(value) => {
                6u8.hash(state);
                value.hash(state);
            }
            OwnedValue::Date(value) => {
                7u8.hash(state);
                value.into_timestamp_nanos().hash(state);
            }
            OwnedValue::Facet(value) => {
                8u8.hash(state);
                value.encoded_str().hash(state);
            }
            OwnedValue::Bytes(value) => {
                9u8.hash(state);
                value.hash(state);
            }
            OwnedValue::Array(values) => {
                10u8.hash(state);
                values.len().hash(state);
                for value in values {
                    HashableOwnedValue(value.clone()).hash(state);
                }
            }
            OwnedValue::Object(map) => {
                11u8.hash(state);
                map.len().hash(state);
                // Sort keys for consistent hashing
                let mut keys: Vec<&String> = map.keys().collect();
                keys.sort();
                for key in keys {
                    key.hash(state);
                    if let Some(value) = map.get(key) {
                        HashableOwnedValue(value.clone()).hash(state);
                    }
                }
            }
            OwnedValue::IpAddr(value) => {
                12u8.hash(state);
                value.hash(state);
            }
        }
    }
}

fn merge_path_buckets(
    path_buckets: BTreeMap<
        Vec<String>,
        Vec<(
            tantivy::schema::Type,
            BinaryHeap<Reverse<(u64, InferenceValue)>>,
        )>,
    >,
) -> Result<Vec<(Vec<String>, SchemaInferenceBucket)>> {
    let mut buckets = Vec::new();
    for (field_name, heaps) in path_buckets {
        let mut types = Vec::new();
        let mut heaps_to_merge = Vec::new();
        for (field_type, heap) in heaps {
            types.push(field_type);
            heaps_to_merge.push(heap);
        }
        let merged_heap = merge_heaps(heaps_to_merge);
        let bucket = SchemaInferenceBucket::new_from_values(merged_heap, types)?;
        buckets.push((field_name, bucket));
    }
    Ok(buckets)
}

// Similar to SortedValue, but uses the more restrictive PartialEq and Eq
#[derive(Debug, Clone, Hash, PartialEq, Eq)]
pub struct InferenceValue(pub Value);

impl PartialOrd for InferenceValue {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        order_values(&self.0, &other.0)
    }
}

impl Ord for InferenceValue {
    fn cmp(&self, other: &Self) -> Ordering {
        let partial = self.partial_cmp(other);
        partial.unwrap_or(Ordering::Equal)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_1_true() {
        let val1: Value = 1.into();
        let val2: Value = true.into();
        assert_ne!(val1, val2);
        assert_ne!(val2, val1);
        // Unlike SortValue, InferenceValue does not permit equality across types
        assert_ne!(InferenceValue(val1.clone()), InferenceValue(val2.clone()));
        assert_ne!(InferenceValue(val2.clone()), InferenceValue(val1.clone()));
    }
}
