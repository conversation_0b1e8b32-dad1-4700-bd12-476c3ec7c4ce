use std::{borrow::Cow, cmp::Ordering};
use tantivy::DateTime;
use util::{
    ptree::{MakePTree, TreeBuilder},
    xact::PaginationKey,
    Value,
};

use btql::{interpreter::expr::order_values, typesystem::cast::value_is_null};

use crate::interpreter::{
    columnar::{
        value::{BytesOrdinal, ColumnarExprContext, Null, StringOrdinal},
        OwnedColumnarType, PrimitiveColumnarType, PtrOffset,
    },
    error::Result,
};
use sketches_ddsketch::{Config, DDSketch};

use super::{
    sum::{self, AddValue, AsF64, DynamicSum},
    ValueAggregator,
};

pub trait PrimitiveAggregateValue:
    std::fmt::Debug + Clone + Sync + Send + Copy + 'static + PrimitiveColumnarType + AsF64
{
    type SumImpl: Aggregator<Item<'static> = Option<Self>> + Default + AsF64 + AddValue;

    #[inline(always)]
    fn is_null(&self) -> bool {
        false
    }
}

impl PrimitiveAggregateValue for Null {
    type SumImpl = sum::NullSum<Null>;

    #[inline(always)]
    fn is_null(&self) -> bool {
        true
    }
}
impl PrimitiveAggregateValue for bool {
    type SumImpl = sum::IntSum<bool>;
}
impl PrimitiveAggregateValue for i64 {
    type SumImpl = sum::IntSum<i64>;
}
impl PrimitiveAggregateValue for tantivy::DateTime {
    type SumImpl = sum::IntSum<DateTime>;
}
impl PrimitiveAggregateValue for u64 {
    type SumImpl = sum::IntSum<u64>;
}
impl PrimitiveAggregateValue for PaginationKey {
    type SumImpl = sum::IntSum<PaginationKey>;
}
impl PrimitiveAggregateValue for f64 {
    type SumImpl = sum::FloatSum;
}
impl PrimitiveAggregateValue for PtrOffset {
    type SumImpl = sum::NullSum<PtrOffset>;
}
impl PrimitiveAggregateValue for StringOrdinal {
    type SumImpl = sum::NullSum<StringOrdinal>;
}
impl PrimitiveAggregateValue for BytesOrdinal {
    type SumImpl = sum::NullSum<BytesOrdinal>;
}

fn is_null(value: &Option<impl PrimitiveAggregateValue>) -> bool {
    match value {
        Some(v) => v.is_null(),
        None => true,
    }
}

pub type DynamicValue = Cow<'static, Value>;

// Splitting these out lets us implement them once.
pub trait AggregatorBase {
    fn combine(&mut self, other: Self) -> Result<()>;
    fn collect(&self) -> Result<util::Value>;
}

pub trait Aggregator:
    AggregatorBase + std::fmt::Debug + Clone + Send + Sync + MakePTree + 'static
{
    type Item<'a>: std::fmt::Debug + Clone + Sync + Send + 'a;

    fn aggregate(&mut self, ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()>;

    #[inline(always)]
    fn aggregate_value(&mut self, ctx: &ColumnarExprContext, value: Self::Item<'_>) -> Result<()> {
        self.aggregate(ctx, &[value])
    }

    fn into_value_aggregator(self) -> ValueAggregator;
}

#[derive(Debug, Clone, MakePTree)]
#[ptree(label_only)]
pub struct ConstantCount<I>(u64, std::marker::PhantomData<I>);

impl<I> Default for ConstantCount<I> {
    fn default() -> Self {
        ConstantCount(0, std::marker::PhantomData)
    }
}

impl<P: PrimitiveAggregateValue> Aggregator for ConstantCount<P> {
    type Item<'a> = Option<P>;

    #[inline(always)]
    fn aggregate(&mut self, _ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        self.0 += input.len() as u64;
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::ConstantCount(ConstantCount::<Cow<'static, Value>>(
            self.0,
            std::marker::PhantomData,
        ))
    }
}

// This has to be exactly duplicated because otherwise E0521 does not kick in.
impl Aggregator for ConstantCount<Cow<'static, Value>> {
    type Item<'a> = Cow<'a, Value>;

    #[inline(always)]
    fn aggregate(&mut self, _ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        self.0 += input.len() as u64;
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::ConstantCount(self)
    }
}

impl<I> AggregatorBase for ConstantCount<I> {
    fn combine(&mut self, other: Self) -> Result<()> {
        self.0 += other.0;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        Ok(self.0.into())
    }
}

#[derive(Debug, Clone, MakePTree)]
#[ptree(label_only)]
pub struct ExprCount<I>(u64, std::marker::PhantomData<I>);

impl<I> Default for ExprCount<I> {
    fn default() -> Self {
        ExprCount(0, std::marker::PhantomData)
    }
}

impl<P: PrimitiveAggregateValue> Aggregator for ExprCount<P> {
    type Item<'a> = Option<P>;

    fn aggregate(&mut self, _ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        for v in input {
            match v {
                Some(v) if !v.is_null() => self.0 += 1,
                _ => {}
            }
        }
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::ExprCount(ExprCount::<Cow<'static, Value>>(
            self.0,
            std::marker::PhantomData,
        ))
    }
}

impl Aggregator for ExprCount<Cow<'static, Value>> {
    type Item<'a> = Cow<'a, Value>; // true if arg is not null
                                    //
    fn aggregate(&mut self, _ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        for v in input {
            if !value_is_null(v) {
                self.0 += 1;
            }
        }
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::ExprCount(self)
    }
}

impl<I> AggregatorBase for ExprCount<I> {
    fn combine(&mut self, other: Self) -> Result<()> {
        self.0 += other.0;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        Ok(self.0.into())
    }
}

#[derive(Debug, Clone, Default)]
pub struct Sum<I: PrimitiveAggregateValue>(I::SumImpl);

impl<P: PrimitiveAggregateValue> Aggregator for Sum<P> {
    type Item<'a> = Option<P>;

    fn aggregate(&mut self, ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        self.0.aggregate(ctx, input)
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        self.0.into_value_aggregator()
    }
}

impl<P: PrimitiveAggregateValue> AggregatorBase for Sum<P> {
    fn combine(&mut self, other: Self) -> Result<()> {
        self.0.combine(other.0)
    }

    fn collect(&self) -> Result<Value> {
        self.0.collect()
    }
}

impl<P: PrimitiveAggregateValue> MakePTree for Sum<P> {
    fn label(&self) -> String {
        self.0.label()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.0.make_ptree(builder)
    }
}

#[derive(Debug, Clone, Default, MakePTree)]
#[ptree(label_only)]
pub struct Avg<I: PrimitiveAggregateValue> {
    total: I::SumImpl,
    count: u64,
}

impl<P: PrimitiveAggregateValue> AddValue for Avg<P> {
    fn add_value(&mut self, value: Option<P>) {
        if is_null(&value) {
            return;
        }
        self.total.add_value(value);
        self.count += 1;
    }
}

impl<P: PrimitiveAggregateValue> Aggregator for Avg<P> {
    type Item<'a> = Option<P>;

    fn aggregate(&mut self, ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        self.total.aggregate(ctx, input)?;
        for value in input {
            if !is_null(value) {
                self.count += 1;
            }
        }
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::Avg(DynamicAvg {
            sum: match self.total.into_value_aggregator() {
                ValueAggregator::Sum(sum) => sum,
                _ => unreachable!(),
            },
            count: self.count,
        })
    }
}

impl<P: PrimitiveAggregateValue> AggregatorBase for Avg<P> {
    fn combine(&mut self, other: Self) -> Result<()> {
        self.total.combine(other.total)?;
        self.count += other.count;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        if self.count == 0 {
            return Ok(Value::Null);
        }
        let avg = (self.total.as_f64()) / (self.count as f64);
        Ok(Value::Number(
            serde_json::Number::from_f64(avg).unwrap_or(serde_json::Number::from(0)),
        ))
    }
}

#[derive(Debug, Clone, Default, MakePTree)]
#[ptree(label_only)]
pub struct DynamicAvg {
    sum: DynamicSum,
    count: u64,
}

impl Aggregator for DynamicAvg {
    type Item<'a> = Cow<'a, Value>;

    fn aggregate(&mut self, ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        self.sum.aggregate(ctx, input)?;
        for value in input {
            if !value_is_null(value) {
                self.count += 1;
            }
        }
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::Avg(self)
    }
}

impl AggregatorBase for DynamicAvg {
    fn combine(&mut self, other: Self) -> Result<()> {
        self.sum.combine(other.sum)?;
        self.count += other.count;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        if self.count == 0 {
            return Ok(Value::Null);
        }
        let avg = (self.sum.as_f64()) / (self.count as f64);
        Ok(Value::Number(
            serde_json::Number::from_f64(avg).unwrap_or(serde_json::Number::from(0)),
        ))
    }
}

#[derive(Debug, Clone, Default, MakePTree)]
#[ptree(label_only)]
pub struct Min<I: PrimitiveAggregateValue> {
    value: Option<I::Owned>,
}

impl<I: PrimitiveAggregateValue> Aggregator for Min<I> {
    type Item<'a> = Option<I>;

    fn aggregate(&mut self, ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        for value in input {
            if value.is_none() {
                continue;
            }
            if (self.value.is_none() && value.is_some()) || value.lt_owned(ctx, &self.value) {
                self.value = value.to_owned_val(ctx);
            }
        }
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::Min(DynamicMin {
            value: self.value.into_value(),
        })
    }
}

impl<I: PrimitiveAggregateValue> AggregatorBase for Min<I> {
    fn collect(&self) -> Result<Value> {
        Ok(self.value.clone().into_value())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        match (&self.value, other.value) {
            (None, Some(v)) => self.value = Some(v.to_owned()),
            (_, None) => {}
            (Some(v1), Some(v2)) => {
                if v1 < &v2 {
                    self.value = Some(v2)
                }
            }
        }
        Ok(())
    }
}

#[derive(Debug, Clone, Default, MakePTree)]
#[ptree(label_only)]
pub struct DynamicMin {
    value: util::Value,
}

impl Aggregator for DynamicMin {
    type Item<'a> = Cow<'a, util::Value>;

    fn aggregate(&mut self, _ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        for value in input {
            if value_is_null(value) {
                continue;
            } else if value_is_null(&self.value) && !value_is_null(value) {
                self.value = value.clone().into_owned();
                continue;
            } else if let Some(Ordering::Less) = order_values(value, &self.value) {
                self.value = value.clone().into_owned();
            }
        }
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::Min(self)
    }
}

impl AggregatorBase for DynamicMin {
    fn collect(&self) -> Result<Value> {
        Ok(self.value.clone())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        if value_is_null(&other.value) {
            // Do nothing
        } else if value_is_null(&self.value) {
            self.value = other.value.clone();
        } else if let Some(Ordering::Less) = order_values(&other.value, &self.value) {
            self.value = other.value;
        }
        Ok(())
    }
}

#[derive(Debug, Clone, Default, MakePTree)]
#[ptree(label_only)]
pub struct Max<I: PrimitiveAggregateValue> {
    value: Option<I::Owned>,
}

impl<I: PrimitiveAggregateValue> Aggregator for Max<I> {
    type Item<'a> = Option<I>;

    fn aggregate(&mut self, ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        for value in input {
            if value.is_none() {
                continue;
            } else if (self.value.is_none() && value.is_some()) || value.gt_owned(ctx, &self.value)
            {
                self.value = value.to_owned_val(ctx);
            }
        }
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::Max(DynamicMax {
            value: self.value.into_value(),
        })
    }
}

impl<I: PrimitiveAggregateValue> AggregatorBase for Max<I> {
    fn collect(&self) -> Result<Value> {
        Ok(self.value.clone().into_value())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        match (&self.value, other.value) {
            (None, Some(v)) => self.value = Some(v.to_owned()),
            (_, None) => {}
            (Some(v1), Some(v2)) => {
                if v1 > &v2 {
                    self.value = Some(v2)
                }
            }
        }
        Ok(())
    }
}

#[derive(Debug, Clone, Default, MakePTree)]
#[ptree(label_only)]
pub struct DynamicMax {
    value: util::Value,
}

impl Aggregator for DynamicMax {
    type Item<'a> = Cow<'a, util::Value>;

    fn aggregate(&mut self, _ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        for value in input {
            if value_is_null(value) {
                continue;
            } else if value_is_null(&self.value) && !value_is_null(value) {
                self.value = value.clone().into_owned();
                continue;
            } else if let Some(Ordering::Greater) = order_values(value, &self.value) {
                self.value = value.clone().into_owned();
            }
        }
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::Max(self)
    }
}

impl AggregatorBase for DynamicMax {
    fn collect(&self) -> Result<Value> {
        Ok(self.value.clone())
    }

    fn combine(&mut self, other: Self) -> Result<()> {
        if value_is_null(&other.value) {
            // Do nothing
        } else if value_is_null(&self.value) {
            self.value = other.value.clone();
        } else if let Some(Ordering::Greater) = order_values(&other.value, &self.value) {
            self.value = other.value;
        }
        Ok(())
    }
}

#[derive(Clone, MakePTree)]
pub struct Percentile<P> {
    percentile: f64,
    #[ptree(skip)]
    sketch: DDSketch,
    #[ptree(skip)]
    _phantom: std::marker::PhantomData<P>,
}

impl<P> std::fmt::Debug for Percentile<P> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("Percentile")
            .field("percentile", &self.percentile)
            .field("sketch_len", &self.sketch.length())
            .field("count", &self.sketch.count())
            .field("min", &self.sketch.min())
            .field("max", &self.sketch.max())
            .field("sum", &self.sketch.sum())
            .finish()
    }
}

impl<P> Percentile<P> {
    pub fn new(percentile: f64) -> Self {
        if !(0.0..=1.0).contains(&percentile) {
            panic!("Percentile must be between 0 and 1");
        }
        Self {
            percentile,
            // NOTE(austin): Technically this is a "low percentile" implementation.
            // If we want to mimic Clickhouse's `quantileExact` we could fork
            // DDSketch to modify the choice of index. We could also do some additional
            // fiddling if we wanted to mimic `percentile_cont` in Postgres.
            // Also see PercentileExact in percentile_test.rs
            sketch: DDSketch::new(Config::new(0.01, 2048, 1.0e-9)),
            _phantom: std::marker::PhantomData,
        }
    }
}

impl<P: PrimitiveAggregateValue> Aggregator for Percentile<P> {
    type Item<'a> = Option<P>;

    fn aggregate(&mut self, _ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        for value in input {
            match value.map(|v| v.as_f64()) {
                Some(f) if f.is_finite() => self.sketch.add(f),
                _ => continue,
            }
        }
        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::Percentile(Percentile {
            percentile: self.percentile,
            sketch: self.sketch,
            _phantom: std::marker::PhantomData,
        })
    }
}

impl Aggregator for Percentile<DynamicValue> {
    type Item<'a> = Cow<'a, Value>;

    fn aggregate(&mut self, _ctx: &ColumnarExprContext, input: &[Self::Item<'_>]) -> Result<()> {
        for value in input {
            let n = match value.as_ref() {
                Value::Number(n) => match n.as_f64() {
                    Some(f) if f.is_finite() => f,
                    _ => continue,
                },
                _ => continue,
            };

            self.sketch.add(n);
        }

        Ok(())
    }

    fn into_value_aggregator(self) -> ValueAggregator {
        ValueAggregator::Percentile(self)
    }
}

impl<P> AggregatorBase for Percentile<P> {
    fn combine(&mut self, other: Self) -> Result<()> {
        self.sketch.merge(&other.sketch).map_err(|e| {
            crate::interpreter::error::InterpreterError::InternalError(format!(
                "Failed to merge DDSketches: {}",
                e
            ))
        })?;
        Ok(())
    }

    fn collect(&self) -> Result<Value> {
        if self.sketch.length() == 0 {
            return Ok(Value::Null);
        }

        match self.sketch.quantile(self.percentile) {
            Ok(Some(value)) => Ok(Value::Number(serde_json::Number::from_f64(value).unwrap())),
            Ok(None) => Ok(Value::Null),
            Err(e) => Err(crate::interpreter::error::InterpreterError::InternalError(
                format!("Failed to compute quantile: {}", e),
            )),
        }
    }
}
