use std::collections::HashSet;

use clap::Parse<PERSON>;
use tantivy::{
    collector::{Collector, SegmentCollector},
    columnar::StrColumn,
    doc,
    query::{TermQuery, TermSetQuery},
    schema::{Field, Schema, TextFieldIndexing, TextOptions, Value, FAST, STORED},
    Index, IndexWriter, Score, SegmentReader, Term,
};
use util::anyhow::Result;

#[derive(Pa<PERSON><PERSON>, Debug, Clone)]
pub struct BenchmarkArgs {
    /// Total number of spans to create
    #[arg(long, default_value = "10000")]
    pub total_spans: usize,

    /// Number of root spans
    #[arg(long, default_value = "1000")]
    pub num_roots: usize,

    /// Average spans per root
    #[arg(long, default_value = "10")]
    pub avg_spans_per_root: usize,

    /// Use u64 for root_span_id instead of string
    #[arg(long)]
    pub u64_root_span_id: bool,

    /// Use bytes for root_span_id instead of string
    #[arg(long)]
    pub bytes_root_span_id: bool,

    /// Use chunked approach instead of two full passes
    #[arg(long)]
    pub chunked: bool,

    /// Chunk size for chunked processing
    #[arg(long, default_value = "100000")]
    pub chunk_size: usize,
}

pub fn benchmark(args: BenchmarkArgs) -> Result<()> {
    if args.chunked {
        if args.u64_root_span_id {
            run_chunked_search_benchmark_u64(
                args.total_spans,
                args.num_roots,
                args.avg_spans_per_root,
                args.chunk_size,
            )
        } else if args.bytes_root_span_id {
            todo!("Chunked bytes implementation")
        } else {
            run_chunked_search_benchmark(
                args.total_spans,
                args.num_roots,
                args.avg_spans_per_root,
                args.chunk_size,
            )
        }
    } else {
        if args.u64_root_span_id {
            run_two_level_search_benchmark_u64(
                args.total_spans,
                args.num_roots,
                args.avg_spans_per_root,
            )
        } else if args.bytes_root_span_id {
            run_two_level_search_benchmark_bytes(
                args.total_spans,
                args.num_roots,
                args.avg_spans_per_root,
            )
        } else {
            run_two_level_search_benchmark(
                args.total_spans,
                args.num_roots,
                args.avg_spans_per_root,
            )
        }
    }
}

fn run_two_level_search_benchmark(
    total_spans: usize,
    num_roots: usize,
    avg_spans_per_root: usize,
) -> Result<()> {
    println!(
        "Creating index with {} total spans, {} root spans",
        total_spans, num_roots
    );
    let start = std::time::Instant::now();

    // Create schema with root_span_id and span_id fields
    let mut schema_builder = Schema::builder();
    let text_field_indexing = TextFieldIndexing::default()
        .set_tokenizer("raw")
        .set_index_option(tantivy::schema::IndexRecordOption::Basic);
    let text_options = TextOptions::default()
        .set_indexing_options(text_field_indexing)
        .set_fast(Some("raw"))
        .set_stored();

    let root_span_id = schema_builder.add_text_field("root_span_id", text_options.clone());
    let span_id = schema_builder.add_text_field("span_id", text_options.clone());
    let content = schema_builder.add_text_field("content", tantivy::schema::TEXT | STORED);
    let score_field = schema_builder.add_f64_field("score", FAST | STORED);

    let schema = schema_builder.build();
    let index = Index::create_in_ram(schema.clone());
    let mut index_writer: IndexWriter = index.writer(500_000_000)?;

    // Add documents in batches
    let mut doc_count = 0;
    let content_variations = [
        "error in authentication module",
        "database connection failed",
        "checking user credentials",
        "loading user profile",
        "connecting to postgres",
        "successful operation",
        "processing request",
        "validating input data",
        "executing query",
        "handling response",
    ];

    // Create root documents
    for root_idx in 0..num_roots {
        let root_id = format!("root{}", root_idx);
        let content_text = content_variations[root_idx % content_variations.len()];

        index_writer.add_document(doc!(
            root_span_id => root_id.clone(),
            span_id => root_id.clone(),
            content => content_text,
            score_field => 0.9 - (root_idx as f64 * 0.0001)
        ))?;
        doc_count += 1;

        // Create child spans for this root
        let spans_for_this_root = if root_idx < num_roots / 10 {
            // 10% of roots have many more spans (simulating hot traces)
            avg_spans_per_root * 5
        } else {
            avg_spans_per_root
        };

        for span_idx in 0..spans_for_this_root {
            if doc_count >= total_spans {
                break;
            }

            let span_id_str = format!("span_{}_{}", root_idx, span_idx);
            let content_text = content_variations[(root_idx + span_idx) % content_variations.len()];

            index_writer.add_document(doc!(
                root_span_id => root_id.clone(),
                span_id => span_id_str,
                content => content_text,
                score_field => 0.5 + (span_idx as f64 * 0.01)
            ))?;
            doc_count += 1;
        }

        if doc_count >= total_spans {
            break;
        }

        // Commit every 100k documents to avoid memory issues
        if doc_count % 100_000 == 0 {
            index_writer.commit()?;
            println!("Committed {} documents...", doc_count);
        }
    }

    index_writer.commit()?;
    println!(
        "Index creation took: {:?}, total documents: {}",
        start.elapsed(),
        doc_count
    );

    let reader = index.reader()?;
    let searcher = reader.searcher();

    // First, let's search for documents containing "authentication"
    let query = TermQuery::new(
        Term::from_field_text(content, "authentication"),
        tantivy::schema::IndexRecordOption::Basic,
    );

    println!("\n=== Phase 1: Search for 'authentication' ===");
    let phase1_start = std::time::Instant::now();

    // Phase 1: Collect root_span_ids from documents matching "authentication"
    let root_collector = RootSpanIdCollector {
        field: root_span_id,
    };
    let root_span_ids = searcher.search(&query, &root_collector)?;

    let phase1_elapsed = phase1_start.elapsed();
    println!("Phase 1 took: {:?}", phase1_elapsed);
    println!("Found {} unique root_span_ids", root_span_ids.len());
    if root_span_ids.len() <= 10 {
        println!("Root IDs: {:?}", root_span_ids);
    }

    // Phase 2: Build TermSetQuery to find all documents with those root_span_ids
    println!("\n=== Phase 2: Fetch all spans for found roots ===");
    let phase2_start = std::time::Instant::now();

    let terms: Vec<Term> = root_span_ids
        .iter()
        .map(|id| Term::from_field_text(root_span_id, id))
        .collect();

    let term_set_query = TermSetQuery::new(terms);

    // Collect all documents with the found root_span_ids
    let doc_collector = DocumentCollector::new(schema.clone());
    let all_related_docs = searcher.search(&term_set_query, &doc_collector)?;

    let phase2_elapsed = phase2_start.elapsed();
    println!("Phase 2 took: {:?}", phase2_elapsed);
    println!(
        "Found {} total documents across all root spans",
        all_related_docs.len()
    );

    println!("\n=== Performance Summary ===");
    let total_time = start.elapsed();
    let index_creation_time = phase1_start.duration_since(start);
    println!("Index creation: {:?}", index_creation_time);
    println!("Phase 1 (find roots): {:?}", phase1_elapsed);
    println!("Phase 2 (expand spans): {:?}", phase2_elapsed);
    println!("Total time: {:?}", total_time);

    Ok(())
}

// First collector: extracts root_span_ids from matching documents
struct RootSpanIdCollector {
    field: Field,
}

impl Collector for RootSpanIdCollector {
    type Fruit = HashSet<String>;
    type Child = RootSpanIdSegmentCollector;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let field_name = segment_reader.schema().get_field_name(self.field);
        let fast_field = segment_reader
            .fast_fields()
            .str(field_name)?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError(format!("Field {} not found", field_name))
            })?;
        Ok(RootSpanIdSegmentCollector {
            fast_field,
            root_span_ids: HashSet::new(),
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        let mut merged = HashSet::new();
        for fruit in fruits {
            merged.extend(fruit);
        }
        Ok(merged)
    }
}

struct RootSpanIdSegmentCollector {
    fast_field: StrColumn,
    root_span_ids: HashSet<String>,
}

impl SegmentCollector for RootSpanIdSegmentCollector {
    type Fruit = HashSet<String>;

    fn collect(&mut self, doc: u32, _score: Score) {
        if let Some(root_id) = self.fast_field.term_ords(doc).next() {
            let mut bytes = Vec::new();
            if self.fast_field.ord_to_bytes(root_id, &mut bytes).unwrap() {
                self.root_span_ids
                    .insert(String::from_utf8_lossy(&bytes).to_string());
            }
        }
    }

    fn harvest(self) -> Self::Fruit {
        self.root_span_ids
    }
}

// Second collector: collects documents with specific fields
struct DocumentCollector {
    schema: Schema,
}

impl DocumentCollector {
    fn new(schema: Schema) -> Self {
        DocumentCollector { schema }
    }
}

impl Collector for DocumentCollector {
    type Fruit = Vec<(String, String, String, f64)>; // (root_span_id, span_id, content, score)
    type Child = DocumentSegmentCollector;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let _root_span_id_field = self.schema.get_field("root_span_id")?;
        let _span_id_field = self.schema.get_field("span_id")?;
        let content_field = self.schema.get_field("content")?;
        let _score_field = self.schema.get_field("score")?;

        let root_span_id_reader = segment_reader
            .fast_fields()
            .str("root_span_id")?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError("root_span_id field not found".to_string())
            })?;
        let span_id_reader = segment_reader
            .fast_fields()
            .str("span_id")?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError("span_id field not found".to_string())
            })?;
        let score_reader = segment_reader.fast_fields().f64("score")?;

        Ok(DocumentSegmentCollector {
            store_reader: segment_reader.get_store_reader(1)?,
            root_span_id_reader,
            span_id_reader,
            score_reader: Some(score_reader),
            content_field,
            docs: Vec::new(),
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        Ok(fruits.into_iter().flatten().collect())
    }
}

struct DocumentSegmentCollector {
    store_reader: tantivy::store::StoreReader,
    root_span_id_reader: StrColumn,
    span_id_reader: StrColumn,
    score_reader: Option<tantivy::columnar::Column<f64>>,
    content_field: Field,
    docs: Vec<(String, String, String, f64)>,
}

impl SegmentCollector for DocumentSegmentCollector {
    type Fruit = Vec<(String, String, String, f64)>;

    fn collect(&mut self, doc: u32, _score: Score) {
        let mut root_span_id = String::new();
        if let Some(ord) = self.root_span_id_reader.term_ords(doc).next() {
            let mut bytes = Vec::new();
            if self
                .root_span_id_reader
                .ord_to_bytes(ord, &mut bytes)
                .unwrap()
            {
                root_span_id = String::from_utf8_lossy(&bytes).to_string();
            }
        }

        let mut span_id = String::new();
        if let Some(ord) = self.span_id_reader.term_ords(doc).next() {
            let mut bytes = Vec::new();
            if self.span_id_reader.ord_to_bytes(ord, &mut bytes).unwrap() {
                span_id = String::from_utf8_lossy(&bytes).to_string();
            }
        }

        let score_val = self
            .score_reader
            .as_ref()
            .and_then(|r| r.first(doc))
            .unwrap_or(0.0);

        let mut content = String::new();
        if let Ok(stored_doc) = self.store_reader.get::<tantivy::TantivyDocument>(doc) {
            for field_value in stored_doc.field_values() {
                if field_value.field() == self.content_field {
                    if let Some(text) = field_value.value().as_str() {
                        content = text.to_string();
                    }
                }
            }
        }

        self.docs.push((root_span_id, span_id, content, score_val));
    }

    fn harvest(self) -> Self::Fruit {
        self.docs
    }
}

// ===== U64 VERSION =====

fn run_two_level_search_benchmark_u64(
    total_spans: usize,
    num_roots: usize,
    avg_spans_per_root: usize,
) -> Result<()> {
    println!(
        "Creating index with {} total spans, {} root spans (using u64 root_span_id)",
        total_spans, num_roots
    );
    let start = std::time::Instant::now();

    // Create schema with u64 root_span_id
    let mut schema_builder = Schema::builder();

    let root_span_id =
        schema_builder.add_u64_field("root_span_id", tantivy::schema::INDEXED | FAST | STORED);
    let span_id = schema_builder.add_text_field("span_id", STORED);
    let content = schema_builder.add_text_field("content", tantivy::schema::TEXT | STORED);
    let score_field = schema_builder.add_f64_field("score", FAST | STORED);

    let schema = schema_builder.build();
    let index = Index::create_in_ram(schema.clone());
    let mut index_writer: IndexWriter = index.writer(500_000_000)?;

    // Add documents in batches
    let mut doc_count = 0;
    let content_variations = vec![
        "error in authentication module",
        "database connection failed",
        "checking user credentials",
        "loading user profile",
        "connecting to postgres",
        "successful operation",
        "processing request",
        "validating input data",
        "executing query",
        "handling response",
    ];

    // Create root documents
    for root_idx in 0..num_roots {
        let root_id = root_idx as u64;
        let content_text = content_variations[root_idx % content_variations.len()];

        index_writer.add_document(doc!(
            root_span_id => root_id,
            span_id => format!("root{}", root_idx),
            content => content_text,
            score_field => 0.9 - (root_idx as f64 * 0.0001)
        ))?;
        doc_count += 1;

        // Create child spans for this root
        let spans_for_this_root = if root_idx < num_roots / 10 {
            // 10% of roots have many more spans (simulating hot traces)
            avg_spans_per_root * 5
        } else {
            avg_spans_per_root
        };

        for span_idx in 0..spans_for_this_root {
            if doc_count >= total_spans {
                break;
            }

            let span_id_str = format!("span_{}_{}", root_idx, span_idx);
            let content_text = content_variations[(root_idx + span_idx) % content_variations.len()];

            index_writer.add_document(doc!(
                root_span_id => root_id,
                span_id => span_id_str,
                content => content_text,
                score_field => 0.5 + (span_idx as f64 * 0.01)
            ))?;
            doc_count += 1;
        }

        if doc_count >= total_spans {
            break;
        }

        // Commit every 100k documents to avoid memory issues
        if doc_count % 100_000 == 0 {
            index_writer.commit()?;
            println!("Committed {} documents...", doc_count);
        }
    }

    index_writer.commit()?;
    println!(
        "Index creation took: {:?}, total documents: {}",
        start.elapsed(),
        doc_count
    );

    let reader = index.reader()?;
    let searcher = reader.searcher();

    // First, let's search for documents containing "authentication"
    let query = TermQuery::new(
        Term::from_field_text(content, "authentication"),
        tantivy::schema::IndexRecordOption::Basic,
    );

    println!("\n=== Phase 1: Search for 'authentication' ===");
    let phase1_start = std::time::Instant::now();

    // Phase 1: Collect root_span_ids from documents matching "authentication"
    let root_collector = RootSpanIdCollectorU64 {
        field: root_span_id,
    };
    let root_span_ids = searcher.search(&query, &root_collector)?;

    let phase1_elapsed = phase1_start.elapsed();
    println!("Phase 1 took: {:?}", phase1_elapsed);
    println!("Found {} unique root_span_ids", root_span_ids.len());
    if root_span_ids.len() <= 10 {
        println!("Root IDs: {:?}", root_span_ids);
    }

    // Phase 2: Build TermSetQuery to find all documents with those root_span_ids
    println!("\n=== Phase 2: Fetch all spans for found roots ===");
    let phase2_start = std::time::Instant::now();

    let terms: Vec<Term> = root_span_ids
        .iter()
        .map(|&id| Term::from_field_u64(root_span_id, id))
        .collect();

    let term_set_query = TermSetQuery::new(terms);

    // Collect all documents with the found root_span_ids
    let doc_collector = DocumentCollectorU64::new(schema.clone());
    let all_related_docs = searcher.search(&term_set_query, &doc_collector)?;

    let phase2_elapsed = phase2_start.elapsed();
    println!("Phase 2 took: {:?}", phase2_elapsed);
    println!(
        "Found {} total documents across all root spans",
        all_related_docs.len()
    );

    println!("\n=== Performance Summary ===");
    let total_time = start.elapsed();
    let index_creation_time = phase1_start.duration_since(start);
    println!("Index creation: {:?}", index_creation_time);
    println!("Phase 1 (find roots): {:?}", phase1_elapsed);
    println!("Phase 2 (expand spans): {:?}", phase2_elapsed);
    println!("Total time: {:?}", total_time);

    Ok(())
}

// First collector for u64: extracts root_span_ids from matching documents
struct RootSpanIdCollectorU64 {
    field: Field,
}

impl Collector for RootSpanIdCollectorU64 {
    type Fruit = HashSet<u64>;
    type Child = RootSpanIdSegmentCollectorU64;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let u64_reader = segment_reader
            .fast_fields()
            .u64(segment_reader.schema().get_field_name(self.field))?;

        Ok(RootSpanIdSegmentCollectorU64 {
            u64_reader,
            root_span_ids: HashSet::new(),
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        let mut merged = HashSet::new();
        for fruit in fruits {
            merged.extend(fruit);
        }
        Ok(merged)
    }
}

struct RootSpanIdSegmentCollectorU64 {
    u64_reader: tantivy::columnar::Column<u64>,
    root_span_ids: HashSet<u64>,
}

impl SegmentCollector for RootSpanIdSegmentCollectorU64 {
    type Fruit = HashSet<u64>;

    fn collect(&mut self, doc: u32, _score: Score) {
        if let Some(root_id) = self.u64_reader.first(doc) {
            self.root_span_ids.insert(root_id);
        }
    }

    fn harvest(self) -> Self::Fruit {
        self.root_span_ids
    }
}

// Second collector for u64
struct DocumentCollectorU64 {
    schema: Schema,
}

impl DocumentCollectorU64 {
    fn new(schema: Schema) -> Self {
        DocumentCollectorU64 { schema }
    }
}

impl Collector for DocumentCollectorU64 {
    type Fruit = Vec<(u64, String, String, f64)>; // (root_span_id, span_id, content, score)
    type Child = DocumentSegmentCollectorU64;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let root_span_id_field = self.schema.get_field("root_span_id")?;
        let content_field = self.schema.get_field("content")?;

        let root_span_id_reader = segment_reader
            .fast_fields()
            .u64(segment_reader.schema().get_field_name(root_span_id_field))?;

        let score_reader = segment_reader.fast_fields().f64("score")?;

        Ok(DocumentSegmentCollectorU64 {
            store_reader: segment_reader.get_store_reader(1)?,
            root_span_id_reader,
            score_reader: Some(score_reader),
            content_field,
            docs: Vec::new(),
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        Ok(fruits.into_iter().flatten().collect())
    }
}

struct DocumentSegmentCollectorU64 {
    store_reader: tantivy::store::StoreReader,
    root_span_id_reader: tantivy::columnar::Column<u64>,
    score_reader: Option<tantivy::columnar::Column<f64>>,
    content_field: Field,
    docs: Vec<(u64, String, String, f64)>,
}

impl SegmentCollector for DocumentSegmentCollectorU64 {
    type Fruit = Vec<(u64, String, String, f64)>;

    fn collect(&mut self, doc: u32, _score: Score) {
        let root_span_id = self.root_span_id_reader.first(doc).unwrap_or(0);
        let score_val = self
            .score_reader
            .as_ref()
            .and_then(|r| r.first(doc))
            .unwrap_or(0.0);

        let mut span_id = String::new();
        let mut content = String::new();

        if let Ok(stored_doc) = self.store_reader.get::<tantivy::TantivyDocument>(doc) {
            for field_value in stored_doc.field_values() {
                if field_value.field() == self.content_field {
                    if let Some(text) = field_value.value().as_str() {
                        content = text.to_string();
                    }
                } else if let Some(text) = field_value.value().as_str() {
                    // This is probably the span_id field
                    if span_id.is_empty() && (text.starts_with("span") || text.starts_with("root"))
                    {
                        span_id = text.to_string();
                    }
                }
            }
        }

        self.docs.push((root_span_id, span_id, content, score_val));
    }

    fn harvest(self) -> Self::Fruit {
        self.docs
    }
}

// ===== BYTES VERSION =====

fn run_two_level_search_benchmark_bytes(
    total_spans: usize,
    num_roots: usize,
    avg_spans_per_root: usize,
) -> Result<()> {
    println!(
        "Creating index with {} total spans, {} root spans (using bytes root_span_id)",
        total_spans, num_roots
    );
    let start = std::time::Instant::now();

    // Create schema with bytes root_span_id
    let mut schema_builder = Schema::builder();

    let root_span_id =
        schema_builder.add_bytes_field("root_span_id", tantivy::schema::INDEXED | FAST | STORED);
    let span_id = schema_builder.add_text_field("span_id", STORED);
    let content = schema_builder.add_text_field("content", tantivy::schema::TEXT | STORED);
    let score_field = schema_builder.add_f64_field("score", FAST | STORED);

    let schema = schema_builder.build();
    let index = Index::create_in_ram(schema.clone());
    let mut index_writer: IndexWriter = index.writer(500_000_000)?;

    // Add documents in batches
    let mut doc_count = 0;
    let content_variations = vec![
        "error in authentication module",
        "database connection failed",
        "checking user credentials",
        "loading user profile",
        "connecting to postgres",
        "successful operation",
        "processing request",
        "validating input data",
        "executing query",
        "handling response",
    ];

    // Create root documents
    for root_idx in 0..num_roots {
        let root_id = format!("root{}", root_idx);
        let root_id_bytes = root_id.as_bytes();
        let content_text = content_variations[root_idx % content_variations.len()];

        index_writer.add_document(doc!(
            root_span_id => root_id_bytes,
            span_id => root_id.clone(),
            content => content_text,
            score_field => 0.9 - (root_idx as f64 * 0.0001)
        ))?;
        doc_count += 1;

        // Create child spans for this root
        let spans_for_this_root = if root_idx < num_roots / 10 {
            // 10% of roots have many more spans (simulating hot traces)
            avg_spans_per_root * 5
        } else {
            avg_spans_per_root
        };

        for span_idx in 0..spans_for_this_root {
            if doc_count >= total_spans {
                break;
            }

            let span_id_str = format!("span_{}_{}", root_idx, span_idx);
            let content_text = content_variations[(root_idx + span_idx) % content_variations.len()];

            index_writer.add_document(doc!(
                root_span_id => root_id_bytes,
                span_id => span_id_str,
                content => content_text,
                score_field => 0.5 + (span_idx as f64 * 0.01)
            ))?;
            doc_count += 1;
        }

        if doc_count >= total_spans {
            break;
        }

        // Commit every 100k documents to avoid memory issues
        if doc_count % 100_000 == 0 {
            index_writer.commit()?;
            println!("Committed {} documents...", doc_count);
        }
    }

    index_writer.commit()?;
    println!(
        "Index creation took: {:?}, total documents: {}",
        start.elapsed(),
        doc_count
    );

    let reader = index.reader()?;
    let searcher = reader.searcher();

    // First, let's search for documents containing "authentication"
    let query = TermQuery::new(
        Term::from_field_text(content, "authentication"),
        tantivy::schema::IndexRecordOption::Basic,
    );

    println!("\n=== Phase 1: Search for 'authentication' ===");
    let phase1_start = std::time::Instant::now();

    // Phase 1: Collect root_span_ids from documents matching "authentication"
    let root_collector = RootSpanIdCollectorBytes {
        field: root_span_id,
    };
    let root_span_ids = searcher.search(&query, &root_collector)?;

    let phase1_elapsed = phase1_start.elapsed();
    println!("Phase 1 took: {:?}", phase1_elapsed);
    println!("Found {} unique root_span_ids", root_span_ids.len());
    if root_span_ids.len() <= 10 {
        println!(
            "Root IDs: {:?}",
            root_span_ids
                .iter()
                .map(|b| String::from_utf8_lossy(b))
                .collect::<Vec<_>>()
        );
    }

    // Phase 2: Build TermSetQuery to find all documents with those root_span_ids
    println!("\n=== Phase 2: Fetch all spans for found roots ===");
    let phase2_start = std::time::Instant::now();

    let terms: Vec<Term> = root_span_ids
        .iter()
        .map(|id| Term::from_field_bytes(root_span_id, id))
        .collect();

    let term_set_query = TermSetQuery::new(terms);

    // Collect all documents with the found root_span_ids
    let doc_collector = DocumentCollectorBytes::new(schema.clone());
    let all_related_docs = searcher.search(&term_set_query, &doc_collector)?;

    let phase2_elapsed = phase2_start.elapsed();
    println!("Phase 2 took: {:?}", phase2_elapsed);
    println!(
        "Found {} total documents across all root spans",
        all_related_docs.len()
    );

    println!("\n=== Performance Summary ===");
    let total_time = start.elapsed();
    let index_creation_time = phase1_start.duration_since(start);
    println!("Index creation: {:?}", index_creation_time);
    println!("Phase 1 (find roots): {:?}", phase1_elapsed);
    println!("Phase 2 (expand spans): {:?}", phase2_elapsed);
    println!("Total time: {:?}", total_time);

    Ok(())
}

// First collector for bytes: extracts root_span_ids from matching documents
struct RootSpanIdCollectorBytes {
    field: Field,
}

impl Collector for RootSpanIdCollectorBytes {
    type Fruit = HashSet<Vec<u8>>;
    type Child = RootSpanIdSegmentCollectorBytes;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let bytes_reader = segment_reader
            .fast_fields()
            .bytes(segment_reader.schema().get_field_name(self.field))?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError("root_span_id field not found".to_string())
            })?;

        Ok(RootSpanIdSegmentCollectorBytes {
            bytes_reader,
            root_span_ids: HashSet::new(),
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        let mut merged = HashSet::new();
        for fruit in fruits {
            merged.extend(fruit);
        }
        Ok(merged)
    }
}

struct RootSpanIdSegmentCollectorBytes {
    bytes_reader: tantivy::columnar::BytesColumn,
    root_span_ids: HashSet<Vec<u8>>,
}

impl SegmentCollector for RootSpanIdSegmentCollectorBytes {
    type Fruit = HashSet<Vec<u8>>;

    fn collect(&mut self, doc: u32, _score: Score) {
        if let Some(ord) = self.bytes_reader.term_ords(doc).next() {
            let mut bytes = Vec::new();
            if self.bytes_reader.ord_to_bytes(ord, &mut bytes).unwrap() {
                self.root_span_ids.insert(bytes);
            }
        }
    }

    fn harvest(self) -> Self::Fruit {
        self.root_span_ids
    }
}

// Second collector for bytes
struct DocumentCollectorBytes {
    schema: Schema,
}

impl DocumentCollectorBytes {
    fn new(schema: Schema) -> Self {
        DocumentCollectorBytes { schema }
    }
}

impl Collector for DocumentCollectorBytes {
    type Fruit = Vec<(Vec<u8>, String, String, f64)>; // (root_span_id, span_id, content, score)
    type Child = DocumentSegmentCollectorBytes;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let root_span_id_field = self.schema.get_field("root_span_id")?;
        let content_field = self.schema.get_field("content")?;

        let root_span_id_reader = segment_reader
            .fast_fields()
            .bytes(segment_reader.schema().get_field_name(root_span_id_field))?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError("root_span_id field not found".to_string())
            })?;

        let score_reader = segment_reader.fast_fields().f64("score")?;

        Ok(DocumentSegmentCollectorBytes {
            store_reader: segment_reader.get_store_reader(1)?,
            root_span_id_reader,
            score_reader: Some(score_reader),
            content_field,
            docs: Vec::new(),
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        Ok(fruits.into_iter().flatten().collect())
    }
}

struct DocumentSegmentCollectorBytes {
    store_reader: tantivy::store::StoreReader,
    root_span_id_reader: tantivy::columnar::BytesColumn,
    score_reader: Option<tantivy::columnar::Column<f64>>,
    content_field: Field,
    docs: Vec<(Vec<u8>, String, String, f64)>,
}

impl SegmentCollector for DocumentSegmentCollectorBytes {
    type Fruit = Vec<(Vec<u8>, String, String, f64)>;

    fn collect(&mut self, doc: u32, _score: Score) {
        let mut root_span_id = Vec::new();
        if let Some(ord) = self.root_span_id_reader.term_ords(doc).next() {
            self.root_span_id_reader
                .ord_to_bytes(ord, &mut root_span_id)
                .unwrap();
        }

        let score_val = self
            .score_reader
            .as_ref()
            .and_then(|r| r.first(doc))
            .unwrap_or(0.0);

        let mut span_id = String::new();
        let mut content = String::new();

        if let Ok(stored_doc) = self.store_reader.get::<tantivy::TantivyDocument>(doc) {
            for field_value in stored_doc.field_values() {
                if field_value.field() == self.content_field {
                    if let Some(text) = field_value.value().as_str() {
                        content = text.to_string();
                    }
                } else if let Some(text) = field_value.value().as_str() {
                    // This is probably the span_id field
                    if span_id.is_empty() && (text.starts_with("span") || text.starts_with("root"))
                    {
                        span_id = text.to_string();
                    }
                }
            }
        }

        self.docs.push((root_span_id, span_id, content, score_val));
    }

    fn harvest(self) -> Self::Fruit {
        self.docs
    }
}

// ===== CHUNKED SEARCH IMPLEMENTATION =====

pub fn run_chunked_search_benchmark(
    total_spans: usize,
    num_roots: usize,
    avg_spans_per_root: usize,
    chunk_size: usize,
) -> Result<()> {
    println!(
        "Creating index with {} total spans, {} root spans (using chunked approach with chunk size {})",
        total_spans, num_roots, chunk_size
    );
    let start = std::time::Instant::now();

    // Create schema with root_span_id and span_id fields
    let mut schema_builder = Schema::builder();
    let text_field_indexing = TextFieldIndexing::default()
        .set_tokenizer("raw")
        .set_index_option(tantivy::schema::IndexRecordOption::Basic);
    let text_options = TextOptions::default()
        .set_indexing_options(text_field_indexing)
        .set_fast(Some("raw"))
        .set_stored();

    let root_span_id = schema_builder.add_text_field("root_span_id", text_options.clone());
    let span_id = schema_builder.add_text_field("span_id", text_options.clone());
    let content = schema_builder.add_text_field("content", tantivy::schema::TEXT | STORED);
    let score_field = schema_builder.add_f64_field("score", FAST | STORED);

    let schema = schema_builder.build();
    let index = Index::create_in_ram(schema.clone());
    let mut index_writer: IndexWriter = index.writer(500_000_000)?;

    // Add documents in batches (same as before)
    let mut doc_count = 0;
    let content_variations = [
        "error in authentication module",
        "database connection failed",
        "checking user credentials",
        "loading user profile",
        "connecting to postgres",
        "successful operation",
        "processing request",
        "validating input data",
        "executing query",
        "handling response",
    ];

    // Create root documents
    for root_idx in 0..num_roots {
        let root_id = format!("root{}", root_idx);
        let content_text = content_variations[root_idx % content_variations.len()];

        index_writer.add_document(doc!(
            root_span_id => root_id.clone(),
            span_id => root_id.clone(),
            content => content_text,
            score_field => 0.9 - (root_idx as f64 * 0.0001)
        ))?;
        doc_count += 1;

        // Create child spans for this root
        let spans_for_this_root = if root_idx < num_roots / 10 {
            // 10% of roots have many more spans (simulating hot traces)
            avg_spans_per_root * 5
        } else {
            avg_spans_per_root
        };

        for span_idx in 0..spans_for_this_root {
            if doc_count >= total_spans {
                break;
            }

            let span_id_str = format!("span_{}_{}", root_idx, span_idx);
            let content_text = content_variations[(root_idx + span_idx) % content_variations.len()];

            index_writer.add_document(doc!(
                root_span_id => root_id.clone(),
                span_id => span_id_str,
                content => content_text,
                score_field => 0.5 + (span_idx as f64 * 0.01)
            ))?;
            doc_count += 1;
        }

        if doc_count >= total_spans {
            break;
        }

        // Commit every 100k documents to avoid memory issues
        if doc_count % 100_000 == 0 {
            index_writer.commit()?;
            println!("Committed {} documents...", doc_count);
        }
    }

    index_writer.commit()?;
    println!(
        "Index creation took: {:?}, total documents: {}",
        start.elapsed(),
        doc_count
    );

    let reader = index.reader()?;
    let searcher = reader.searcher();

    // Chunked search approach
    println!("\n=== Chunked Search for 'authentication' ===");
    let search_start = std::time::Instant::now();

    let query = TermQuery::new(
        Term::from_field_text(content, "authentication"),
        tantivy::schema::IndexRecordOption::Basic,
    );

    // DocumentCollector is already defined in this module

    // Process documents in chunks
    let mut all_matching_docs = Vec::new();
    let mut total_chunks = 0;

    // Calculate number of chunks needed
    let num_chunks = (doc_count + chunk_size - 1) / chunk_size;

    for chunk_idx in 0..num_chunks {
        let chunk_start = std::time::Instant::now();
        let chunk_start_doc = chunk_idx * chunk_size;
        let chunk_end_doc = std::cmp::min((chunk_idx + 1) * chunk_size, doc_count);

        println!(
            "Processing chunk {} (docs {} to {})",
            chunk_idx + 1,
            chunk_start_doc,
            chunk_end_doc
        );

        // Phase 1 for this chunk: Find matching documents
        let chunk_root_collector = ChunkedRootSpanIdCollector {
            field: root_span_id,
            start_doc: chunk_start_doc,
            end_doc: chunk_end_doc,
        };

        let chunk_root_span_ids = searcher.search(&query, &chunk_root_collector)?;

        if !chunk_root_span_ids.is_empty() {
            println!(
                "  Found {} unique root_span_ids in chunk",
                chunk_root_span_ids.len()
            );

            // Phase 2 for this chunk: Expand to all related spans
            let terms: Vec<Term> = chunk_root_span_ids
                .iter()
                .map(|id| Term::from_field_text(root_span_id, id))
                .collect();

            let term_set_query = TermSetQuery::new(terms);
            let doc_collector = DocumentCollector::new(schema.clone());
            let chunk_docs = searcher.search(&term_set_query, &doc_collector)?;

            println!("  Found {} related documents in chunk", chunk_docs.len());
            all_matching_docs.extend(chunk_docs);
        }

        println!("  Chunk processing took: {:?}", chunk_start.elapsed());
        total_chunks += 1;
    }

    let search_elapsed = search_start.elapsed();
    println!("\nChunked search took: {:?}", search_elapsed);
    println!(
        "Found {} total documents across {} chunks",
        all_matching_docs.len(),
        total_chunks
    );

    println!("\n=== Performance Summary (Chunked) ===");
    let total_time = start.elapsed();
    let index_creation_time = search_start.duration_since(start);
    println!("Index creation: {:?}", index_creation_time);
    println!("Chunked search: {:?}", search_elapsed);
    println!("Total time: {:?}", total_time);

    Ok(())
}

// Chunked collector that only processes documents within a specific range
struct ChunkedRootSpanIdCollector {
    field: Field,
    start_doc: usize,
    end_doc: usize,
}

impl Collector for ChunkedRootSpanIdCollector {
    type Fruit = HashSet<String>;
    type Child = ChunkedRootSpanIdSegmentCollector;

    fn for_segment(
        &self,
        segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let field_name = segment_reader.schema().get_field_name(self.field);
        let fast_field = segment_reader
            .fast_fields()
            .str(field_name)?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError(format!("Field {} not found", field_name))
            })?;

        // Calculate segment offset
        let segment_base = segment_local_id as usize * 1_000_000; // Approximate segment size

        Ok(ChunkedRootSpanIdSegmentCollector {
            fast_field,
            root_span_ids: HashSet::new(),
            start_doc: self.start_doc,
            end_doc: self.end_doc,
            segment_base,
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        let mut merged = HashSet::new();
        for fruit in fruits {
            merged.extend(fruit);
        }
        Ok(merged)
    }
}

struct ChunkedRootSpanIdSegmentCollector {
    fast_field: StrColumn,
    root_span_ids: HashSet<String>,
    start_doc: usize,
    end_doc: usize,
    segment_base: usize,
}

impl SegmentCollector for ChunkedRootSpanIdSegmentCollector {
    type Fruit = HashSet<String>;

    fn collect(&mut self, doc: u32, _score: Score) {
        let global_doc = self.segment_base + doc as usize;

        // Only process documents within our chunk range
        if global_doc >= self.start_doc && global_doc < self.end_doc {
            if let Some(root_id) = self.fast_field.term_ords(doc).next() {
                let mut bytes = Vec::new();
                if self.fast_field.ord_to_bytes(root_id, &mut bytes).unwrap() {
                    self.root_span_ids
                        .insert(String::from_utf8_lossy(&bytes).to_string());
                }
            }
        }
    }

    fn harvest(self) -> Self::Fruit {
        self.root_span_ids
    }
}

// U64 version of chunked search
pub fn run_chunked_search_benchmark_u64(
    total_spans: usize,
    num_roots: usize,
    avg_spans_per_root: usize,
    chunk_size: usize,
) -> Result<()> {
    println!(
        "Creating index with {} total spans, {} root spans (using u64 chunked approach with chunk size {})",
        total_spans, num_roots, chunk_size
    );
    let start = std::time::Instant::now();

    // Create schema with u64 root_span_id
    let mut schema_builder = Schema::builder();

    let root_span_id =
        schema_builder.add_u64_field("root_span_id", tantivy::schema::INDEXED | FAST | STORED);
    let span_id = schema_builder.add_text_field("span_id", STORED);
    let content = schema_builder.add_text_field("content", tantivy::schema::TEXT | STORED);
    let score_field = schema_builder.add_f64_field("score", FAST | STORED);

    let schema = schema_builder.build();
    let index = Index::create_in_ram(schema.clone());
    let mut index_writer: IndexWriter = index.writer(500_000_000)?;

    // Add documents in batches
    let mut doc_count = 0;
    let content_variations = vec![
        "error in authentication module",
        "database connection failed",
        "checking user credentials",
        "loading user profile",
        "connecting to postgres",
        "successful operation",
        "processing request",
        "validating input data",
        "executing query",
        "handling response",
    ];

    // Create root documents
    for root_idx in 0..num_roots {
        let root_id = root_idx as u64;
        let content_text = content_variations[root_idx % content_variations.len()];

        index_writer.add_document(doc!(
            root_span_id => root_id,
            span_id => format!("root{}", root_idx),
            content => content_text,
            score_field => 0.9 - (root_idx as f64 * 0.0001)
        ))?;
        doc_count += 1;

        // Create child spans for this root
        let spans_for_this_root = if root_idx < num_roots / 10 {
            avg_spans_per_root * 5
        } else {
            avg_spans_per_root
        };

        for span_idx in 0..spans_for_this_root {
            if doc_count >= total_spans {
                break;
            }

            let span_id_str = format!("span_{}_{}", root_idx, span_idx);
            let content_text = content_variations[(root_idx + span_idx) % content_variations.len()];

            index_writer.add_document(doc!(
                root_span_id => root_id,
                span_id => span_id_str,
                content => content_text,
                score_field => 0.5 + (span_idx as f64 * 0.01)
            ))?;
            doc_count += 1;
        }

        if doc_count >= total_spans {
            break;
        }

        // Commit every 100k documents to avoid memory issues
        if doc_count % 100_000 == 0 {
            index_writer.commit()?;
            println!("Committed {} documents...", doc_count);
        }
    }

    index_writer.commit()?;
    println!(
        "Index creation took: {:?}, total documents: {}",
        start.elapsed(),
        doc_count
    );

    let reader = index.reader()?;
    let searcher = reader.searcher();

    // Chunked search approach
    println!("\n=== Chunked Search for 'authentication' (u64) ===");
    let search_start = std::time::Instant::now();

    let query = TermQuery::new(
        Term::from_field_text(content, "authentication"),
        tantivy::schema::IndexRecordOption::Basic,
    );

    // DocumentCollectorU64 is already defined in this module

    // Process documents in chunks
    let mut all_matching_docs = Vec::new();
    let mut total_chunks = 0;

    // Calculate number of chunks needed
    let num_chunks = (doc_count + chunk_size - 1) / chunk_size;

    for chunk_idx in 0..num_chunks {
        let chunk_start = std::time::Instant::now();
        let chunk_start_doc = chunk_idx * chunk_size;
        let chunk_end_doc = std::cmp::min((chunk_idx + 1) * chunk_size, doc_count);

        println!(
            "Processing chunk {} (docs {} to {})",
            chunk_idx + 1,
            chunk_start_doc,
            chunk_end_doc
        );

        // Phase 1 for this chunk: Find matching documents
        let chunk_root_collector = ChunkedRootSpanIdCollectorU64 {
            field: root_span_id,
            start_doc: chunk_start_doc,
            end_doc: chunk_end_doc,
        };

        let chunk_root_span_ids = searcher.search(&query, &chunk_root_collector)?;

        if !chunk_root_span_ids.is_empty() {
            println!(
                "  Found {} unique root_span_ids in chunk",
                chunk_root_span_ids.len()
            );

            // Phase 2 for this chunk: Expand to all related spans
            let terms: Vec<Term> = chunk_root_span_ids
                .iter()
                .map(|&id| Term::from_field_u64(root_span_id, id))
                .collect();

            let term_set_query = TermSetQuery::new(terms);
            let doc_collector = DocumentCollectorU64::new(schema.clone());
            let chunk_docs = searcher.search(&term_set_query, &doc_collector)?;

            println!("  Found {} related documents in chunk", chunk_docs.len());
            all_matching_docs.extend(chunk_docs);
        }

        println!("  Chunk processing took: {:?}", chunk_start.elapsed());
        total_chunks += 1;
    }

    let search_elapsed = search_start.elapsed();
    println!("\nChunked search took: {:?}", search_elapsed);
    println!(
        "Found {} total documents across {} chunks",
        all_matching_docs.len(),
        total_chunks
    );

    println!("\n=== Performance Summary (Chunked u64) ===");
    let total_time = start.elapsed();
    let index_creation_time = search_start.duration_since(start);
    println!("Index creation: {:?}", index_creation_time);
    println!("Chunked search: {:?}", search_elapsed);
    println!("Total time: {:?}", total_time);

    Ok(())
}

// Chunked collector for u64
struct ChunkedRootSpanIdCollectorU64 {
    field: Field,
    start_doc: usize,
    end_doc: usize,
}

impl Collector for ChunkedRootSpanIdCollectorU64 {
    type Fruit = HashSet<u64>;
    type Child = ChunkedRootSpanIdSegmentCollectorU64;

    fn for_segment(
        &self,
        segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let u64_reader = segment_reader
            .fast_fields()
            .u64(segment_reader.schema().get_field_name(self.field))?;

        // Calculate segment offset
        let segment_base = segment_local_id as usize * 1_000_000; // Approximate segment size

        Ok(ChunkedRootSpanIdSegmentCollectorU64 {
            u64_reader,
            root_span_ids: HashSet::new(),
            start_doc: self.start_doc,
            end_doc: self.end_doc,
            segment_base,
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        let mut merged = HashSet::new();
        for fruit in fruits {
            merged.extend(fruit);
        }
        Ok(merged)
    }
}

struct ChunkedRootSpanIdSegmentCollectorU64 {
    u64_reader: tantivy::columnar::Column<u64>,
    root_span_ids: HashSet<u64>,
    start_doc: usize,
    end_doc: usize,
    segment_base: usize,
}

impl SegmentCollector for ChunkedRootSpanIdSegmentCollectorU64 {
    type Fruit = HashSet<u64>;

    fn collect(&mut self, doc: u32, _score: Score) {
        let global_doc = self.segment_base + doc as usize;

        // Only process documents within our chunk range
        if global_doc >= self.start_doc && global_doc < self.end_doc {
            if let Some(root_id) = self.u64_reader.first(doc) {
                self.root_span_ids.insert(root_id);
            }
        }
    }

    fn harvest(self) -> Self::Fruit {
        self.root_span_ids
    }
}
