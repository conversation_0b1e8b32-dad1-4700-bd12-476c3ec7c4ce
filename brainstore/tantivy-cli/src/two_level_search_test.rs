// This example demonstrates a two-level search approach using Tantivy collectors:
// 1. First collector searches for documents matching a query and extracts root_span_ids
// 2. Second collector uses TermSetQuery to find all documents with those root_span_ids
// 3. Optional: Combine scoring from both levels for advanced ranking
//
// This is similar to the "expand" functionality in brainstore but implemented
// as reusable collectors that can be composed together.

use std::collections::HashSet;

use tantivy::{
    collector::{Collector, SegmentCollector},
    columnar::StrColumn,
    doc,
    query::{TermQuery, TermSetQuery},
    schema::{Field, Schema, TextFieldIndexing, TextOptions, Value, FAST, STORED},
    Index, IndexWriter, Score, SegmentReader, Term,
};
use util::anyhow::Result;

#[test]
fn test_two_level_search() -> Result<()> {
    test_two_level_search_with_params(6, 3, 2)
}

#[test]
fn benchmark_two_level_search() -> Result<()> {
    // 10k total spans, 1k root spans = ~10 spans per root
    test_two_level_search_with_params(10_000, 1_000, 10)
}

#[test]
#[ignore] // Run with: cargo test benchmark_two_level_search_large -- --ignored --nocapture
fn benchmark_two_level_search_large() -> Result<()> {
    // 1M total spans, 10k root spans = ~100 spans per root
    test_two_level_search_with_params(1_000_000, 10_000, 100)
}

fn test_two_level_search_with_params(
    total_spans: usize,
    num_roots: usize,
    avg_spans_per_root: usize,
) -> Result<()> {
    println!(
        "Creating index with {} total spans, {} root spans",
        total_spans, num_roots
    );
    let start = std::time::Instant::now();

    // Create schema with root_span_id and span_id fields
    let mut schema_builder = Schema::builder();
    let text_field_indexing = TextFieldIndexing::default()
        .set_tokenizer("raw")
        .set_index_option(tantivy::schema::IndexRecordOption::Basic);
    let text_options = TextOptions::default()
        .set_indexing_options(text_field_indexing)
        .set_fast(Some("raw"))
        .set_stored();

    let root_span_id = schema_builder.add_text_field("root_span_id", text_options.clone());
    let span_id = schema_builder.add_text_field("span_id", text_options.clone());
    let content = schema_builder.add_text_field("content", tantivy::schema::TEXT | STORED);
    let score_field = schema_builder.add_f64_field("score", FAST | STORED);

    let schema = schema_builder.build();
    let index = Index::create_in_ram(schema.clone());
    let mut index_writer: IndexWriter = index.writer(500_000_000)?;

    // Add documents in batches
    let mut doc_count = 0;
    let content_variations = vec![
        "error in authentication module",
        "database connection failed",
        "checking user credentials",
        "loading user profile",
        "connecting to postgres",
        "successful operation",
        "processing request",
        "validating input data",
        "executing query",
        "handling response",
    ];

    // Create root documents
    for root_idx in 0..num_roots {
        let root_id = format!("root{}", root_idx);
        let content_text = content_variations[root_idx % content_variations.len()];

        index_writer.add_document(doc!(
            root_span_id => root_id.clone(),
            span_id => root_id.clone(),
            content => content_text,
            score_field => 0.9 - (root_idx as f64 * 0.0001)
        ))?;
        doc_count += 1;

        // Create child spans for this root
        let spans_for_this_root = if root_idx < num_roots / 10 {
            // 10% of roots have many more spans (simulating hot traces)
            avg_spans_per_root * 5
        } else {
            avg_spans_per_root
        };

        for span_idx in 0..spans_for_this_root {
            if doc_count >= total_spans {
                break;
            }

            let span_id_str = format!("span_{}_{}", root_idx, span_idx);
            let content_text = content_variations[(root_idx + span_idx) % content_variations.len()];

            index_writer.add_document(doc!(
                root_span_id => root_id.clone(),
                span_id => span_id_str,
                content => content_text,
                score_field => 0.5 + (span_idx as f64 * 0.01)
            ))?;
            doc_count += 1;
        }

        if doc_count >= total_spans {
            break;
        }

        // Commit every 100k documents to avoid memory issues
        if doc_count % 100_000 == 0 {
            index_writer.commit()?;
            println!("Committed {} documents...", doc_count);
        }
    }

    index_writer.commit()?;
    println!(
        "Index creation took: {:?}, total documents: {}",
        start.elapsed(),
        doc_count
    );

    let reader = index.reader()?;
    let searcher = reader.searcher();

    // First, let's search for documents containing "authentication"
    let query = TermQuery::new(
        Term::from_field_text(content, "authentication"),
        tantivy::schema::IndexRecordOption::Basic,
    );

    println!("\n=== Phase 1: Search for 'authentication' ===");
    let phase1_start = std::time::Instant::now();

    // Phase 1: Collect root_span_ids from documents matching "authentication"
    let root_collector = RootSpanIdCollector {
        field: root_span_id,
    };
    let root_span_ids = searcher.search(&query, &root_collector)?;

    let phase1_elapsed = phase1_start.elapsed();
    println!("Phase 1 took: {:?}", phase1_elapsed);
    println!("Found {} unique root_span_ids", root_span_ids.len());
    if root_span_ids.len() <= 10 {
        println!("Root IDs: {:?}", root_span_ids);
    }

    // Phase 2: Build TermSetQuery to find all documents with those root_span_ids
    println!("\n=== Phase 2: Fetch all spans for found roots ===");
    let phase2_start = std::time::Instant::now();

    let terms: Vec<Term> = root_span_ids
        .iter()
        .map(|id| Term::from_field_text(root_span_id, id))
        .collect();

    let term_set_query = TermSetQuery::new(terms);

    // Collect all documents with the found root_span_ids
    let doc_collector = DocumentCollector::new(schema.clone());
    let all_related_docs = searcher.search(&term_set_query, &doc_collector)?;

    let phase2_elapsed = phase2_start.elapsed();
    println!("Phase 2 took: {:?}", phase2_elapsed);
    println!(
        "Found {} total documents across all root spans",
        all_related_docs.len()
    );

    // Print sample results only if small dataset
    if all_related_docs.len() <= 10 {
        for (root_id, span, cont, score) in &all_related_docs {
            println!(
                "root_span_id: {}, span_id: {}, content: '{}', score: {}",
                root_id, span, cont, score
            );
        }
    }

    // Example of scoring both levels
    println!("\n=== Two-level scoring example ===");
    let scoring_start = std::time::Instant::now();

    let scored_results = two_level_scored_search(&searcher, &query, root_span_id, content)?;

    let scoring_elapsed = scoring_start.elapsed();
    println!("Two-level scoring took: {:?}", scoring_elapsed);
    println!("Processed {} root spans with scoring", scored_results.len());

    // Calculate average documents per root
    let total_docs: usize = scored_results.iter().map(|(_, docs)| docs.len()).sum();
    let avg_docs_per_root = if scored_results.len() > 0 {
        total_docs as f64 / scored_results.len() as f64
    } else {
        0.0
    };
    println!("Average documents per root: {:.1}", avg_docs_per_root);

    // Print sample results only if small dataset
    if scored_results.len() <= 3 {
        for (root_id, docs) in scored_results {
            println!("\nRoot span {}: ", root_id);
            for (_root, span, cont, doc_score, combined) in docs.into_iter().take(3) {
                println!(
                    "  - span_id: {}, content: '{}', doc_score: {:.2}, combined_score: {:.3}",
                    span, cont, doc_score, combined
                );
            }
        }
    }

    println!("\n=== Performance Summary ===");
    let total_time = start.elapsed();
    let index_creation_time = phase1_start.duration_since(start);
    println!("Index creation: {:?}", index_creation_time);
    println!("Phase 1 (find roots): {:?}", phase1_elapsed);
    println!("Phase 2 (expand spans): {:?}", phase2_elapsed);
    println!("Two-level scoring: {:?}", scoring_elapsed);
    println!("Total time: {:?}", total_time);

    Ok(())
}

// First collector: extracts root_span_ids from matching documents
struct RootSpanIdCollector {
    field: Field,
}

impl Collector for RootSpanIdCollector {
    type Fruit = HashSet<String>;
    type Child = RootSpanIdSegmentCollector;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let field_name = segment_reader.schema().get_field_name(self.field);
        let fast_field = segment_reader
            .fast_fields()
            .str(field_name)?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError(format!("Field {} not found", field_name))
            })?;
        Ok(RootSpanIdSegmentCollector {
            fast_field,
            root_span_ids: HashSet::new(),
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        let mut merged = HashSet::new();
        for fruit in fruits {
            merged.extend(fruit);
        }
        Ok(merged)
    }
}

struct RootSpanIdSegmentCollector {
    fast_field: StrColumn,
    root_span_ids: HashSet<String>,
}

impl SegmentCollector for RootSpanIdSegmentCollector {
    type Fruit = HashSet<String>;

    fn collect(&mut self, doc: u32, _score: Score) {
        if let Some(root_id) = self.fast_field.term_ords(doc).next() {
            let mut bytes = Vec::new();
            if self.fast_field.ord_to_bytes(root_id, &mut bytes).unwrap() {
                self.root_span_ids
                    .insert(String::from_utf8_lossy(&bytes).to_string());
            }
        }
    }

    fn harvest(self) -> Self::Fruit {
        self.root_span_ids
    }
}

// Second collector: collects documents with specific fields
struct DocumentCollector {
    schema: Schema,
}

impl DocumentCollector {
    fn new(schema: Schema) -> Self {
        DocumentCollector { schema }
    }
}

impl Collector for DocumentCollector {
    type Fruit = Vec<(String, String, String, f64)>; // (root_span_id, span_id, content, score)
    type Child = DocumentSegmentCollector;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let _root_span_id_field = self.schema.get_field("root_span_id")?;
        let _span_id_field = self.schema.get_field("span_id")?;
        let content_field = self.schema.get_field("content")?;
        let _score_field = self.schema.get_field("score")?;

        let root_span_id_reader = segment_reader
            .fast_fields()
            .str("root_span_id")?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError("root_span_id field not found".to_string())
            })?;
        let span_id_reader = segment_reader
            .fast_fields()
            .str("span_id")?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError("span_id field not found".to_string())
            })?;
        let score_reader = segment_reader.fast_fields().f64("score")?;

        Ok(DocumentSegmentCollector {
            store_reader: segment_reader.get_store_reader(1)?,
            root_span_id_reader,
            span_id_reader,
            score_reader: Some(score_reader),
            content_field,
            docs: Vec::new(),
        })
    }

    fn requires_scoring(&self) -> bool {
        false
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        Ok(fruits.into_iter().flatten().collect())
    }
}

struct DocumentSegmentCollector {
    store_reader: tantivy::store::StoreReader,
    root_span_id_reader: StrColumn,
    span_id_reader: StrColumn,
    score_reader: Option<tantivy::columnar::Column<f64>>,
    content_field: Field,
    docs: Vec<(String, String, String, f64)>,
}

impl SegmentCollector for DocumentSegmentCollector {
    type Fruit = Vec<(String, String, String, f64)>;

    fn collect(&mut self, doc: u32, _score: Score) {
        let mut root_span_id = String::new();
        if let Some(ord) = self.root_span_id_reader.term_ords(doc).next() {
            let mut bytes = Vec::new();
            if self
                .root_span_id_reader
                .ord_to_bytes(ord, &mut bytes)
                .unwrap()
            {
                root_span_id = String::from_utf8_lossy(&bytes).to_string();
            }
        }

        let mut span_id = String::new();
        if let Some(ord) = self.span_id_reader.term_ords(doc).next() {
            let mut bytes = Vec::new();
            if self.span_id_reader.ord_to_bytes(ord, &mut bytes).unwrap() {
                span_id = String::from_utf8_lossy(&bytes).to_string();
            }
        }

        let score_val = self
            .score_reader
            .as_ref()
            .and_then(|r| r.first(doc))
            .unwrap_or(0.0);

        let mut content = String::new();
        if let Ok(stored_doc) = self.store_reader.get::<tantivy::TantivyDocument>(doc) {
            for field_value in stored_doc.field_values() {
                if field_value.field() == self.content_field {
                    if let Some(text) = field_value.value().as_str() {
                        content = text.to_string();
                    }
                }
            }
        }

        self.docs.push((root_span_id, span_id, content, score_val));
    }

    fn harvest(self) -> Self::Fruit {
        self.docs
    }
}

// Example of how to combine scores from both levels
fn two_level_scored_search(
    searcher: &tantivy::Searcher,
    initial_query: &TermQuery,
    root_span_id_field: Field,
    _content_field: Field,
) -> Result<Vec<(String, Vec<(String, String, String, f64, f32)>)>> {
    // Phase 1: Get matching documents with scores
    let scored_collector = ScoredRootCollector {
        field: root_span_id_field,
    };
    let (root_ids_with_scores, max_score) = searcher.search(initial_query, &scored_collector)?;

    let mut results = Vec::new();

    // Phase 2: For each root, get all related documents
    for (root_id, first_level_score) in root_ids_with_scores {
        let term = Term::from_field_text(root_span_id_field, &root_id);
        let term_query = TermQuery::new(term, tantivy::schema::IndexRecordOption::Basic);

        // Collect documents with custom scoring
        let doc_collector =
            ScoredDocumentCollector::new(first_level_score, max_score, searcher.schema().clone());
        let docs_with_scores = searcher.search(&term_query, &doc_collector)?;

        results.push((root_id, docs_with_scores));
    }

    Ok(results)
}

// Collector that preserves scores from first level
struct ScoredRootCollector {
    field: Field,
}

impl Collector for ScoredRootCollector {
    type Fruit = (Vec<(String, f32)>, f32); // (root_ids with scores, max_score)
    type Child = ScoredRootSegmentCollector;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let field_name = segment_reader.schema().get_field_name(self.field);
        let fast_field = segment_reader
            .fast_fields()
            .str(field_name)?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError(format!("Field {} not found", field_name))
            })?;
        Ok(ScoredRootSegmentCollector {
            fast_field,
            root_scores: Vec::new(),
        })
    }

    fn requires_scoring(&self) -> bool {
        true // We need scores!
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        let mut merged = Vec::new();
        let mut max_score = 0.0f32;

        for (items, fruit_max) in fruits {
            merged.extend(items);
            max_score = max_score.max(fruit_max);
        }

        // Deduplicate by keeping highest score for each root_id
        let mut score_map = std::collections::HashMap::new();
        for (root_id, score) in merged {
            score_map
                .entry(root_id)
                .and_modify(|s: &mut f32| *s = (*s).max(score))
                .or_insert(score);
        }

        Ok((score_map.into_iter().collect(), max_score))
    }
}

struct ScoredRootSegmentCollector {
    fast_field: StrColumn,
    root_scores: Vec<(String, f32)>,
}

impl SegmentCollector for ScoredRootSegmentCollector {
    type Fruit = (Vec<(String, f32)>, f32);

    fn collect(&mut self, doc: u32, score: Score) {
        if let Some(root_id) = self.fast_field.term_ords(doc).next() {
            let mut bytes = Vec::new();
            if self.fast_field.ord_to_bytes(root_id, &mut bytes).unwrap() {
                self.root_scores
                    .push((String::from_utf8_lossy(&bytes).to_string(), score));
            }
        }
    }

    fn harvest(self) -> Self::Fruit {
        let max_score = self
            .root_scores
            .iter()
            .map(|(_, s)| *s)
            .max_by(|a, b| a.partial_cmp(b).unwrap())
            .unwrap_or(0.0);
        (self.root_scores, max_score)
    }
}

// Document collector that combines scores
struct ScoredDocumentCollector {
    first_level_score: f32,
    first_level_max: f32,
    schema: Schema,
}

impl ScoredDocumentCollector {
    fn new(first_level_score: f32, first_level_max: f32, schema: Schema) -> Self {
        ScoredDocumentCollector {
            first_level_score,
            first_level_max,
            schema,
        }
    }
}

impl Collector for ScoredDocumentCollector {
    type Fruit = Vec<(String, String, String, f64, f32)>; // (root_span_id, span_id, content, score, combined_score)
    type Child = ScoredDocumentSegmentCollector;

    fn for_segment(
        &self,
        _segment_local_id: u32,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<Self::Child> {
        let root_span_id_reader = segment_reader
            .fast_fields()
            .str("root_span_id")?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError("root_span_id field not found".to_string())
            })?;
        let span_id_reader = segment_reader
            .fast_fields()
            .str("span_id")?
            .ok_or_else(|| {
                tantivy::TantivyError::SchemaError("span_id field not found".to_string())
            })?;
        let score_reader = segment_reader.fast_fields().f64("score")?;
        let content_field = self.schema.get_field("content")?;

        Ok(ScoredDocumentSegmentCollector {
            store_reader: segment_reader.get_store_reader(1)?,
            root_span_id_reader,
            span_id_reader,
            score_reader: Some(score_reader),
            content_field,
            docs: Vec::new(),
            first_level_score: self.first_level_score,
            first_level_max: self.first_level_max,
        })
    }

    fn requires_scoring(&self) -> bool {
        false // Second level doesn't need scoring
    }

    fn merge_fruits(&self, fruits: Vec<Self::Fruit>) -> tantivy::Result<Self::Fruit> {
        Ok(fruits.into_iter().flatten().collect())
    }
}

struct ScoredDocumentSegmentCollector {
    store_reader: tantivy::store::StoreReader,
    root_span_id_reader: StrColumn,
    span_id_reader: StrColumn,
    score_reader: Option<tantivy::columnar::Column<f64>>,
    content_field: Field,
    docs: Vec<(String, String, String, f64, f32)>,
    first_level_score: f32,
    first_level_max: f32,
}

impl SegmentCollector for ScoredDocumentSegmentCollector {
    type Fruit = Vec<(String, String, String, f64, f32)>;

    fn collect(&mut self, doc: u32, _score: Score) {
        let mut root_span_id = String::new();
        if let Some(ord) = self.root_span_id_reader.term_ords(doc).next() {
            let mut bytes = Vec::new();
            if self
                .root_span_id_reader
                .ord_to_bytes(ord, &mut bytes)
                .unwrap()
            {
                root_span_id = String::from_utf8_lossy(&bytes).to_string();
            }
        }

        let mut span_id = String::new();
        if let Some(ord) = self.span_id_reader.term_ords(doc).next() {
            let mut bytes = Vec::new();
            if self.span_id_reader.ord_to_bytes(ord, &mut bytes).unwrap() {
                span_id = String::from_utf8_lossy(&bytes).to_string();
            }
        }

        let score_val = self
            .score_reader
            .as_ref()
            .and_then(|r| r.first(doc))
            .unwrap_or(0.0);

        let mut content = String::new();
        if let Ok(stored_doc) = self.store_reader.get::<tantivy::TantivyDocument>(doc) {
            for field_value in stored_doc.field_values() {
                if field_value.field() == self.content_field {
                    if let Some(text) = field_value.value().as_str() {
                        content = text.to_string();
                    }
                }
            }
        }

        // Combine scores: normalize first level score and use as weight
        let normalized_first_score = self.first_level_score / self.first_level_max;
        let combined_score = normalized_first_score * 0.7 + (score_val as f32) * 0.3; // 70% first level, 30% document score

        self.docs
            .push((root_span_id, span_id, content, score_val, combined_score));
    }

    fn harvest(self) -> Self::Fruit {
        self.docs
    }
}
