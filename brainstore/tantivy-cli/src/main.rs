use util::anyhow::Result;

use clap::{Parse<PERSON>, Subcommand};

pub mod benchmark;
pub mod create;
pub mod search;

#[cfg(test)]
pub mod json_agg_test;

#[cfg(test)]
pub mod two_level_search_test;

#[derive(<PERSON><PERSON><PERSON>, Debug, <PERSON>lone)]
pub struct Args {
    #[command(subcommand)]
    cmd: Commands,
}

#[derive(Subcommand, Debug, Clone)]
enum Commands {
    Create(create::CreateIndexArgs),
    Search(search::SearchArgs),
    Benchmark(benchmark::BenchmarkArgs),
}

pub fn main() -> Result<()> {
    let args = Args::parse();

    match args.cmd {
        Commands::Create(args) => create::create(args),
        Commands::Search(args) => search::search(args),
        Commands::Benchmark(args) => benchmark::benchmark(args),
    }
}
