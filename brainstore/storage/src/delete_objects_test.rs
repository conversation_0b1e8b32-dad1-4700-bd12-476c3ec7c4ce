use util::{schema::Schema, system_types::FullObjectId, uuid::Uuid};

use crate::{
    delete_objects::{delete_objects, DeleteObjectInput},
    index_document::make_full_schema,
    index_wal_reader::{IndexWalReader, IndexWalReaderInput},
    index_wal_reader_test_util::get_reader_full_docs,
    process_wal::{process_object_wal, ProcessObjectWalInput},
    test_util::TmpDirConfigWithStore,
    wal::WALScope,
    wal_entry::WalEntry,
};

fn make_schema() -> Schema {
    Schema::new("default".to_string(), Vec::new(), Default::default()).unwrap()
}

struct TestFixture {
    pub tmp_dir_config: TmpDirConfigWithStore,
}

impl TestFixture {
    fn new() -> Self {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        Self { tmp_dir_config }
    }

    pub async fn wal_token(&self) -> Uuid {
        self.tmp_dir_config
            .config
            .global_store
            .query_object_metadatas(&[FullObjectId::default()])
            .await
            .unwrap()
            .remove(0)
            .wal_token
    }

    async fn write_wal_entries(&self, entries: Vec<WalEntry>) {
        self.tmp_dir_config
            .config
            .wal
            .insert(
                WALScope::ObjectId(FullObjectId::default(), self.wal_token().await),
                entries,
            )
            .await
            .unwrap();
    }

    async fn run_process_wal(&self) {
        process_object_wal(
            ProcessObjectWalInput {
                object_id: FullObjectId::default(),
                config: &self.tmp_dir_config.config,
            },
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap();
    }

    async fn read_object_entries(&self) -> Vec<String> {
        let reader = IndexWalReader::new(
            IndexWalReaderInput {
                config_with_store: &self.tmp_dir_config.config,
                full_schema: make_full_schema(&make_schema()).unwrap(),
                object_ids: &[FullObjectId::default().to_owned()],
                filters: &[],
                sort: &None,
                partition_all: false,
            },
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap();
        let mut ids = get_reader_full_docs(&reader)
            .await
            .into_keys()
            .collect::<Vec<_>>();
        ids.sort();
        ids
    }

    async fn delete_object(&self) {
        delete_objects(DeleteObjectInput {
            object_ids: &[FullObjectId::default()],
            global_store: &*self.tmp_dir_config.config.global_store,
        })
        .await
        .unwrap();
    }
}

#[tokio::test]
async fn test_merge_tantivy_indices_unlocked() {
    let fixture = TestFixture::new();
    let wal_entries: Vec<WalEntry> = vec![
        WalEntry {
            id: "row0".to_string(),
            ..Default::default()
        },
        WalEntry {
            id: "row1".to_string(),
            ..Default::default()
        },
    ];

    // When only in the object WAL, we should be able to read the entries.
    fixture.write_wal_entries(wal_entries.clone()).await;
    assert_eq!(fixture.read_object_entries().await, vec!["row0", "row1"]);

    // After we run a delete, it should come up empty.
    fixture.delete_object().await;
    assert!(fixture.read_object_entries().await.is_empty());

    // Same thing when we write into the object WAL and process into segments.
    fixture.write_wal_entries(wal_entries.clone()).await;
    fixture.run_process_wal().await;
    assert_eq!(fixture.read_object_entries().await, vec!["row0", "row1"]);
    fixture.delete_object().await;
    assert!(fixture.read_object_entries().await.is_empty());
}
