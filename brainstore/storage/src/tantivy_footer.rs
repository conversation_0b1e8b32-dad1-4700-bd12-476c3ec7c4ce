use std::path::PathBuf;

use tracing::instrument;
use util::{anyhow::Result, await_spawn_blocking, uuid::Uuid};

use crate::{
    config_with_store::StoreInfo,
    directory::{tracing_directory::TracedRangeDirectory, AsyncDirectoryArc},
    tantivy_index::{IndexMetaJson, TantivyIndexScope},
    tantivy_index_wrapper::ReadonlyTantivyIndexWrapper,
};

#[derive(Debug, Clone)]
pub struct MakeFooterInput<'a> {
    pub index_store: &'a StoreInfo,
    pub schema: util::schema::Schema,
    pub segment_id: Uuid,
    pub index_meta: &'a IndexMetaJson,
}

#[instrument(err, skip(input), fields(segment_id = ?input.segment_id))]
pub async fn make_footer<'a>(input: MakeFooterInput<'a>) -> Result<()> {
    let traced_directory = TracedRangeDirectory::new(input.index_store.directory.clone());
    let tantivy_index_wrapper = ReadonlyTantivyIndexWrapper::new(
        AsyncDirectoryArc::new(traced_directory.clone()),
        input.schema,
        &input.index_store.prefix,
        [(input.segment_id, input.index_meta.clone())]
            .into_iter()
            .collect(),
    )
    .await?;
    {
        let tantivy_index_wrapper = &tantivy_index_wrapper;
        await_spawn_blocking!(
            move |tantivy_index_wrapper: &ReadonlyTantivyIndexWrapper| {
                // This should be fast, because the reader should already be cached
                // in the underlying directory.
                tracing::info_span!(
                    "Open reader",
                    num_chunks = tantivy_index_wrapper
                        .index
                        .searchable_segment_metas()?
                        .len() as u64
                )
                .in_scope(|| {
                    let builder = tantivy_index_wrapper.index.reader_builder();
                    builder.try_into()
                })?;
                let _metas = tantivy_index_wrapper.index.load_metas()?;
                Ok::<_, util::anyhow::Error>(())
            },
            tantivy_index_wrapper
        )???;
    };

    let footer_data = traced_directory.make_traced_redirects().await?;
    let filename = make_footer_fname(input.index_meta)?;
    input
        .index_store
        .directory
        .async_atomic_write(
            &TantivyIndexScope::Segment(input.segment_id)
                .path(&input.index_store.prefix)
                .join(filename),
            &footer_data,
        )
        .await?;
    Ok(())
}

pub fn make_footer_fname(index_meta: &IndexMetaJson) -> Result<PathBuf> {
    Ok(PathBuf::from(format!("{}.footer", index_meta.hash()?)))
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{
        basic_test_fixture::{BasicTestFixture, ValidateVacuumArgs},
        directory::cached_directory::FileCacheOpts,
        index_wal_reader::{IndexWalReader, IndexWalReaderInput},
        merge::{merge_tantivy_segments, MergeTantivySegmentsInput},
        process_wal::{compact_segment_wal, CompactSegmentWalOptions},
        tantivy_index::{make_prefix_directory, TantivyIndexScope, TantivyIndexWriterOpts},
        wal_entry::WalEntry,
    };
    use tantivy::directory::DirectoryClone;
    use tokio::runtime::Handle;
    use util::{
        spawn_blocking_util::spawn_blocking_with_async_timeout, system_types::FullObjectIdOwned,
        uuid::Uuid, xact::TransactionId,
    };

    #[tokio::test]
    async fn test_footer_lifecycle() {
        let fixture = BasicTestFixture::new_with_file_cache_opts(FileCacheOpts {
            stats_enabled: true,
            ..Default::default()
        });

        // Write a few WAL entries to a segment in different compaction steps.
        fixture
            .write_object_wal_entries(vec![WalEntry {
                id: "foo".to_string(),
                _xact_id: TransactionId(0),
                ..Default::default()
            }])
            .await;
        let segment_ids = fixture.run_process_wal().await.modified_segment_ids;
        assert_eq!(segment_ids.len(), 1);
        let segment_id = *segment_ids.iter().next().unwrap();

        let index_scope = TantivyIndexScope::Segment(segment_id);

        // Make sure there's no footer file
        let index_meta = fixture
            .tmp_dir_config
            .config
            .global_store
            .query_segment_metadatas(&[segment_id])
            .await
            .unwrap()
            .remove(0)
            .last_compacted_index_meta;
        assert!(index_meta.is_none());

        // Now run compaction
        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            CompactSegmentWalOptions::default(),
        )
        .await
        .unwrap();

        // Now there should be a footer file
        let footer_fname = get_footer_fname(&fixture, segment_id).await;
        let prefixed_directory = make_prefix_directory(
            fixture.tmp_dir_config.config.index.directory.clone(),
            &index_scope,
            &fixture.tmp_dir_config.config.index.prefix,
        );
        assert!(prefixed_directory
            .async_exists(&footer_fname)
            .await
            .unwrap());

        verify_cached_open_index(&fixture).await;

        // Compact a few more records, and then merge
        fixture
            .write_object_wal_entries(vec![WalEntry {
                id: "bar".to_string(),
                _xact_id: TransactionId(1),
                ..Default::default()
            }])
            .await;
        let segment_ids = fixture.run_process_wal().await.modified_segment_ids;
        assert_eq!(segment_ids.len(), 1);
        let new_segment_id = *segment_ids.iter().next().unwrap();
        assert_eq!(segment_id, new_segment_id);

        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            CompactSegmentWalOptions {
                writer_opts: TantivyIndexWriterOpts {
                    index_writer_force_no_merges: true,
                    ..Default::default()
                },
                ..Default::default()
            },
        )
        .await
        .unwrap();

        fixture.validate_vacuum(ValidateVacuumArgs::default()).await;

        let index_meta = fixture
            .tmp_dir_config
            .config
            .global_store
            .query_segment_metadatas(&[segment_id])
            .await
            .unwrap()
            .remove(0)
            .last_compacted_index_meta
            .unwrap();
        assert!(index_meta.tantivy_meta.segments.len() > 1);

        let footer_fname_2 = get_footer_fname(&fixture, segment_id).await;
        assert_ne!(footer_fname, footer_fname_2);

        verify_cached_open_index(&fixture).await;

        merge_tantivy_segments(
            MergeTantivySegmentsInput {
                segment_id,
                config: fixture.tmp_dir_config.config.clone(),
                schema: &fixture.make_default_full_schema(),
                dry_run: false,
                try_acquire: false,
            },
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap();

        let footer_fname_3 = get_footer_fname(&fixture, segment_id).await;
        assert_ne!(footer_fname_2, footer_fname_3);

        fixture.validate_vacuum(ValidateVacuumArgs::default()).await;

        verify_cached_open_index(&fixture).await;
    }

    async fn get_footer_fname(fixture: &BasicTestFixture, segment_id: Uuid) -> PathBuf {
        let index_meta = fixture
            .tmp_dir_config
            .config
            .global_store
            .query_segment_metadatas(&[segment_id])
            .await
            .unwrap()
            .remove(0)
            .last_compacted_index_meta
            .unwrap();
        let footer_fname = make_footer_fname(&index_meta.tantivy_meta).unwrap();

        let prefixed_directory = make_prefix_directory(
            fixture.tmp_dir_config.config.index.directory.clone(),
            &TantivyIndexScope::Segment(segment_id),
            &fixture.tmp_dir_config.config.index.prefix,
        );
        assert!(prefixed_directory
            .async_exists(&footer_fname)
            .await
            .unwrap());

        footer_fname
    }

    async fn verify_cached_open_index(fixture: &BasicTestFixture) {
        // Now create an index wal reader
        let index_wal_reader = IndexWalReader::new(
            IndexWalReaderInput {
                config_with_store: &fixture.tmp_dir_config.config,
                full_schema: fixture.make_default_full_schema(),
                object_ids: &[FullObjectIdOwned::default()],
                sort: &None,
                filters: &[],
                partition_all: false,
            },
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap();

        let storage_directory = fixture
            .tmp_dir_config
            .config
            .index
            .directory
            .as_dyn_instrumented();

        storage_directory.reset_caches();
        storage_directory.enable_timing();

        let directory = index_wal_reader.only_directory();
        let loaded = directory.load_segment_footer(None).await.unwrap();
        assert_eq!(loaded, 1);

        let (before_hits, before_misses) = get_hits_and_misses(&fixture);

        // Open the index, and make sure the reader is cached.
        let handle = Handle::current();
        let tantivy_index = tantivy::Index::open(directory.box_clone()).unwrap();
        spawn_blocking_with_async_timeout(
            &handle,
            move || {
                let reader_builder = tantivy_index.reader_builder();
                let reader = reader_builder.try_into()?;
                Ok::<_, util::anyhow::Error>(reader)
            },
            Default::default(),
            || "open index reader".into(),
        )
        .await
        .unwrap()
        .unwrap()
        .unwrap();

        // capture the hits and misses in the cached directory
        let (after_hits, after_misses) = get_hits_and_misses(&fixture);
        assert!(after_hits > before_hits);
        assert!(after_misses == before_misses);
    }

    fn get_hits_and_misses(fixture: &BasicTestFixture) -> (i64, i64) {
        let stats = fixture
            .tmp_dir_config
            .config
            .index
            .directory
            .as_dyn_instrumented()
            .cache_metrics();
        let hits = stats
            .iter()
            .map(|(_, stat)| stat.num_mem_hits + stat.num_disk_hits)
            .sum::<i64>();
        let misses = stats.iter().map(|(_, stat)| stat.num_misses).sum::<i64>();
        (hits, misses)
    }
}
