/**
 * This file implements the OpenTelemetry GenAI semantic conventions.
 * https://opentelemetry.io/docs/specs/semconv/gen-ai/gen-ai-spans/
 */

import { z } from "zod";
import { get } from "lodash";
import { isEmpty, isObject } from "@braintrust/core";
import {
  Message,
  chatCompletionContentPartSchema,
  chatCompletionMessageParamSchema,
  chatCompletionMessageToolCallSchema,
  toolsSchema,
} from "@braintrust/core/typespecs";
import {
  MessageField,
  SpanSpec,
  deserializeIfString,
  mustParseJson,
  translateModelParams,
  notHandled,
  handled,
} from "./attributes";
import { attributeSchema } from "../trace";
// TODO: Use proper pino logger when available in this context
// import { getLogger } from "../../instrumentation/logger";

export const genAISpanSpec: SpanSpec = {
  input: (attributes) => {
    const result = translateGenAIPrompt({
      value: get(attributes, "gen_ai.prompt"),
      valueJson: get(attributes, "gen_ai.prompt_json"),
      defaultRole: "user",
    });
    if (result === undefined) {
      return notHandled;
    }
    return handled(result);
  },
  output: (attributes) => {
    const result = translateGenAIPrompt({
      value: get(attributes, "gen_ai.completion"),
      valueJson: get(attributes, "gen_ai.completion_json"),
      defaultRole: "assistant",
    });
    if (result === undefined) {
      return notHandled;
    }
    return handled(result);
  },
  metadata: (attributes, events) => {
    const metadata = translateModelParams(
      deserializeIfString(get(attributes, "gen_ai.request")),
    );

    // Clean the model name by removing provider prefixes
    const rawModelName = get(attributes, "gen_ai.request.model");
    if (rawModelName && typeof rawModelName === "string") {
      metadata.model = cleanModelName(rawModelName);
    }

    // Parse and add tools metadata
    const tools = parseTools(attributes, events);
    if (tools.length > 0) {
      metadata.tools = tools;
    }

    if (isEmpty(metadata)) {
      return notHandled;
    }
    return handled(metadata);
  },
  span_attributes: (attributes) => {
    const operationName = get(attributes, "gen_ai.operation.name");
    if (operationName === "chat") {
      return handled({ type: "llm" });
    }
    if (operationName === "execute_tool") {
      return handled({ type: "tool" });
    }
    return notHandled;
  },
  metrics: (attributes) => {
    const metrics = deserializeIfString(get(attributes, "gen_ai.usage"));
    const result: Record<string, unknown> = {};

    if (isObject(metrics)) {
      Object.assign(result, metrics);
    }

    // Also check for individual usage attributes
    const promptTokens = get(attributes, "gen_ai.usage.prompt_tokens");
    const completionTokens = get(attributes, "gen_ai.usage.completion_tokens");
    const inputTokens = get(attributes, "gen_ai.usage.input_tokens");
    const outputTokens = get(attributes, "gen_ai.usage.output_tokens");
    const totalTokens = get(attributes, "gen_ai.usage.total_tokens");

    // Convert to numbers, but skip NaN values
    const tokenFields = [
      [promptTokens, "prompt_tokens"],
      [completionTokens, "completion_tokens"],
      [inputTokens, "input_tokens"],
      [outputTokens, "output_tokens"],
      [totalTokens, "total_tokens"],
    ] as const;

    for (const [value, key] of tokenFields) {
      if (value !== undefined) {
        const num = Number(value);
        if (!isNaN(num)) result[key] = num;
      }
    }

    // Normalize token names - prefer OpenAI spec names over GenAI spec names
    const normalizeFields = [
      ["prompt_tokens", "input_tokens"],
      ["completion_tokens", "output_tokens"],
      ["tokens", "total_tokens"],
    ] as const;

    for (const [preferred, alternative] of normalizeFields) {
      if (
        result[preferred] === undefined &&
        result[alternative] !== undefined
      ) {
        result[preferred] = result[alternative];
      }
    }

    // Remove duplicate fields if values are identical
    const duplicateFields = [
      ["prompt_tokens", "input_tokens"],
      ["completion_tokens", "output_tokens"],
      ["tokens", "total_tokens"],
    ] as const;

    for (const [keep, remove] of duplicateFields) {
      if (result[keep] === result[remove]) {
        delete result[remove];
      }
    }

    // Calculate total tokens if not present but input/output tokens are available
    if (!result.tokens && !result.total_tokens) {
      const promptTokens = result.prompt_tokens;
      const completionTokens = result.completion_tokens;
      if (promptTokens !== undefined && completionTokens !== undefined) {
        const promptNum = Number(promptTokens);
        const completionNum = Number(completionTokens);
        if (!isNaN(promptNum) && !isNaN(completionNum)) {
          result.tokens = promptNum + completionNum;
        }
      }
    }

    if (isEmpty(result)) {
      return notHandled;
    }
    return handled(result);
  },
};

type Tool = z.infer<typeof toolsSchema>[number];

function cleanModelName(modelName: string): string {
  // some clients (e.g. litellm) add a prefix to the model name which breaks our processing
  const prefixes = [
    "openai/",
    "anthropic/",
    "google/",
    "azure/",
    "bedrock/",
    "vertex_ai/",
  ];

  for (const prefix of prefixes) {
    if (modelName.startsWith(prefix)) {
      return modelName.slice(prefix.length);
    }
  }

  return modelName;
}

function parseTools(attributes: unknown, events?: unknown[]): Tool[] {
  const tools: Tool[] = [];

  // Use Zod to safely parse attributes as a record
  const attrsSchema = z.record(z.unknown());
  const parseResult = attrsSchema.safeParse(attributes);
  if (!parseResult.success) {
    return tools;
  }
  const attrs = parseResult.data;

  // Source 1: gen_ai.agent.tools attribute (agent spans)
  const agentTools = get(attrs, "gen_ai.agent.tools");
  if (agentTools && typeof agentTools === "string") {
    try {
      const toolNames = JSON.parse(agentTools);
      if (Array.isArray(toolNames)) {
        tools.push(
          ...toolNames.map((name: string) => ({
            type: "function" as const,
            function: {
              name: name,
              description: `Tool: ${name}`,
              parameters: {
                type: "object",
                properties: {},
                required: [],
              },
            },
          })),
        );
      }
    } catch {
      // Ignore parsing errors
    }
  }

  // Source 2: gen_ai.tool.name attribute (tool execution spans)
  const toolName = get(attrs, "gen_ai.tool.name");
  if (
    toolName &&
    typeof toolName === "string" &&
    !tools.some((t) => t.function.name === toolName)
  ) {
    tools.push({
      type: "function" as const,
      function: {
        name: toolName,
        description: `Tool: ${toolName}`,
        parameters: {
          type: "object",
          properties: {},
          required: [],
        },
      },
    });
  }

  // Source 3: gen_ai.choice events containing toolUse objects (chat spans)
  if (!events) {
    return tools;
  }

  const eventsArraySchema = z.array(
    z.object({
      name: z.string(),
      attributes: z.array(attributeSchema).optional(),
    }),
  );

  const eventsParseResult = eventsArraySchema.safeParse(events);
  if (!eventsParseResult.success) {
    return tools;
  }

  for (const event of eventsParseResult.data) {
    if (event.name === "gen_ai.choice" && event.attributes) {
      for (const attr of event.attributes) {
        if (attr.key === "message" && attr.value?.stringValue) {
          try {
            const message = JSON.parse(attr.value.stringValue);
            // Handle both array format [{"toolUse": {...}}] and direct object format {"toolUse": {...}}
            const items = Array.isArray(message) ? message : [message];
            for (const item of items) {
              if (item.toolUse && item.toolUse.name) {
                const toolName = item.toolUse.name;
                const toolInput = item.toolUse.input || {};

                if (!tools.some((t) => t.function.name === toolName)) {
                  // Try to infer parameter schema from actual usage
                  const properties: Record<string, unknown> = {};
                  const required: string[] = [];

                  for (const [key, value] of Object.entries(toolInput)) {
                    properties[key] = {
                      type:
                        typeof value === "string"
                          ? "string"
                          : typeof value === "number"
                            ? "number"
                            : typeof value === "boolean"
                              ? "boolean"
                              : "string",
                      description: `Parameter: ${key}`,
                    };
                    required.push(key);
                  }

                  tools.push({
                    type: "function" as const,
                    function: {
                      name: toolName,
                      description: `Tool: ${toolName}`,
                      parameters: {
                        type: "object",
                        properties,
                        required,
                      },
                    },
                  });
                }
              }
            }
          } catch {
            // Ignore parsing errors
          }
        }
      }
    }
  }

  return tools;
}

const genAIAssistantMessageParamSchema = z.object({
  role: z.literal("assistant"),
  content: z.string().nullish(),
  name: z
    .string()
    .nullish()
    .transform((x) => x ?? undefined),
  tool_calls: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        arguments: z.string(),
      }),
    )
    .nullish()
    .transform((value) =>
      value?.map(
        (tc): z.infer<typeof chatCompletionMessageToolCallSchema> => ({
          id: tc.id,
          type: "function",
          function: {
            arguments: tc.arguments,
            name: tc.name,
          },
        }),
      ),
    ),
});

const genAIMessageSchema = z.union([
  chatCompletionMessageParamSchema,
  genAIAssistantMessageParamSchema,
]);

function translateGenAIPrompt({
  value,
  valueJson,
  defaultRole,
}: {
  value: unknown;
  valueJson: unknown;
  defaultRole: "user" | "assistant";
}): MessageField | undefined {
  if (isEmpty(value) && isEmpty(valueJson)) {
    return undefined;
  }

  const prompt = valueJson ? mustParseJson(valueJson) : value;

  if (typeof prompt === "string") {
    return {
      isLLM: true,
      data: [
        {
          role: defaultRole,
          content: prompt,
        },
      ],
    };
  }

  try {
    const parsed = z.array(genAIMessageSchema).parse(prompt);

    const messages = parsed.map((message): Message => {
      const { role, content } = message;
      if (role !== "user" || typeof content !== "string") {
        return message;
      }

      try {
        const contentArray = z.array(z.unknown()).parse(JSON.parse(content));
        return {
          role: "user",
          content: contentArray.map((part) =>
            chatCompletionContentPartSchema.parse(part),
          ),
        };
      } catch {
        return {
          role: "user",
          content,
        };
      }
    });

    return {
      isLLM: true,
      data: messages,
    };
  } catch {
    return { isLLM: false, data: prompt };
  }
}
