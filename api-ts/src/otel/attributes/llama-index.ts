import { z } from "zod";
import { get } from "lodash";
import { ContentPart, Message } from "@braintrust/core/typespecs";
import { SpanSpec, notHandled, handled } from "./attributes";

const llamaIndexImagePartSchema = z.object({
  type: z.literal("image"),
  image: z.object({
    image: z.object({
      url: z.string(),
    }),
  }),
});

const llamaIndexTextPartSchema = z.object({
  type: z.literal("text"),
  text: z.string(),
});

const llamaIndexContentPartSchema = z.union([
  llamaIndexImagePartSchema,
  llamaIndexTextPartSchema,
]);

const llamaIndexMessageSchema = z
  .object({
    role: z.union([
      z.literal("system"),
      z.literal("user"),
      z.literal("assistant"),
    ]),
  })
  .and(
    z.union([
      z.object({ content: z.string() }),
      z.object({
        contents: z.array(
          z.object({ message_content: llamaIndexContentPartSchema }),
        ),
      }),
    ]),
  );

const llamaIndexMessagesSchema = z.array(
  z.object({
    message: llamaIndexMessageSchema,
  }),
);
type LlamaIndexMessages = z.infer<typeof llamaIndexMessagesSchema>;

const llamaIndexTokenCountsSchema = z.object({
  total: z.number().nullish(),
  prompt: z.number().nullish(),
  content: z.number().nullish(),
});

export const llamaIndexSpanSpec: SpanSpec = {
  input: (attributes) => {
    const fromValue = get(attributes, "llm.input_messages");
    if (fromValue) {
      const messages = llamaIndexMessagesSchema.parse(fromValue);
      return handled({
        isLLM: true,
        data: translateLlamaIndexMessages(messages),
      });
    }
    return notHandled;
  },
  output: (obj) => {
    const fromValue = get(obj, "llm.output_messages");
    if (fromValue) {
      const messages = llamaIndexMessagesSchema.parse(fromValue);
      return handled({
        isLLM: true,
        data: translateLlamaIndexMessages(messages),
      });
    }
    return notHandled;
  },
  metadata: (obj) => {
    const userMetadataString: unknown = get(obj, "metadata");
    const userMetadata =
      typeof userMetadataString === "string"
        ? JSON.parse(userMetadataString)
        : undefined;

    let modelParams: Record<string, unknown> | undefined = undefined;
    const fromValue: unknown = get(obj, "input.value");
    if (typeof fromValue === "string") {
      const val = JSON.parse(fromValue);
      const parsed = z
        .object({
          kwargs: z.record(z.unknown()),
        })
        .nullish()
        .safeParse(val);
      modelParams = parsed.success ? parsed.data?.kwargs : undefined;
    }

    const result = {
      ...userMetadata,
      ...modelParams,
    };

    // Return notHandled if we have no metadata to contribute
    if (Object.keys(result).length === 0) {
      return notHandled;
    }

    return handled(result);
  },
  metrics: (obj) => {
    const fromValue = get(obj, "llm.token_count");
    if (!fromValue) return notHandled;

    const tokenCounts = llamaIndexTokenCountsSchema.parse(fromValue);
    return handled({
      tokens: tokenCounts.total,
      prompt_tokens: tokenCounts.prompt,
      completion_tokens: tokenCounts.content,
    });
  },
};

function translateLlamaIndexMessages(messages: LlamaIndexMessages): Message[] {
  const ret: Message[] = [];

  for (const { message } of messages ?? []) {
    if ("content" in message) {
      ret.push(message);
      continue;
    } else {
      if (message.role !== "user") {
        throw new Error(
          `Unexpected 'contents' array for non-user role: ${message.role}`,
        );
      }
      const content = message.contents.map(
        ({ message_content: part }): ContentPart => {
          switch (part.type) {
            case "text": {
              return {
                type: "text",
                text: part.text,
              };
            }
            case "image": {
              return {
                type: "image_url",
                image_url: {
                  url: part.image.image.url,
                },
              };
            }
          }
        },
      );
      ret.push({ role: message.role, content });
    }
  }

  return ret;
}
