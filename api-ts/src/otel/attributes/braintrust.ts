import { z } from "zod";
import { get } from "lodash";
import { isEmpty, isObject } from "@braintrust/core";
import { chatCompletionMessageParamSchema } from "@braintrust/core/typespecs";
import {
  MessageField,
  SpanSpec,
  deserializeIfString,
  mustParseJson,
  notHandled,
  handled,
} from "./attributes";

export const braintrustSpanSpec: SpanSpec = {
  input: (attributes) => {
    const result = formatMessages({
      value: get(attributes, "braintrust.input"),
      valueJson: get(attributes, "braintrust.input_json"),
      defaultRole: "user",
    });
    if (result === undefined) {
      return notHandled;
    }
    return handled(result, ["braintrust.input", "braintrust.input_json"]);
  },
  output: (attributes) => {
    const result = formatMessages({
      value: get(attributes, "braintrust.output"),
      valueJson: get(attributes, "braintrust.output_json"),
      defaultRole: "assistant",
    });
    if (result === undefined) {
      return notHandled;
    }
    return handled(result, ["braintrust.output", "braintrust.output_json"]);
  },
  metadata: (attributes) => {
    const metadata = deserializeIfString(
      get(attributes, "braintrust.metadata"),
    );
    if (!isObject(metadata)) {
      return notHandled;
    }
    return handled(metadata, ["braintrust.metadata"]);
  },
  metrics: (attributes) => {
    const metrics = deserializeIfString(get(attributes, "braintrust.metrics"));
    if (!isObject(metrics)) {
      return notHandled;
    }
    return handled(metrics, ["braintrust.metrics"]);
  },
  span_attributes: (attributes) => {
    const spanAttributes = deserializeIfString(
      get(attributes, "braintrust.span_attributes"),
    );
    if (!isObject(spanAttributes)) {
      return notHandled;
    }
    return handled(spanAttributes, ["braintrust.span_attributes"]);
  },
  scores: (attributes) => {
    const rawScores = deserializeIfString(get(attributes, "braintrust.scores"));
    if (!isObject(rawScores)) {
      return notHandled;
    }
    return handled(rawScores, ["braintrust.scores"]);
  },
  expected: (attributes) => {
    const result = deserializeIfString(get(attributes, "braintrust.expected"));
    if (result === undefined || result === null) {
      return notHandled;
    }
    return handled(result, ["braintrust.expected"]);
  },
  tags: (attributes) => {
    const tags: unknown = get(attributes, "braintrust.tags");
    if (Array.isArray(tags)) {
      const filteredTags = tags.filter(
        (tag: unknown): tag is string => typeof tag === "string",
      );
      if (filteredTags.length > 0) {
        return handled(filteredTags, ["braintrust.tags"]);
      }
    }
    return notHandled;
  },
};

function formatMessages({
  value,
  valueJson,
  defaultRole,
}: {
  value: unknown;
  valueJson: unknown;
  defaultRole: "user" | "assistant";
}): MessageField | undefined {
  if (isEmpty(value) && isEmpty(valueJson)) {
    return undefined;
  }

  const prompt = valueJson ? mustParseJson(valueJson) : value;

  // NOTE[matt] I don't think it is wise to transform the data here, especially in a way that's specific
  // to the OpenAI chat completions endpoint. This could break if we need other data formats for other endpoints
  // or providers. I think it's much better to leave it as is and (a) put the burden on users to send correct data or
  // (b) let our backend UI deal with formatting for providers.
  if (typeof prompt === "string") {
    return {
      isLLM: true,
      data: [
        {
          role: defaultRole,
          content: prompt,
        },
      ],
    };
  }

  try {
    return {
      isLLM: true,
      data: z.array(chatCompletionMessageParamSchema).parse(prompt),
    };
  } catch {
    return {
      isLLM: false,
      data: prompt,
    };
  }
}
