// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`llama-index convertAttributesToSpan > basic 1`] = `
{
  "keysToDelete": Set {},
  "span": {
    "input": [
      {
        "content": "What does a llama look like?",
        "role": "user",
      },
    ],
    "metadata": {
      "model": "gpt-4o-mini",
      "temperature": 0.7,
      "test_name": "llama",
    },
    "metrics": {
      "prompt_tokens": 14,
      "tokens": 152,
    },
    "output": [
      {
        "content": "Llamas are large, domesticated South American animals that belong to the camelid family. They have elongated bodies, long necks, and a distinctive, wedge-shaped head.",
        "role": "assistant",
      },
    ],
    "span_attributes": {
      "type": "llm",
    },
  },
}
`;

exports[`llama-index convertAttributesToSpan > messages 1`] = `
{
  "keysToDelete": Set {},
  "span": {
    "input": [
      {
        "content": "You are a helpful assistant.",
        "role": "system",
      },
      {
        "content": [
          {
            "text": "Why are they orange?",
            "type": "text",
          },
        ],
        "role": "user",
      },
      {
        "content": "What do you mean?",
        "role": "assistant",
      },
      {
        "content": [
          {
            "text": "Why are llamas orange?",
            "type": "text",
          },
        ],
        "role": "user",
      },
    ],
    "metadata": {
      "model": "gpt-4o-mini",
      "temperature": 0.7,
      "test_name": "messages",
    },
    "metrics": {
      "prompt_tokens": 41,
      "tokens": 160,
    },
    "output": [
      {
        "content": "Llamas can have a variety of coat colors, including shades of brown, white, black, gray, and sometimes orange or reddish hues. The specific color of a llama's coat is determined by its genetics.",
        "role": "assistant",
      },
    ],
    "span_attributes": {
      "type": "llm",
    },
  },
}
`;

exports[`llama-index convertAttributesToSpan > multimodal 1`] = `
{
  "keysToDelete": Set {},
  "span": {
    "input": [
      {
        "content": "Speak like a pirate. Arrrr.",
        "role": "system",
      },
      {
        "content": [
          {
            "text": "Tell a brief story about the provided image(s).",
            "type": "text",
          },
          {
            "image_url": {
              "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAApgAAAKYB3X3/OAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAANCSURBVEiJtZZPbBtFFMZ/M7ubXdtdb1xSFyeilBapySVU8h8OoFaooFSqiihIVIpQBKci6KEg9Q6H9kovIHoCIVQJJCKE1ENFjnAgcaSGC6rEnxBwA04Tx43t2FnvDAfjkNibxgHxnWb2e/u992bee7tCa00YFsffekFY+nUzFtjW0LrvjRXrCDIAaPLlW0nHL0SsZtVoaF98mLrx3pdhOqLtYPHChahZcYYO7KvPFxvRl5XPp1sN3adWiD1ZAqD6XYK1b/dvE5IWryTt2udLFedwc1+9kLp+vbbpoDh+6TklxBeAi9TL0taeWpdmZzQDry0AcO+jQ12RyohqqoYoo8RDwJrU+qXkjWtfi8Xxt58BdQuwQs9qC/afLwCw8tnQbqYAPsgxE1S6F3EAIXux2oQFKm0ihMsOF71dHYx+f3NND68ghCu1YIoePPQN1pGRABkJ6Bus96CutRZMydTl+TvuiRW1m3n0eDl0vRPcEysqdXn+jsQPsrHMquGeXEaY4Yk4wxWcY5V/9scqOMOVUFthatyTy8QyqwZ+kDURKoMWxNKr2EeqVKcTNOajqKoBgOE28U4tdQl5p5bwCw7BWquaZSzAPlwjlithJtp3pTImSqQRrb2Z8PHGigD4RZuNX6JYj6wj7O4TFLbCO/Mn/m8R+h6rYSUb3ekokRY6f/YukArN979jcW+V/S8g0eT/N3VN3kTqWbQ428m9/8k0P/1aIhF36PccEl6EhOcAUCrXKZXXWS3XKd2vc/TRBG9O5ELC17MmWubD2nKhUKZa26Ba2+D3P+4/MNCFwg59oWVeYhkzgN/JDR8deKBoD7Y+ljEjGZ0sosXVTvbc6RHirr2reNy1OXd6pJsQ+gqjk8VWFYmHrwBzW/n+uMPFiRwHB2I7ih8ciHFxIkd/3Omk5tCDV1t+2nNu5sxxpDFNx+huNhVT3/zMDz8usXC3ddaHBj1GHj/As08fwTS7Kt1HBTmyN29vdwAw+/wbwLVOJ3uAD1wi/dUH7Qei66PfyuRj4Ik9is+hglfbkbfR3cnZm7chlUWLdwmprtCohX4HUtlOcQjLYCu+fzGJH2QRKvP3UNz8bWk1qMxjGTOMThZ3kvgLI5AzFfo379UAAAAASUVORK5CYII=",
            },
            "type": "image_url",
          },
          {
            "image_url": {
              "url": "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png",
            },
            "type": "image_url",
          },
        ],
        "role": "user",
      },
    ],
    "metadata": {
      "model": "gpt-4o-mini",
      "temperature": 0.7,
      "test_name": "multimodal",
    },
    "metrics": {
      "prompt_tokens": 17029,
      "tokens": 17254,
    },
    "output": [
      {
        "content": "Arrr, gather 'round, mateys, for a tale of a jolly banana named Benny!",
        "role": "assistant",
      },
    ],
    "span_attributes": {
      "type": "llm",
    },
  },
}
`;
