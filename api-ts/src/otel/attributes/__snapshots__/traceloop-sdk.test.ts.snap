// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`traceloop convertAttributesToSpan > basic 1`] = `
{
  "keysToDelete": Set {},
  "span": {
    "input": [
      {
        "content": "What is the population of Tokyo?",
        "role": "user",
      },
    ],
    "metadata": {
      "max_tokens": 20,
      "model": "gpt-4o-mini",
      "temperature": 0.9,
      "type": "chat",
    },
    "metrics": {
      "completion_tokens": 20,
      "prompt_tokens": 14,
      "tokens": 34,
    },
    "output": [
      {
        "content": "As of my last update in October 2023, the population of Tokyo was estimated to be around",
        "role": "assistant",
      },
    ],
    "span_attributes": {
      "type": "llm",
    },
  },
}
`;

exports[`traceloop convertAttributesToSpan > multimodal 1`] = `
{
  "keysToDelete": Set {},
  "span": {
    "input": [
      {
        "content": "Speak like a pirate. Arrrr.",
        "role": "system",
      },
      {
        "content": [
          {
            "text": "Tell a brief story about the provided image(s).",
            "type": "text",
          },
          {
            "image_url": {
              "url": "https://mystickermania.com/cdn/stickers/games/mario-banana-peel-512x512.png",
            },
            "type": "image_url",
          },
        ],
        "role": "user",
      },
    ],
    "metadata": {
      "model": "gpt-4o-mini",
      "type": "chat",
    },
    "metrics": {
      "completion_tokens": 251,
      "prompt_tokens": 8529,
      "tokens": 8780,
    },
    "output": [
      {
        "content": "Arrr, gather 'round, mateys, for a tale of a jolly little fruit!",
        "role": "assistant",
      },
    ],
    "span_attributes": {
      "type": "llm",
    },
  },
}
`;

exports[`traceloop convertAttributesToSpan > serialized 1`] = `
{
  "keysToDelete": Set {},
  "span": {
    "input": [
      {
        "content": "Hello",
        "role": "user",
      },
      {
        "content": "Hi there!",
        "role": "assistant",
      },
    ],
    "metadata": {
      "max_tokens": 20,
      "model": "gpt-4o-mini",
      "temperature": 0.9,
    },
    "metrics": {
      "completion_tokens": 20,
      "prompt_tokens": 10,
      "tokens": 30,
    },
    "output": [
      {
        "content": "Here's your response",
        "role": "assistant",
      },
    ],
    "span_attributes": {
      "type": "llm",
    },
  },
}
`;

exports[`traceloop convertAttributesToSpan > serializedEmpty 1`] = `
{
  "keysToDelete": Set {},
  "span": {
    "input": [],
    "metadata": {},
    "metrics": {},
    "output": [
      {
        "content": "",
        "role": "assistant",
      },
    ],
    "span_attributes": {
      "type": "llm",
    },
  },
}
`;

exports[`traceloop convertAttributesToSpan > serializedInvalid 1`] = `
{
  "keysToDelete": Set {},
  "span": {
    "input": undefined,
    "metadata": {},
    "metrics": {},
    "output": undefined,
    "span_attributes": {
      "type": "task",
    },
  },
}
`;

exports[`traceloop convertAttributesToSpan > stream 1`] = `
{
  "keysToDelete": Set {},
  "span": {
    "input": [
      {
        "content": "You are an assistant that can access weather information.",
        "role": "system",
      },
      {
        "content": "What is the weather in San Diego and what attractions should I visit?",
        "role": "user",
      },
    ],
    "metadata": {
      "model": "gpt-4o-mini",
      "tools": [
        {
          "function": {
            "description": "Get the weather for a location",
            "name": "weather",
            "parameters": {
              "properties": {
                "location": {
                  "description": "The location to get the weather for",
                  "type": "string",
                },
              },
              "type": "object",
            },
          },
          "type": "function",
        },
      ],
      "type": "chat",
    },
    "metrics": {
      "completion_tokens": 0,
      "prompt_tokens": 23,
      "tokens": 23,
    },
    "output": [
      {
        "role": "assistant",
        "tool_calls": [
          {
            "function": {
              "arguments": "{"location": "San Diego"}",
              "name": "weather",
            },
            "id": "call_iCyA3NrzZhAGAu47JMjZV9GY",
            "type": "function",
          },
          {
            "function": {
              "arguments": "{"location": "popular attractions in San Diego"}",
              "name": "weather",
            },
            "id": "call_G4GpNvN4F2dNRprSuqX0TPQp",
            "type": "function",
          },
        ],
      },
    ],
    "span_attributes": {
      "type": "llm",
    },
  },
}
`;

exports[`traceloop convertAttributesToSpan > tools 1`] = `
{
  "keysToDelete": Set {},
  "span": {
    "input": [
      {
        "content": "Calculate the sum of 123 and 456.",
        "role": "user",
      },
    ],
    "metadata": {
      "model": "gpt-4o-mini",
      "tools": [
        {
          "function": {
            "description": "Add two numbers",
            "name": "add_numbers",
            "parameters": {
              "properties": {
                "a": {
                  "description": "First number",
                  "type": "number",
                },
                "b": {
                  "description": "Second number",
                  "type": "number",
                },
              },
              "required": [
                "a",
                "b",
              ],
              "type": "object",
            },
          },
          "type": "function",
        },
      ],
      "type": "chat",
    },
    "metrics": {
      "completion_tokens": 18,
      "prompt_tokens": 62,
      "tokens": 80,
    },
    "output": [
      {
        "role": "assistant",
        "tool_calls": [
          {
            "function": {
              "arguments": "{"a":123,"b":456}",
              "name": "add_numbers",
            },
            "id": "call_EXzHE31NyQa3y3pItz3v4Ztf",
            "type": "function",
          },
        ],
      },
    ],
    "span_attributes": {
      "type": "llm",
    },
  },
}
`;
