import {
  ExperimentEvent,
  LoggingEvent,
  IS_MERGE_FIELD,
  isEmpty,
  PlaygroundLogEvent,
} from "@braintrust/core";
import {
  AnyValue,
  OtelSpan,
  exportTraceServiceRequestSchema,
  otelSpanSchema,
  arrayValueSchema,
  kvlistValueSchema,
} from "./trace";
import { convertAttributesToSpan } from "./attributes";
import { OtelBadRequestError } from "./util";

const BRAINTRUST_PARENT_OTEL_ATTR = "braintrust.parent";

// Simple interface for OTEL trace service response
interface IExportTraceServiceResponse {
  partialSuccess?: {
    rejectedSpans?: number;
    errorMessage?: string;
  };
}

export function makeExportTraceServiceResponse(
  rejectedSpans: RejectedSpan[],
): IExportTraceServiceResponse {
  const response: IExportTraceServiceResponse = {};

  if (rejectedSpans.length) {
    response.partialSuccess = {
      rejectedSpans: rejectedSpans.length,
      errorMessage: JSON.stringify(rejectedSpans),
    };
  }

  return response;
}

// open telemetry's error status code
const STATUS_OK = 1;
const STATUS_ERROR = 2;

export type RejectedSpan = {
  traceId: string;
  spanId: string;
  error: string;
};

export type RowID = "log_id" | "experiment_id" | "project_id";
export type Row = ExperimentEvent | LoggingEvent | PlaygroundLogEvent;

// an unresolved row is one where we haven't figured out it's parent id yet.
export type UnresolvedRow = Omit<Row, RowID>;

// otelTraceToRows takes an otel trace (e.g. a collection of raw otel spans) and
// converts them into rows our backend can understand. If a span isn't tagged with
// a braintrust parent object (e.g. a project, experiment, or playground) and a default
// parent is provided, it will be assigned to that span.
export function otelTraceToRows(
  trace: unknown,
  defaultParent?: string,
): {
  rowsByParent: Map<string, UnresolvedRow[]>;
  rejectedSpans: RejectedSpan[];
} {
  const result = exportTraceServiceRequestSchema.safeParse(trace);
  if (!result.success) {
    throw new OtelBadRequestError(
      `Invalid traces data: ${JSON.stringify(result.error.errors)}`,
    );
  }
  const parsedTrace = result.data;

  const rows: UnresolvedRow[] = [];
  const rejectedSpans: RejectedSpan[] = [];
  for (const resourceSpan of parsedTrace.resourceSpans ?? []) {
    for (const scopeSpan of resourceSpan?.scopeSpans ?? []) {
      for (const span of scopeSpan.spans ?? []) {
        try {
          const row = otelSpanToRow(span);
          if (row) {
            rows.push(row);
          }
        } catch (e) {
          rejectedSpans.push({
            traceId: String(span.traceId || "unknown"),
            spanId: String(span.spanId || "unknown"),
            error: String(e),
          });
        }
      }
    }
  }

  const rowsByParent: Map<string, UnresolvedRow[]> = new Map();
  for (const row of rows) {
    const meta = row.metadata || {};
    let parent = meta[BRAINTRUST_PARENT_OTEL_ATTR];
    // If no span-level parent is set, use the default parent
    if (!parent || typeof parent !== "string") {
      if (defaultParent) {
        parent = defaultParent;
      } else {
        // No parent and no default - reject this span
        rejectedSpans.push({
          traceId: row.root_span_id || "unknown",
          spanId: row.span_id || "unknown",
          error: "No parent specified and no default parent provided",
        });
        continue;
      }
    }

    if (typeof parent !== "string") {
      continue;
    }
    if (!rowsByParent.get(parent)) {
      rowsByParent.set(parent, []);
    }
    rowsByParent.get(parent)?.push(row);
  }

  return {
    rowsByParent,
    rejectedSpans,
  };
}

function byteArrayToHex(value: Uint8Array): string {
  return Buffer.from(value).toString("hex");
}

function unixNanoToSeconds(nano: unknown): number | undefined {
  if (typeof nano !== "number") {
    return undefined;
  }
  return nano / 1_000_000_000;
}

export function otelSpanToRow(span: unknown): UnresolvedRow | undefined {
  const otelSpan = otelSpanSchema.parse(span);

  const rootSpanId = byteArrayToHex(otelSpan.traceId);
  const spanId = byteArrayToHex(otelSpan.spanId);
  const parentSpanId = otelSpan.parentSpanId
    ? byteArrayToHex(otelSpan.parentSpanId)
    : undefined;

  const name = otelSpan.name ?? (parentSpanId ? "subspan" : "root");

  const attrs: Record<string, unknown> = {};
  for (const attr of otelSpan.attributes ?? []) {
    attrs[attr.key] = parseAttributeValue(attr.value);
  }

  // Filter genAI events for processing
  const genAIEvents = (otelSpan.events ?? []).filter((event) =>
    event.name?.startsWith("gen_ai."),
  );

  const { span: partialSpan, keysToDelete } = convertAttributesToSpan(
    attrs,
    genAIEvents,
  );

  const start = unixNanoToSeconds(otelSpan.startTimeUnixNano);
  const end = unixNanoToSeconds(otelSpan.endTimeUnixNano);

  const error = parseError(otelSpan);
  const genAIEventData = parseGenAIEvents(otelSpan);

  return {
    id: spanId,
    created: (start ? new Date(start * 1000) : new Date()).toISOString(),
    span_id: spanId,
    root_span_id: rootSpanId,
    span_parents: parentSpanId ? [parentSpanId] : [],
    [IS_MERGE_FIELD]: false,
    ...partialSpan,
    // Override input/output with event data if available
    ...(genAIEventData.input ? { input: genAIEventData.input } : {}),
    ...(genAIEventData.output ? { output: genAIEventData.output } : {}),
    metadata: {
      // Filter out attributes that were processed and marked for deletion
      ...Object.fromEntries(
        Object.entries(attrs).filter(([key]) => !keysToDelete.has(key)),
      ),
      ...partialSpan.metadata,
    },
    metrics: {
      start,
      end,
      ...partialSpan.metrics,
    },
    span_attributes: {
      name,
      ...partialSpan.span_attributes,
    },
    error: error,
  };
}

function parseError(otelSpan: OtelSpan): string | string[] | null {
  // Convert the error information on an otel span into a format our backend back understand.

  // a single otel span can have multiple exception events.
  const errEvents = (otelSpan.events ?? []).filter(
    (event) => event.name === "exception",
  );

  // A span can still be marked STATUS_ERROR even if it has no exceptions, so handle that here.
  if (errEvents.length == 0 && otelSpan.status?.code == STATUS_OK) {
    return null;
  } else if (errEvents.length == 0 && otelSpan.status?.code == STATUS_ERROR) {
    return otelSpan.status.message ?? "error";
  }

  // OTel records exceptions in a few fields: stacktrace, type, message. We have only one field called `error`
  // We use the stacktrace, since it should contain all the data. NOTE[matt] here we're dropping a bit of data
  // (e.g. the exact time the exception was thrown). Is this important?
  const errorMsgs: string[] = [];
  for (const event of errEvents) {
    // get the error attributes from each event.
    const attrsByKey: Record<string, string> = {};
    for (const attr of event.attributes ?? []) {
      const key = attr.key ?? "";
      const val = attr.value?.stringValue ?? "";
      if (val && key) {
        attrsByKey[key] = val;
      }
    }

    // Co-erce the three fields into the best error message we can get.
    let curErrMsg = "";
    if (attrsByKey["exception.stacktrace"]) {
      curErrMsg = attrsByKey["exception.stacktrace"];
    } else if (
      attrsByKey["exception.type"] &&
      attrsByKey["exception.message"]
    ) {
      curErrMsg = `${attrsByKey["exception.type"]}: ${attrsByKey["exception.message"]}`;
    } else if (attrsByKey["exception.type"]) {
      curErrMsg = attrsByKey["exception.type"];
    } else if (attrsByKey["exception.message"]) {
      curErrMsg = attrsByKey["exception.message"];
    }

    if (curErrMsg !== "") {
      errorMsgs.push(curErrMsg);
    }
  }

  // As of 2025-04-01, the UI looks a bit nicer if you just return a single error, so if we only have one
  // error, return that. Otherwise, return a list.
  switch (errorMsgs.length) {
    case 0:
      return null;
    case 1:
      return errorMsgs[0];
    default:
      return errorMsgs;
  }
}

function parseGenAIEvents(otelSpan: OtelSpan): {
  input?: unknown;
  output?: unknown;
} {
  // Convert GenAI events on an otel span into input/output messages for Braintrust format.

  const genAIEvents = (otelSpan.events ?? []).filter((event) =>
    event.name?.startsWith("gen_ai."),
  );

  if (genAIEvents.length === 0) {
    return {};
  }

  const input: unknown[] = [];
  const output: unknown[] = [];

  for (const event of genAIEvents) {
    // get the event attributes
    const attrsByKey: Record<string, string> = {};
    for (const attr of event.attributes ?? []) {
      const key = attr.key ?? "";
      const val = attr.value?.stringValue ?? "";
      if (val && key) {
        attrsByKey[key] = val;
      }
    }

    switch (event.name) {
      case "gen_ai.user.message": {
        const content = attrsByKey["content"];
        if (content) {
          try {
            // Content is JSON array format: [{"text": "message"}]
            const parsed = JSON.parse(content);
            if (Array.isArray(parsed) && parsed[0]?.text) {
              input.push({
                role: "user",
                content: parsed[0].text,
              });
            }
          } catch {
            // Fallback to raw content
            input.push({
              role: "user",
              content: content,
            });
          }
        }
        break;
      }

      case "gen_ai.choice": {
        const message = attrsByKey["message"];
        if (message) {
          try {
            // Try parsing as JSON array first: [{"text": "response"}]
            const parsed = JSON.parse(message);
            if (Array.isArray(parsed) && parsed[0]?.text) {
              output.push({
                role: "assistant",
                content: parsed[0].text,
              });
            } else {
              // Fallback to raw message
              output.push({
                role: "assistant",
                content: message.trim(),
              });
            }
          } catch {
            // Fallback to raw message
            output.push({
              role: "assistant",
              content: message.trim(),
            });
          }
        }
        break;
      }

      case "gen_ai.tool.message": {
        const content = attrsByKey["content"];
        const id = attrsByKey["id"];
        if (content) {
          try {
            const parsed = JSON.parse(content);
            input.push({
              role: "tool",
              content: parsed,
              ...(id && { id }),
            });
          } catch {
            input.push({
              role: "tool",
              content: content,
              ...(id && { id }),
            });
          }
        }
        break;
      }

      case "gen_ai.assistant.message": {
        const content = attrsByKey["content"];
        if (content) {
          output.push({
            role: "assistant",
            content: content,
          });
        }
        break;
      }
    }
  }

  const result: { input?: unknown; output?: unknown } = {};
  if (input.length > 0) {
    result.input = input;
  }
  if (output.length > 0) {
    result.output = output;
  }

  return result;
}

function parseAttributeValue(value?: AnyValue): unknown {
  if (value === undefined) {
    return undefined;
  }

  const entries = Object.entries(value).filter(
    ([_, val]) => !isEmpty(val) && !Number.isNaN(val),
  );
  if (!entries.length) {
    // The original value was undefined or empty.
    return undefined;
  }

  const [key, val] = entries[0];
  switch (key) {
    case "bytesValue":
    case "intValue":
    case "stringValue":
    case "boolValue":
    case "doubleValue":
      return val;
    case "arrayValue":
      const array = arrayValueSchema.parse(val);
      return array.values?.map((item) => parseAttributeValue(item)) ?? [];
    case "kvlistValue":
      const kvlist = kvlistValueSchema.parse(val);
      return (
        kvlist.values?.reduce((acc: Record<string, unknown>, entry) => {
          acc[entry.key] = parseAttributeValue(entry.value);
          return acc;
        }, {}) ?? {}
      );
    default:
      throw new Error(`Unknown AnyValue key: ${key}`);
  }
}
