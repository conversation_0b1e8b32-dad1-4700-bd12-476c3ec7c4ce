import { test, expect } from "vitest";
import { z } from "zod";

import {
  removeReservedFields,
  validateNoReservedMetadataFields,
} from "./metadata";

const reservedSchemaConfig = {
  "~__bt_assignments": {
    schema: z.array(z.string()).nullish(),
    hidden: true,
  },
  model: {
    schema: z.string().nullish(),
  },
};
test("removeReservedFields removes hidden reserved fields", () => {
  const input = {
    "~__bt_assignments": ["assignment1", "assignment2"],
    regularField: "value",
    anotherField: 42,
  };

  const result = removeReservedFields(input, reservedSchemaConfig);

  expect(result).toEqual({
    regularField: "value",
    anotherField: 42,
  });
  expect(result).not.toHaveProperty("~__bt_assignments");
});

test("removeReservedFields keeps non-hidden reserved fields", () => {
  // Mock a scenario where we have a non-hidden reserved field
  const input = {
    "~__bt_assignments": ["assignment1", "assignment2"],
    model: "gpt-4o",
    regularField: "value",
  };

  // If the field is not in RESERVED_METADATA_FIELDS or not hidden, it should remain
  const result = removeReservedFields(input, reservedSchemaConfig);

  expect(result).toEqual({
    model: "gpt-4o",
    regularField: "value",
  });
});

test("removeReservedFields handles empty object", () => {
  const input = {};
  const result = removeReservedFields(input, reservedSchemaConfig);
  expect(result).toEqual({});
});

test("removeReservedFields handles object with only reserved fields", () => {
  const input = {
    "~__bt_assignments": ["assignment1"],
  };

  const result = removeReservedFields(input, reservedSchemaConfig);
  expect(result).toEqual({});
});

test("removeReservedFields handles object with no reserved fields", () => {
  const input = {
    field1: "value1",
    field2: 123,
    field3: { nested: "object" },
  };

  const result = removeReservedFields(input, reservedSchemaConfig);
  expect(result).toEqual(input);
});

test("removeReservedFields returns original object on parse error", () => {
  // The function actually filters out reserved fields even with invalid values
  // because the transform function runs before validation
  const input = {
    "~__bt_assignments": "invalid_value_should_be_array",
    regularField: "value",
  };

  const result = removeReservedFields(input, reservedSchemaConfig);

  // The reserved field gets filtered out regardless of validity
  expect(result).toEqual({
    regularField: "value",
  });
});

test("validateNoReservedMetadataFields allows object without reserved fields", () => {
  const input = {
    field1: "value1",
    field2: 123,
    field3: { nested: "object" },
  };

  expect(() => validateNoReservedMetadataFields(input)).not.toThrow();
});

test("validateNoReservedMetadataFields throws error for object with reserved fields", () => {
  const input = {
    "~__bt_assignments": ["assignment1"],
    regularField: "value",
  };

  expect(() => validateNoReservedMetadataFields(input)).toThrow();
});

test("validateNoReservedMetadataFields provides correct error message", () => {
  const input = {
    "~__bt_assignments": ["assignment1"],
    regularField: "value",
  };

  try {
    validateNoReservedMetadataFields(input);
    expect.fail("Should have thrown an error");
  } catch (error) {
    expect(error).toBeInstanceOf(z.ZodError);
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    const zodError = error as z.ZodError;
    expect(zodError.issues[0].message).toBe(
      "Cannot update reserved metadata fields",
    );
    expect(zodError.issues[0].path).toEqual(["metadata", "~__bt_assignments"]);
  }
});

test("validateNoReservedMetadataFields handles multiple reserved fields", () => {
  // Test with just the known reserved field
  const input = {
    "~__bt_assignments": ["assignment1"],
    model: "gpt-4o",
  };

  try {
    validateNoReservedMetadataFields(input);
    expect.fail("Should have thrown an error");
  } catch (error) {
    expect(error).toBeInstanceOf(z.ZodError);
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    const zodError = error as z.ZodError;
    expect(zodError.issues.length).toBe(1);
    expect(zodError.issues[0].message).toBe(
      "Cannot update reserved metadata fields",
    );
  }
});

test("validateNoReservedMetadataFields handles non-object inputs", () => {
  // Should not throw for non-object inputs
  expect(() => validateNoReservedMetadataFields(null)).not.toThrow();
  expect(() => validateNoReservedMetadataFields(undefined)).not.toThrow();
  expect(() => validateNoReservedMetadataFields("string")).not.toThrow();
  expect(() => validateNoReservedMetadataFields(123)).not.toThrow();
  expect(() => validateNoReservedMetadataFields([])).not.toThrow();
});

test("validateNoReservedMetadataFields handles empty object", () => {
  expect(() => validateNoReservedMetadataFields({})).not.toThrow();
});
