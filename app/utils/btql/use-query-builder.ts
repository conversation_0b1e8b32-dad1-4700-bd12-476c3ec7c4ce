import {
  type ParsedQuery,
  type Ident,
  type Expr,
  type Shape,
  type AliasExpr,
} from "@braintrust/btql/parser";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useMemo } from "react";
import { alias, binOp, from, ident } from "./query-builder";

export interface BtqlQueryBuilder {
  and: (...clauses: (Expr | null | undefined)[]) => Expr;
  or: (...clauses: (Expr | null | undefined)[]) => Expr;
  from: (
    objectType: string,
    objectIds: string[],
    shape?: Shape,
  ) => ParsedQuery["from"];
  ident: (...name: string[]) => Ident;
  alias: (alias: string, expr: string | Expr) => AliasExpr;
}
export function useBtqlQueryBuilder({}: {}): BtqlQueryBuilder {
  const {
    flags: { flattenedBoolOps },
  } = useFeatureFlags();

  const builder = useMemo(
    () => ({
      and: (...clauses: (Expr | null | undefined)[]) =>
        binOp("and", flattenedBoolOps, ...clauses),
      or: (...clauses: (Expr | null | undefined)[]) => {
        return binOp("or", flattenedBoolOps, ...clauses);
      },
      from,
      ident,
      alias,
    }),
    [flattenedBoolOps],
  );

  return builder;
}
