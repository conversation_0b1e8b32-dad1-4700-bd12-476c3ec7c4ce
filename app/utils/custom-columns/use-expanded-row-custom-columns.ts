import { useDuckDB, dbQuery } from "#/utils/duckdb";
import { skipToken, useQuery } from "@tanstack/react-query";
import { z } from "zod";
import {
  type CustomColumnDefinition,
  type CustomColumn,
} from "./use-custom-columns";
import {
  dataObjectPageShape,
  fetchBtql,
  rowWithIdsSchema,
} from "#/utils/btql/btql";
import * as Query from "#/utils/btql/query-builder";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import { useBtqlFlags } from "#/lib/feature-flags";
import { useMemo } from "react";
import { withErrorTiming } from "#/utils/btapi/type-error";

const customColumnsSchema = z.record(z.string(), z.unknown());

export type CustomColumnsRowParams = {
  scan?: string | null;
  scanQueryKeys?: string[];
  customColumns?: CustomColumn[] | CustomColumnDefinition[];
};

export function useExpandedRowCustomColumns({
  rowId,
  params,
  whereClause,
  objectType,
  objectId,
  isFastSummaryEnabled,
}: {
  rowId: string | null;
  params?: CustomColumnsRowParams;
  whereClause: string | null;
  objectType: DataObjectType;
  objectId: string | null;
  isFastSummaryEnabled?: boolean;
}) {
  const btqlFlags = useBtqlFlags();

  const duck = useDuckDB();
  const tableScan = params?.scan;
  const customColumns = params?.customColumns;
  const isQueryEnabled =
    !!duck &&
    !!params &&
    !!tableScan &&
    !!customColumns &&
    !!customColumns.length &&
    !!whereClause &&
    !isFastSummaryEnabled;
  const { data } = useQuery({
    queryKey: [
      params?.scanQueryKeys,
      "customColumnsRowQuery",
      params?.scan,
      params?.customColumns,
      whereClause,
    ],
    queryFn: isQueryEnabled
      ? async ({ signal }) => {
          const conn = await duck.connect();
          const customColsQuery = `SELECT span_id, ${customColumns
            .map(({ name }) => name)
            .join(", ")} FROM (${tableScan.replaceAll(
            "/*RE_PUSHDOWN_FILTER_SUB*/",
            whereClause,
          )})`;
          const customCols = await dbQuery(conn, signal, customColsQuery);
          const customColumnsArray = customCols?.toArray().map((row) => {
            const spanId = z.string().parse(row.span_id);
            const data = customColumnsSchema.parse(row.toJSON());
            return [spanId, data];
          });
          return (
            (customColumnsArray &&
              z
                .record(z.string(), customColumnsSchema)
                .parse(Object.fromEntries(customColumnsArray))) ??
            null
          );
        }
      : skipToken,
    placeholderData: undefined,
    enabled: isQueryEnabled,
  });

  const org = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  const shape = useMemo(() => dataObjectPageShape(objectType), [objectType]);

  const { data: summaryData } = useQuery({
    queryKey: [
      params?.scanQueryKeys,
      "customColumnsRowSummaryQuery",
      rowId,
      objectType,
      objectId,
      shape !== "summary" ? customColumns : undefined,
    ],
    queryFn: withErrorTiming(
      async ({ signal }: { signal: AbortSignal }) =>
        objectId
          ? await fetchBtql({
              args: {
                query: {
                  filter: { btql: `id = "${rowId}"` },
                  from: Query.from(objectType, [objectId], shape),
                  select: [{ op: "star" }],
                  ...(shape === "summary" ? { preview_length: -1 } : {}),
                  ...(shape !== "summary"
                    ? {
                        custom_columns: customColumns?.map((c) => ({
                          expr: { btql: c.expr },
                          alias: c.name,
                        })),
                      }
                    : {}),
                },
                brainstoreRealtime: true,
              },
              btqlFlags,
              apiUrl: org.api_url,
              getOrRefreshToken,
              schema: rowWithIdsSchema,
              signal,
            })
          : undefined,
      "Custom columns query",
      {
        objectType,
        ...(objectId ? { objectId } : {}),
        shape,
      },
    ),

    placeholderData: undefined,
    enabled:
      !!objectId &&
      !!isFastSummaryEnabled &&
      (shape === "summary" || !!customColumns?.length),
  });

  const customColumnsData = useMemo(() => {
    if (data) {
      return data;
    }
    if (summaryData?.data[0]) {
      if (!customColumns) {
        return null;
      }
      const row = summaryData.data[0];
      const filteredRow = customColumns.reduce<Record<string, unknown>>(
        (acc, col) => {
          if (row[col.name] !== undefined) {
            acc[col.name] = row[col.name];
          }
          return acc;
        },
        {},
      );
      return Object.fromEntries([[row.span_id, filteredRow]]);
    }
    return null;
  }, [data, summaryData?.data, customColumns]);

  return {
    data: customColumnsData,
  };
}
