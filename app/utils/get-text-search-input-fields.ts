import { type DataObjectType } from "./btapi/btapi";

/**
 * What fields to apply an input text search on.
 * We then do an OR query of all of these together.
 */
export const getTextSearchInputFields = (objectType: DataObjectType) => {
  const base = ["input", "expected", "metadata", "tags"];

  // A little hacky, but we don't want to include output or span_attributes in the
  // full text search for datasets.
  if (objectType !== "dataset") {
    return [...base, "output", "span_attributes"];
  }

  return base;
};
