import { toast } from "sonner";
import * as <PERSON><PERSON> from "@sentry/nextjs";

export function toastAndLogError(
  action: string,
  error: unknown,
  skipLog?: boolean,
) {
  toast.error(`Failed to ${action} view`, { description: `${error}` });
  if (!skipLog) {
    console.warn(`Failed to ${action} view`, error);
  }
  Sentry.captureException(error, {
    tags: {
      feature: "views",
      action,
    },
  });
}
