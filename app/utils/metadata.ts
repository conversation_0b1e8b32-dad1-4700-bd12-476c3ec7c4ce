import { v4 as uuidv4 } from "uuid";
import z from "zod";
import { BT_ASSIGNMENTS_META_FIELD } from "./assign";

export function newObjectName(objectType: string) {
  return `${objectType}-${uuidv4().slice(0, 8)}`;
}

export function objectCopyName(objectName: string) {
  return `Copy of ${objectName}`;
}

type MetadataSchemaConfig = Record<
  string,
  {
    schema: z.ZodType<unknown>;
    hidden?: boolean;
  }
>;

const reservedMetadataSchemaConfig: MetadataSchemaConfig = {
  [BT_ASSIGNMENTS_META_FIELD]: {
    schema: z.array(z.string()).nullish(),
    hidden: true,
  },
};
const RESERVED_METADATA_FIELDS = Object.keys(reservedMetadataSchemaConfig);

export const isReservedMetadataPath = (path: string[]) =>
  path[0] === "metadata" &&
  !!path[1] &&
  RESERVED_METADATA_FIELDS.includes(path[1]);

export const reservedMetadataSchema = z.object(
  Object.fromEntries(
    Object.entries(reservedMetadataSchemaConfig).map(([key, { schema }]) => [
      key,
      schema,
    ]),
  ),
);

export function removeReservedFields(
  value: Record<string, unknown>,
  schemaConfig: MetadataSchemaConfig,
) {
  const schema = z.record(z.string(), z.unknown()).transform((obj) => {
    const result: Record<string, unknown> = {};

    for (const key in obj) {
      if (
        !Object.keys(schemaConfig).includes(key) ||
        !schemaConfig[key].hidden
      ) {
        result[key] = obj[key];
      }
    }

    return result;
  });
  try {
    return schema.parse(value);
  } catch {
    return value;
  }
}

export function removeReservedMetadataFields(value: Record<string, unknown>) {
  return removeReservedFields(value, reservedMetadataSchemaConfig);
}

export function validateNoReservedMetadataFields(value: unknown) {
  if (!value || typeof value !== "object" || Array.isArray(value)) {
    return;
  }

  const base = z.record(z.string(), z.unknown()).superRefine((obj, ctx) => {
    for (const key of RESERVED_METADATA_FIELDS) {
      if (key in obj) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Cannot update reserved metadata fields",
          path: ["metadata", key],
        });
      }
    }
  });
  return base.parse(value);
}
