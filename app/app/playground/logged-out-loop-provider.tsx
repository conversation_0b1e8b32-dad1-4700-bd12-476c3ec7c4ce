"use client";

import { useMemo, useState } from "react";
import { OptimizationContext } from "#/utils/optimization/provider";
import { ToolManager } from "@braintrust/local/optimization/tools";
import {
  GlobalChatContext,
  type GlobalChatContextType,
  type ChatSession,
} from "#/ui/optimization/use-global-chat-context";
import { ALL_TOOL_NAMES } from "@braintrust/local/optimization/tools";

const OPTIMIZATION_CONTEXT_VALUE = {
  tools: new ToolManager(false),
  makeChatContext: () => {},
  flush: async () => {},
  allowRunningWithoutConsent: false,
  setAllowRunningWithoutConsent: () => {},
};

const DEFAULT_SESSION: ChatSession = {
  id: "default-session",
  name: "New Session",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  messages: [],
  contextObjects: {},
  tools: Array.from(ALL_TOOL_NAMES),
  mode: "agent",
  userMessage: "",
  isActive: false,
};

export function LoggedOutLoopProvider({
  children,
  openUpsellDialog,
}: {
  children: React.ReactNode;
  openUpsellDialog: VoidFunction;
}) {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isDocked, setIsDocked] = useState(false);
  const [model, setModel] = useState("claude-3-5-sonnet-latest");
  const [currentSessionUserMessage, setCurrentSessionUserMessage] =
    useState("");
  const [currentSessionIsActive, setCurrentSessionIsActive] = useState(false);

  const globalChatContextValue: GlobalChatContextType = useMemo(
    () => ({
      chat: null,
      isChatOpen,
      setIsChatOpen,
      isAwaitingEditConfirmation: false,
      setIsAwaitingEditConfirmation: () => {},
      editTaskConfirmationData: null,
      setEditTaskConfirmationData: () => {},
      editDatasetConfirmationData: null,
      setEditDatasetConfirmationData: () => {},
      editScorersConfirmationData: null,
      setEditScorersConfirmationData: () => {},
      userConsentConfirmationData: null,
      setUserConsentConfirmationData: () => {},
      isDocked,
      setIsDocked,
      createNewSession: () => {},
      chatSessions: { sessions: [DEFAULT_SESSION] },
      setChatSessions: () => {},
      currentChatSessionId: DEFAULT_SESSION.id,
      setCurrentChatSessionId: () => {},
      currentSessionMessages: [],
      currentSessionContextObjects: {},
      model,
      setModel,
      currentSessionTools: DEFAULT_SESSION.tools,
      currentSessionMode: DEFAULT_SESSION.mode,
      currentSessionIsActive,
      currentSessionUserMessage,
      setCurrentSessionMessages: () => {},
      setCurrentSessionContextObjects: () => {},
      setCurrentSessionTools: () => {},
      setCurrentSessionMode: () => {},
      setCurrentSessionIsActive,
      setCurrentSessionUserMessage,
      handleSendMessage: async () => {
        openUpsellDialog();
      },
      handleAbort: () => {},
      deleteSession: () => {},
      createLLMScorerConfirmationData: null,
      setCreateLLMScorerConfirmationData: () => {},
      createCodeScorerConfirmationData: null,
      setCreateCodeScorerConfirmationData: () => {},
      implementedTools: [],
      pageKey: "playground",
      screenTooNarrow: false,
    }),
    [
      isChatOpen,
      isDocked,
      model,
      currentSessionIsActive,
      currentSessionUserMessage,
      openUpsellDialog,
    ],
  );

  return (
    // @ts-expect-error - this is a stubbed context, but won't affect anything
    <OptimizationContext.Provider value={OPTIMIZATION_CONTEXT_VALUE}>
      <GlobalChatContext.Provider value={globalChatContextValue}>
        {children}
      </GlobalChatContext.Provider>
    </OptimizationContext.Provider>
  );
}
