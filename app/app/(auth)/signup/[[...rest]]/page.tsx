"use client";

import { getRedirectParam } from "#/utils/auth/redirect";
import { SignUp, useSignUp } from "@clerk/nextjs";
import {
  Airtable,
  Instacart,
  Notion,
  Stripe,
  Vercel,
  Zapier,
} from "#/app/(landing)/logos";
import Link from "next/link";
import { use } from "react";
import { Input } from "#/ui/input";
import { Button } from "#/ui/button";
import { Google } from "#/ui/icons/providers";

export default function SignUpPage(props: {
  searchParams: Promise<Record<string, string | string[]>>;
}) {
  const searchParams = use(props.searchParams);
  const redirect = getRedirectParam(searchParams);
  const forceRedirectUrl = `/redirects/after-signin${
    redirect ? `?redirect_url=${encodeURIComponent(redirect)}` : ""
  }`;

  const { isLoaded } = useSignUp();

  const footer = (
    <>
      <div className="border-y py-4 text-sm border-primary-100 text-primary-500">
        By signing up, you agree to our{" "}
        <Link href="/legal/privacy-policy" className="text-accent-700">
          Privacy Policy
        </Link>{" "}
        and{" "}
        <Link href="/legal/terms-of-service" className="text-accent-700">
          Terms of Service
        </Link>
      </div>
      <div className="pt-4 text-sm text-primary-500">
        Trusted by leading AI teams
        <div className="flex flex-wrap items-center gap-x-12 gap-y-5 pt-6 text-primary-700 md:gap-x-12">
          <Stripe isMono />
          <Vercel isMono />
          <Notion isMono />
          <Zapier isMono />
          <Instacart isMono />
          <Airtable isMono />
        </div>
      </div>
    </>
  );

  // Show placeholder while Clerk is loading
  if (!isLoaded) {
    return (
      <div className="flex flex-col pb-4 pt-8 leading-[1.38462]">
        <h1 className="mb-8 text-balance text-left font-display text-4xl font-medium leading-tight tracking-[-0.01em] text-primary-900 sm:text-5xl">
          Create your Braintrust account
        </h1>
        <div className="mb-6 flex flex-col gap-2">
          <label>Email address</label>
          <Input
            type="email"
            disabled
            placeholder="Enter email address"
            className="h-11 text-base"
          />
        </div>
        <div className="mb-8 flex flex-col gap-2">
          <label>Password</label>
          <Input
            type="password"
            disabled
            placeholder="Enter password"
            className="h-11 text-base"
          />
        </div>
        <Button variant="primary" disabled className="mb-6 h-11 text-base">
          Continue
        </Button>
        <div className="mb-6 flex items-center gap-4">
          <span className="flex-1 border-b" />
          <span className="text-xs uppercase text-primary-400">Or</span>
          <span className="flex-1 border-b" />
        </div>
        <Button
          variant="default"
          disabled
          className="mb-10 h-11 gap-3 border text-sm font-medium"
        >
          <Google size={20} />
          Continue with Google
        </Button>
        <div className="mb-4 text-sm text-primary-500">
          Already have a Braintrust account?{" "}
          <Link href="/signin" className="font-medium text-accent-700">
            Sign in
          </Link>
        </div>
        {footer}
      </div>
    );
  }

  return (
    <>
      <SignUp
        // NOTE: We could have different semantics for what we do when someone
        // signs in vs. signs up, but we just don't right now.
        signInForceRedirectUrl={forceRedirectUrl}
        forceRedirectUrl={forceRedirectUrl}
        signInUrl="/signin"
      />
      {footer}
    </>
  );
}
