"use client";

import {
  BROWSER_EXTENSION_ERROR_MESSAGE,
  GENERAL_ERROR_MESSAGE,
  isBrowserExtensionError,
} from "#/utils/errors/error-helpers";
import * as Sentry from "@sentry/nextjs";
import { useEffect } from "react";
import { ProgressiveBlurShape } from "./(landing)/progressive-blur-shape";
import { AlertTriangle } from "lucide-react";

export default function GlobalError({
  error,
}: {
  error: Error & { digest?: string };
}) {
  const isExtensionError = isBrowserExtensionError(error);

  useEffect(() => {
    if (Sentry && !isExtensionError) {
      Sentry.captureException(error, {
        tags: {
          page: "global-error",
        },
      });
    }
  }, [error, isExtensionError]);

  const message = isExtensionError
    ? BROWSER_EXTENSION_ERROR_MESSAGE
    : GENERAL_ERROR_MESSAGE;

  return (
    <html className="dark">
      <body className="bg-background">
        <ErrorPageContent digest={error.digest}>{message}</ErrorPageContent>
      </body>
    </html>
  );
}

export const ErrorPageContent = ({
  digest,
  children,
}: React.PropsWithChildren<{ digest?: string }>) => (
  <div className="z-[-1] flex flex-col items-center px-8 py-36">
    <AlertTriangle className="z-10 mb-6 size-5 text-bad-800" />
    <div className="z-0">
      <ProgressiveBlurShape
        strength={0.07}
        direction="right"
        shapeClassName=""
        className="h-[48px] w-[240px]"
      >
        <div className="mb-4 font-display text-5xl font-medium text-bad-800">
          OOOOOPS
        </div>
      </ProgressiveBlurShape>
    </div>
    <div className="z-10">
      <div className="my-6 max-w-md text-pretty text-center">{children}</div>
      {digest && (
        <div className="text-center font-mono text-xs opacity-50">
          Digest {digest}
        </div>
      )}
    </div>
  </div>
);
