import { generateOpenGraphUrl } from "#/lib/opengraph";
import { type Metadata } from "next";

export const buildMetadata = ({
  title,
  skipTitleFormatting = false,
  sections = [],
  description,
  relativeUrl = "",
  ogImageUrl,
  ogTemplate,
  twitterImageUrl,
  type = "website",
  publishedTime,
  modifiedTime,
  authors,
  tags,
}: {
  title: string;
  skipTitleFormatting?: boolean;
  description?: string;
  relativeUrl?: string;
  ogImageUrl?: string;
  twitterImageUrl?: string;
  sections?: string[];
  type?: "website" | "article";
  publishedTime?: string;
  modifiedTime?: string;
  ogTemplate?: "docs" | "blog" | "product";
  authors?: string[];
  tags?: string[];
}): Metadata => {
  const formattedTitle = skipTitleFormatting
    ? title
    : `${title} - ${sections.map((s) => `${s} - `).join("")}Braintrust`;
  const url = `https://www.braintrust.dev${relativeUrl}`;

  // Enhanced keywords for better SEO coverage
  const baseKeywords = [
    "AI evaluation",
    "LLM evaluation",
    "AI testing",
    "LLM testing",
    "AI observability",
    "LLM observability",
    "AI monitoring",
    "LLM monitoring",
    "AI debugging",
    "LLM debugging",
    "AI development platform",
    "LLM development platform",
    "AI evaluation framework",
    "LLM evaluation framework",
    "AI performance testing",
    "LLM performance testing",
    "braintrust",
    "openai",
    "anthropic",
    "claude",
    "gpt",
    "llm ops",
    "mlops",
    "ai ops",
    "prompt engineering",
    "prompt testing",
    "ai metrics",
    "llm metrics",
    "ai analytics",
    "llm analytics",
  ];

  const allKeywords = tags ? [...baseKeywords, ...tags] : baseKeywords;

  return {
    title: formattedTitle,
    description,
    keywords: allKeywords,
    authors: authors?.map((author) => ({ name: author })),
    alternates: {
      canonical: url,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    icons: {
      apple: [
        {
          media: "(prefers-color-scheme: light)",
          url: "/icon180.png",
          href: "/icon180.png",
        },
        {
          media: "(prefers-color-scheme: dark)",
          url: "/icon-dark180.png",
          href: "/icon-dark180.png",
        },
      ],
      icon: [
        {
          media: "(prefers-color-scheme: light)",
          type: "image/x-icon",
          url: "/icon.png",
          href: "/icon.png",
        },
        {
          media: "(prefers-color-scheme: dark)",
          type: "image/x-icon",
          url: "/icon-dark.png",
          href: "/icon-dark.png",
        },
      ],
    },
    openGraph: {
      images:
        ogImageUrl ||
        generateOpenGraphUrl({ title, description, template: ogTemplate }),
      siteName: "Braintrust",
      title: formattedTitle,
      description,
      url,
      locale: "en_US",
      type,
      ...(type === "article" && {
        publishedTime,
        modifiedTime,
        authors,
        tags,
      }),
    },
    twitter: {
      card: "summary_large_image",
      title: formattedTitle,
      description,
      creator: "@braintrustdata",
      site: "@braintrustdata",
      images:
        twitterImageUrl ||
        generateOpenGraphUrl({ title, description, template: ogTemplate }),
    },
  };
};
