"use client";

import React, { useState } from "react";
import { Input } from "#/ui/input";
import { <PERSON><PERSON>, MoreVertical, Search, Trash2, UsersRound } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "#/ui/basic-table";
import { BasicTooltip } from "#/ui/tooltip";

import NewGroupButton from "./new-group-button";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { deleteGroup } from "./api-groups";
import { useOrg } from "#/utils/user";
import { type getGroupsSummary } from "./group-actions";
import { Dialog } from "#/ui/dialog";
import OrgLevelPermissionsDialog from "#/ui/permissions/org-level-permissions-dialog";
import { toast } from "sonner";
import { useQueryFunc } from "#/utils/react-query";
import { But<PERSON> } from "#/ui/button";
import {
  Dropdown<PERSON><PERSON><PERSON>,
  Dropdown<PERSON>enuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { GroupMembersDialog } from "./group-members-dialog";
import { useSessionToken } from "#/utils/auth/session-token";
import { type Permission } from "@braintrust/core/typespecs";

import { ObjectPermissionsDialog } from "../../p/[project]/permissions/object-permissions-dialog";
import { TableEmptyState } from "#/ui/table/TableEmptyState";

interface Group {
  id: string;
  name: string;
  description?: string | null | undefined;
}

function ClientPage({
  groups: groupsFromServer,
  orgName,
  orgGroupPermissions,
}: {
  groups: Group[];
  orgName: string;
  orgGroupPermissions: Permission[];
}) {
  const { api_url: apiUrl, id: orgId } = useOrg();
  const [pendingGroupToDelete, setPendingGroupToDelete] = useState<{
    id: string;
    name: string;
  } | null>(null);

  const [groupToSetPermissions, setGroupToSetPermissions] = useState<{
    id: string;
    name: string;
  } | null>(null);

  const [groupToSetAccess, setGroupToSetAccess] = useState<{
    id: string;
    name: string;
  } | null>(null);

  const [searchQuery, setSearchQuery] = useState("");

  const { data: groups, invalidate: reloadGroups } = useQueryFunc<
    typeof getGroupsSummary
  >({
    fName: "getGroupsSummary",
    args: { org_name: orgName },
    serverData: groupsFromServer,
  });

  const filteredGroups = groups.filter((item) =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
  };

  const isEmptyGroups = filteredGroups.length === 0 && !searchQuery;
  const isEmptySearchResults = filteredGroups.length === 0 && searchQuery;
  const hasResults = filteredGroups.length > 0;

  const allGroupNames = groups.map((group) => group.name.toLowerCase());

  const [editingName, setEditingName] = useState<string | null>(null);

  const canCreateGroups = orgGroupPermissions.includes("create");

  const { getOrRefreshToken } = useSessionToken();

  return (
    <div className="max-w-4xl">
      {!isEmptyGroups && (
        <div className="relative flex gap-4">
          <div className="relative grow">
            <Search
              size={14}
              className="pointer-events-none absolute left-2 top-3 text-primary-500"
            />
            <Input
              placeholder="Find permission group"
              className="flex-1 py-1 pl-7"
              onChange={handleSearchChange}
              value={searchQuery}
            />
          </div>
          {canCreateGroups && (
            <NewGroupButton
              orgName={orgName}
              onCreate={reloadGroups}
              apiUrl={apiUrl}
              allGroupNames={allGroupNames}
            />
          )}
        </div>
      )}
      {isEmptySearchResults && (
        <div className="mt-4">No permission groups found</div>
      )}
      {isEmptyGroups && (
        <TableEmptyState label="This organization has no permission groups, or you do not have permission to see them" />
      )}
      {hasResults && (
        <Table className="mt-6 table-auto text-left">
          <TableHeader>
            <TableRow>
              <TableHead className="w-48">Name</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredGroups.map((row) => (
              <TableRow key={row.id} className="py-2">
                <TableCell className="w-full flex-1 pr-0">{row.name}</TableCell>
                <TableCell className="flex items-center justify-end gap-2 pr-0">
                  <BasicTooltip tooltipContent="Edit organization level permissions for the members of this group">
                    <Button
                      onClick={() =>
                        setGroupToSetPermissions({
                          id: row.id,
                          name: row.name,
                        })
                      }
                      size="xs"
                    >
                      Permissions
                    </Button>
                  </BasicTooltip>
                  <BasicTooltip tooltipContent="Edit who can access and modify this group">
                    <Button
                      onClick={() =>
                        setGroupToSetAccess({
                          id: row.id,
                          name: row.name,
                        })
                      }
                      size="xs"
                    >
                      Group access
                    </Button>
                  </BasicTooltip>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button size="xs">
                        <MoreVertical className="size-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuGroup>
                        <DropdownMenuItem
                          className="text-xs"
                          onSelect={() => setEditingName(row.name)}
                        >
                          <UsersRound className="size-3" />
                          Members
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-xs"
                          onSelect={() => {
                            navigator.clipboard.writeText(row.id);
                            toast("Permission group ID copied to clipboard");
                          }}
                        >
                          <Copy className="size-3" />
                          Copy permission group ID
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-xs text-bad-600"
                          onSelect={() =>
                            setPendingGroupToDelete({
                              id: row.id,
                              name: row.name,
                            })
                          }
                        >
                          <Trash2 className="size-3" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuGroup>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
            {pendingGroupToDelete && (
              <ConfirmationDialog
                title="Remove permission group"
                description={`Are you sure you want to remove the ${pendingGroupToDelete.name} permission group? This action cannot be undone.`}
                open={Boolean(pendingGroupToDelete)}
                onOpenChange={(isOpen) => {
                  if (!isOpen) {
                    setPendingGroupToDelete(null);
                  }
                }}
                onConfirm={async () => {
                  try {
                    await deleteGroup({
                      groupId: pendingGroupToDelete.id,
                      apiUrl,
                      sessionToken: await getOrRefreshToken(),
                    });
                    reloadGroups();
                  } catch (e) {
                    console.error(e);
                    toast.error("Something went wrong deleting this group.", {
                      description: e instanceof Error ? e.message : String(e),
                    });
                  }
                }}
                confirmText="Confirm"
              />
            )}
            {groupToSetPermissions && (
              <Dialog
                open
                onOpenChange={(isOpen) => {
                  if (!isOpen) {
                    setGroupToSetPermissions(null);
                  }
                }}
              >
                <OrgLevelPermissionsDialog
                  orgId={orgId ?? ""}
                  userOrGroupLabel={groupToSetPermissions.name}
                  userOrGroupId={groupToSetPermissions.id}
                  userObjectType="group"
                  onSubmit={() => {
                    setGroupToSetPermissions(null);
                  }}
                />
              </Dialog>
            )}
            {groupToSetAccess && (
              <ObjectPermissionsDialog
                open
                onOpenChange={(open) => {
                  if (!open) {
                    setGroupToSetAccess(null);
                  }
                }}
                orgName={orgName}
                objectId={groupToSetAccess.id}
                objectName={groupToSetAccess.name}
                objectType="group"
              />
            )}
          </TableBody>
        </Table>
      )}
      <Dialog
        open={editingName !== null}
        onOpenChange={(o) => {
          if (!o) setEditingName(null);
        }}
      >
        {editingName && (
          <GroupMembersDialog orgName={orgName} name={editingName} />
        )}
      </Dialog>
    </div>
  );
}

export default ClientPage;
