import { useEntityStorage } from "#/lib/clientDataStorage";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import {
  useLoadViews,
  useViewOperations,
  type View,
  type ViewParams,
} from "#/utils/view/use-view-generic";
import {
  monitorViewOptionsSchema,
  type MonitorViewOptions,
} from "@braintrust/core/typespecs";
import { useSearchParams } from "next/navigation";
import {
  parseAsBoolean,
  parseAsString,
  parseAsStringLiteral,
  useQueryState,
} from "nuqs";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { type ProjectSummary } from "../org-actions";
import { type UrlSearch, urlSearchSchema } from "#/utils/search/search";
import { parseAsJsonEncoded } from "#/ui/query-parameters";

type MonitorOptionsWithSearch = MonitorViewOptions & {
  search?: UrlSearch;
};

const viewTypeEnum = monitorViewOptionsSchema.shape.type
  .unwrap()
  .unwrap().options;

export const useMonitorView = (projects: ProjectSummary[]) => {
  const { getOrRefreshToken } = useSessionToken();
  const { id: orgId, name: orgName, api_url: apiUrl } = useOrg();

  // Track if we've done the initial load
  const hasInitializedView = useRef(false);

  // "local" view states, these are our source of truth and tied to url params
  const [viewParam, setViewParam] = useQueryState("v", parseAsString);

  const [projectId, setProjectId] = useQueryState("projectId", parseAsString);
  const [spanType, setSpanType] = useQueryState(
    "timeType",
    monitorViewOptionsSchema.shape.spanType,
  );
  const [rangeValue, setRangeValue] = useQueryState(
    "timeRange",
    monitorViewOptionsSchema.shape.rangeValue,
  );
  const [frameStart, setFrameStart] = useQueryState(
    "frameStart",
    monitorViewOptionsSchema.shape.frameStart,
  );
  const [frameEnd, setFrameEnd] = useQueryState(
    "frameEnd",
    monitorViewOptionsSchema.shape.frameEnd,
  );
  const [tzUTC, setTzUTC] = useQueryState(
    "tzUTC",
    parseAsBoolean.withDefault(false),
  );
  const [type, setType] = useQueryState(
    "rowType",
    parseAsStringLiteral(viewTypeEnum).withDefault("project"),
  );
  const [groupBy, setGroupBy] = useQueryState(
    "groupBy",
    parseAsString.withDefault(""),
  );
  const [search, setSearch] = useQueryState(
    "search",
    parseAsJsonEncoded(urlSearchSchema.parse).withDefault({}),
  );

  const searchParams = useSearchParams();

  const viewParams: ViewParams = useMemo(
    () => ({
      objectType: "org_project",
      objectId: orgId ?? "", //TODO: Handle case where orgId is not set
      viewType: "monitor",
    }),
    [orgId],
  );

  const queryKey = useMemo(
    () => [
      "views",
      viewParams.objectType,
      viewParams.objectId,
      viewParams.viewType,
    ],
    [viewParams.objectType, viewParams.objectId, viewParams.viewType],
  );

  const getViewsArgs = useMemo(
    () => ({
      apiUrl,
      getOrRefreshToken,
      viewParams,
    }),
    [apiUrl, getOrRefreshToken, viewParams],
  );

  const localViewOptions: MonitorOptionsWithSearch = useMemo(
    () => ({
      spanType,
      rangeValue,
      frameStart,
      frameEnd,
      tzUTC,
      projectId,
      type,
      groupBy,
      search,
    }),
    [
      spanType,
      rangeValue,
      frameStart,
      frameEnd,
      tzUTC,
      projectId,
      type,
      groupBy,
      search,
    ],
  );

  const [loadedViewType, setLoadedViewType] = useState<
    "project" | "experiment"
  >("project");

  const loadView = useCallback(
    (view: View | null, initialize?: boolean) => {
      if (view?.options) {
        // This page should only receive monitor views
        if ("viewType" in view.options && view.options.viewType === "monitor") {
          const optsWithSearch = {
            ...view.options.options,
            search: view?.view_data?.search,
          };
          // @ts-ignore search view isn't typed correctly
          setLoadedViewOptions(optsWithSearch);

          setLoadedViewType(optsWithSearch.type ?? "project");
          setLastSavedJSON(JSON.stringify(optsWithSearch));
        } else {
          console.warn("Received non-monitor view on monitor page:", view);
        }
      } else {
        // else load the current options (initially from url params)
        setLoadedViewOptions(localViewOptions);
      }

      if (view?.builtin) {
        setViewParam(null);
      } else {
        setViewParam(view?.name ?? null);
      }
    },
    [setViewParam, localViewOptions],
  );

  const {
    isUpdating: isUpdatingViews,
    createViewAsync,
    updateView,
    deleteView,
  } = useViewOperations({
    queryKey,
    viewParams,
    getViewsArgs,
    loadView,
  });

  const { data: views, isPending: isPendingViews } = useLoadViews({
    queryKey,
    viewParams,
    getViewsArgs,
    enabled: true,
  });

  const allDataView = useMemo(
    () => ({ id: null, builtin: true, name: "All data" }),
    [],
  );
  const allViews = useMemo(
    () => [allDataView, ...(views ?? [])],
    [allDataView, views],
  );

  const selectedView = useMemo(
    () => allViews.find((v) => v.name === viewParam) ?? allDataView,
    [allViews, viewParam, allDataView],
  );

  const pageIdentifier = `org-monitor-${orgId}`;

  // Get the user's default view preference from localStorage
  const [defaultViewName] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: pageIdentifier,
    key: "defaultView",
  });

  const [loadedViewOptions, setLoadedViewOptions] =
    useState<MonitorOptionsWithSearch | null>();
  const [lastSavedJSON, setLastSavedJSON] = useState<string | null>(null);

  const isViewDirty = useMemo(() => {
    const optionsJSON = JSON.stringify(localViewOptions);
    return optionsJSON !== lastSavedJSON;
  }, [localViewOptions, lastSavedJSON]);

  // Handle view parameter loading on page initialization
  useEffect(() => {
    if (isPendingViews || hasInitializedView.current) {
      return;
    }

    // Handle case where URL parameter exists
    if (viewParam !== null) {
      const viewToLoad = allViews.find((v) => v.name === viewParam);
      if (viewToLoad) {
        loadView(viewToLoad, true);
      } else {
        // View doesn't exist, clear URL and load builtin view
        setViewParam(null);
        loadView(null, true);
      }
      hasInitializedView.current = true;
      return;
    }

    // No URL view parameter - check for other URL params and default view
    const hasAnyUrlParams = searchParams && searchParams.toString().length > 0;
    if (!hasAnyUrlParams && defaultViewName) {
      const defaultViewToLoad = allViews.find(
        (v) => v.name === defaultViewName,
      );
      loadView(defaultViewToLoad || null, true);
    } else {
      loadView(null, true);
    }

    hasInitializedView.current = true;
  }, [
    viewParam,
    isPendingViews,
    searchParams,
    allViews,
    loadView,
    setViewParam,
    allDataView,
    defaultViewName,
  ]);

  // update local states when loaded view options change
  useEffect(() => {
    if (!loadedViewOptions) {
      return;
    }

    setSpanType(loadedViewOptions.spanType ?? null);
    setRangeValue(loadedViewOptions.rangeValue ?? null);
    setFrameStart(loadedViewOptions.frameStart ?? null);
    setFrameEnd(loadedViewOptions.frameEnd ?? null);
    setTzUTC(loadedViewOptions.tzUTC ?? null);
    setProjectId(loadedViewOptions.projectId ?? null);
    setGroupBy(loadedViewOptions.groupBy ?? null);
    setSearch(loadedViewOptions.search ?? null);
  }, [
    loadedViewOptions,
    setFrameEnd,
    setFrameStart,
    setGroupBy,
    setProjectId,
    setRangeValue,
    setSearch,
    setSpanType,
    setTzUTC,
  ]);

  useEffect(() => {
    setType(loadedViewType);
  }, [loadedViewType, setType]);

  // set a default projectId
  const [urlProjectId] = useQueryState("projectId", parseAsString);
  const [recentProjectIds] = useEntityStorage({
    entityType: "org",
    entityIdentifier: orgName,
    key: "recentProjectIds",
  });

  useEffect(() => {
    // do nothing if already exists
    if (projectId) {
      return;
    }

    // use url if set
    if (urlProjectId?.length) {
      setProjectId(urlProjectId);
      return;
    }

    // use most recent project if it exists
    if (recentProjectIds.length) {
      setProjectId(recentProjectIds[0]);
      return;
    }

    // else we initialize to first in projects list
    setProjectId(projects?.[0]?.project_id);
  }, [urlProjectId, projectId, recentProjectIds, projects, setProjectId]);

  const renameView = useCallback(
    ({ name }: { name: string }) => {
      const { search, ...options } = localViewOptions;

      updateView({
        viewId: selectedView.id!,
        name,
        viewData: { search },
        options: {
          viewType: "monitor",
          options,
        },
      });
      setViewParam(name);
    },
    [updateView, selectedView.id, localViewOptions, setViewParam],
  );

  const createView = useCallback(
    async (name: string) => {
      const { search, ...options } = localViewOptions;
      const newView = await createViewAsync({
        name,
        viewData: { search },
        options: {
          viewType: "monitor",
          options,
        },
      });

      // Switch to the newly created view
      if (newView) {
        loadView(newView);
      }
      return newView;
    },
    [createViewAsync, localViewOptions, loadView],
  );

  const saveView = useCallback(() => {
    const { search, ...options } = localViewOptions;
    updateView({
      viewId: selectedView.id!,
      viewData: { search },
      options: {
        viewType: "monitor",
        options,
      },
    });

    setLastSavedJSON(JSON.stringify(localViewOptions));
  }, [updateView, selectedView.id, localViewOptions]);

  return {
    isPendingViews,
    isUpdatingViews,
    createViewAsync,
    deleteView,

    allViews,
    selectedView,
    selectedViewName: viewParam,
    setSelectedViewName: setViewParam,

    loadedViewOptions, // only for time controls

    pageIdentifier,
    loadView,
    renameView,
    createView,
    saveView,
    isViewDirty,

    // view options state and setters
    viewType: type,
    setViewType: setType,
    projectId,
    setProjectId,
    spanType,
    setSpanType,
    rangeValue,
    setRangeValue,
    frameStart,
    setFrameStart,
    frameEnd,
    setFrameEnd,
    tzUTC,
    setTzUTC,
    groupBy,
    setGroupBy,
    search,
    setSearch,
  };
};
