import { type TimeseriesVizType } from "#/ui/charts/timeseries/timeseries.types";

interface MeasureConfig {
  btql: string;
  durationPercentile?: number;
  alias: string;
  displayName?: string;
}

export type CardIconName =
  | "dollar"
  | "snail"
  | "message-circle"
  | "blocks"
  | "percent"
  | "clock"
  | "bolt"
  | "alert-circle"
  | "timer";

export type UnitType = "duration" | "percent" | "count" | "cost";

export interface MonitorCardConfig {
  /** Name used for react key  */
  name: string;

  /** Header of the card (omit to hide) */
  header?: {
    /** Title in the card header */
    title: string;

    /** Icon on top left of card header */
    iconName: CardIconName;
  };

  /** Name used for pivot key */
  idName: string;

  /** Timeseries viz type */
  vizType: TimeseriesVizType;

  /** Measures to query for */
  measures: MeasureConfig[];

  /** Unit to format numbers appropriately */
  unitType?: UnitType;

  /** Optionally append group by to query */
  additionalGroupBy?: { alias: string; btql: string };

  /** Calculations specific for cost data */
  applyCrunchCosts?: boolean;

  /** pivot query */
  pivot?: string;

  /** unpivot query */
  unpivot?: [string, string];

  /** tool metric specific category */
  toolMetric?: "count" | "error_rate" | "p50_duration";
}
