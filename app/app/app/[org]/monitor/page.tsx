import ClientPage, { type Params } from "./clientpage";
import { type Metadata, type ResolvingMetadata } from "next";
import { decodeURIComponentPatched } from "#/utils/url";
import { buildMetadata } from "#/app/metadata";
import { getProjectSummary } from "../org-actions";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const projectSummary = await getProjectSummary({ org_name });

  return <ClientPage params={params} projectSummary={projectSummary} />;
}

export async function generateMetadata(
  props: { params: Promise<Params> },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "Monitor",
    sections: [orgName],
    description: `${orgName}`,
    relativeUrl: `/${params.org}/monitor`,
    ogTemplate: "product",
  });
}
