import { FilterEditor, type MinimalColumnData } from "#/ui/table/filter-editor";
import { HeaderIcons } from "#/ui/table/formatters/header-formatters";
import { useQueryFunc } from "#/utils/react-query";
import { type Clause<PERSON><PERSON><PERSON>, type Search } from "#/utils/search/search";
import { BlocksIcon } from "lucide-react";
import { useMemo, type Dispatch, type SetStateAction } from "react";
import type { fetchProjectConfig } from "../p/[project]/configuration/configuration-actions";
import { useOrg } from "#/utils/user";
import type { ProjectSummary } from "../org-actions";

const hardcodedCols = [
  "metadata",
  "scores",
  "input",
  "output",
  "expected",
  "tags",
];

const MONITOR_FILTER_COLUMNS: MinimalColumnData<null>[] = hardcodedCols.map(
  (colName) => ({
    id: colName,
    columnDefId: colName,
    meta: {
      name: colName,
      path: [colName],
      header: () => null,
      type: "object",
    },
  }),
);

const MONITOR_FILTER_FORMATTERS = Object.fromEntries(
  hardcodedCols.map((name) => [
    name,
    { headerIcon: HeaderIcons[name] ?? BlocksIcon },
  ]),
);

interface MonitorFilterEditorProps {
  objectId?: string | null;
  objectType?: string | null;
  clauseChecker: ClauseChecker | null;
  search?: Search;
  setSearch: Dispatch<SetStateAction<Search>>;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  projects: ProjectSummary[];
  projectId: string;
}

/** a thin wrapper of FilterEditor */
export const MonitorFilterEditor = ({
  clauseChecker,
  isOpen,
  setIsOpen,
  search,
  setSearch,
  objectType,
  objectId,
  projects,
  projectId,
}: MonitorFilterEditorProps) => {
  const { name: orgName } = useOrg();

  const project = projects.find((p) => p.project_id === projectId);
  const projectName = project?.project_name ?? "";

  const args = useMemo(
    () => ({ org_name: orgName, project_name: projectName }),
    [orgName, projectName],
  );

  const { data: config, invalidate: mutateConfig } = useQueryFunc<
    typeof fetchProjectConfig
  >({
    fName: "fetchProjectConfig",
    args,
  });

  const tagsProvidedProject = useMemo(() => {
    if (!projectName || !config) {
      return undefined;
    }
    return {
      projectId,
      projectName,
      config: config,
      mutateConfig,
    };
  }, [config, mutateConfig, projectId, projectName]);

  const selectableColumns = useMemo(() => {
    if (tagsProvidedProject) {
      return MONITOR_FILTER_COLUMNS;
    }
    return MONITOR_FILTER_COLUMNS.filter((col) => col.id !== "tags");
  }, [tagsProvidedProject]);

  return (
    <FilterEditor
      selectableColumns={selectableColumns}
      formatters={MONITOR_FILTER_FORMATTERS}
      clauseChecker={clauseChecker}
      isOpen={isOpen}
      setOpen={setIsOpen}
      search={search}
      setSearch={setSearch}
      objectType={objectType}
      objectId={objectId}
      tagsProvidedProject={tagsProvidedProject}
    />
  );
};
