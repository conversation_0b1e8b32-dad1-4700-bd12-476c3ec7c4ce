import { type TIME_BUCKETS } from "#/ui/charts/scale-time";
import { parseAsJsonEncoded } from "#/ui/query-parameters";
import { type UrlSearch, urlSearchSchema } from "#/utils/search/search";
import { getOrgLink } from "../getOrgLink";
import { addMinutes, addHours, addDays, format } from "date-fns";
import {
  getExperimentsPageRedirectLink,
  getObjectRedirectLink,
} from "#/app/app/[org]/object/object-actions";

export const getMonitorLink = ({
  orgName,
  projectId,
}: {
  orgName: string;
  projectId?: string;
}) =>
  `${getOrgLink({ orgName })}/monitor${projectId ? `?projectId=${projectId}` : ""}`;

function getEndTime({
  timeBucket,
  time,
}: {
  timeBucket: TIME_BUCKETS;
  time: string;
}) {
  let endTime = new Date(time);

  if (timeBucket === "minute") {
    endTime = addMinutes(endTime, 1);
  } else if (timeBucket === "hour") {
    endTime = addHours(endTime, 1);
  } else if (timeBucket === "day") {
    endTime = addDays(endTime, 1);
  }

  return endTime.toISOString();
}

export const getTracesLink = async ({
  orgName,
  projectId,
  from,
  timeBucket,
  time,
  existingSearch,
}: {
  orgName: string;
  projectId: string;
  from: "experiment" | "project_logs";
  timeBucket: TIME_BUCKETS;
  time: string;
  existingSearch?: UrlSearch;
}) => {
  // The Experiments page does not have a 'created' column, so we use 'last_updated' instead
  const searchField = from === "project_logs" ? "created" : "last_updated";
  const searchFieldLabel = from === "project_logs" ? "Created" : "Updated";

  const endTime = getEndTime({ time, timeBucket });
  const dateFormat =
    timeBucket === "day" ? "MMM dd, yyyy" : "MMM dd, yyyy hh:mm a";

  const filterQuery: UrlSearch = {
    filter: [
      {
        text: `${searchField} >= \"${time}\" AND ${searchField} <= \"${endTime}\"`,
        label: `${searchFieldLabel} custom range… ${format(new Date(time), dateFormat)} - ${format(new Date(endTime), dateFormat)}`,
      },
      ...(existingSearch?.filter ?? []),
    ],
    match: existingSearch?.match ?? [],
  };

  const parser = parseAsJsonEncoded(urlSearchSchema.parse);
  const filterQueryString = parser.serialize(filterQuery);

  let logsOrExperimentsLink;

  if (from === "project_logs") {
    logsOrExperimentsLink = await getObjectRedirectLink({
      orgName,
      object_type: from,
      object_id: projectId,
      _xact_id: "",
      id: "",
    });
  } else if (from === "experiment") {
    logsOrExperimentsLink = await getExperimentsPageRedirectLink({
      orgName,
      projectId,
    });
  }

  return `${logsOrExperimentsLink}?search=${encodeURIComponent(filterQueryString)}`;
};
