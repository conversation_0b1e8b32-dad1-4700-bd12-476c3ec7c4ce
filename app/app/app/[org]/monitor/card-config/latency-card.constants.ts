import { type MonitorCardConfig } from "../card/monitor-card-config.types";

export const LATENCY_CARD_CONFIG: MonitorCardConfig = {
  header: {
    title: "Latency",
    iconName: "snail",
  },
  name: "Latency query",
  idName: "latency",
  vizType: "lines",
  measures: [
    {
      alias: "p50_duration",
      displayName: "P50",
      btql: "",
      durationPercentile: 0.5,
    },
    {
      alias: "p95_duration",
      displayName: "P95",
      btql: "",
      durationPercentile: 0.95,
    },
  ],
  unitType: "duration",
};
