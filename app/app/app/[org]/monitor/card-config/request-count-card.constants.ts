import { type MonitorCardConfig } from "../card/monitor-card-config.types";

export const REQUEST_COUNT_CARD_CONFIG: MonitorCardConfig = {
  header: {
    title: "Spans",
    iconName: "message-circle",
  },
  name: "Spans summary query",
  idName: "spans",
  vizType: "bars",
  measures: [
    {
      alias: "llm_count",
      displayName: "LLM calls",
      btql: "sum(span_attributes.type = 'llm' ? 1 : 0)",
    },
    {
      alias: "tool_count",
      displayName: "Tool calls",
      btql: "sum(span_attributes.type = 'tool' ? 1 : 0)",
    },
    {
      alias: "spans",
      displayName: "Other spans",
      btql: "count(1) - tool_count - llm_count",
    },
  ],
  unitType: "count",
};
