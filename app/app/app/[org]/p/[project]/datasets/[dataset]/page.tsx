import ClientPage, { type DatasetPanelLayout, type Params } from "./clientpage";
import { type Metadata, type ResolvingMetadata } from "next";
import { fetchDatasetInfo } from "./actions";
import { decodeURIComponentPatched } from "#/utils/url";
import { buildMetadata } from "#/app/metadata";
import { cookies } from "next/headers";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const project_name = decodeURIComponentPatched(params.project);
  const dataset_name = decodeURIComponentPatched(params.dataset);
  const datasetInfo = await fetchDatasetInfo({
    org_name,
    project_name,
    dataset_name,
  });

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#server-component
  const layout = (await cookies()).get("react-resizable-panels:dataset-layout");

  const defaultPanelLayout: DatasetPanelLayout = {
    main: 80,
    trace: 20,
    ...(layout ? JSON.parse(layout.value) : {}),
  };

  return (
    <ClientPage
      params={params}
      datasetInfo={datasetInfo}
      defaultPanelLayout={defaultPanelLayout}
    />
  );
}

export async function generateMetadata(
  props: { params: Promise<Params> },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const params = await props.params;
  const datasetName = decodeURIComponentPatched(params.dataset);
  const projectName = decodeURIComponentPatched(params.project);
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: datasetName,
    sections: ["Datasets", projectName],
    description: `${orgName} / ${projectName}`,
    relativeUrl: `/${params.org}/p/${params.project}/datasets/${params.dataset}`,
    ogTemplate: "product",
  });
}
