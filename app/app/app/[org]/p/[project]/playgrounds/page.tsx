import ClientPage, { type Params } from "./clientpage";
import { getPromptSessionSummary } from "./playground-actions";
import { decodeURIComponentPatched } from "#/utils/url";
import { type Metadata } from "next";
import { buildMetadata } from "#/app/metadata";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const project_name = decodeURIComponentPatched(params.project);
  const prompts = await getPromptSessionSummary({
    org_name,
    project_name,
  });
  return <ClientPage params={params} promptSessions={prompts} />;
}

export async function generateMetadata(props: {
  params: Promise<Params>;
}): Promise<Metadata> {
  const params = await props.params;
  const projectName = decodeURIComponentPatched(params.project);
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "Playgrounds",
    sections: [projectName],
    description: `${orgName} / ${projectName}`,
    relativeUrl: `/${params.org}/p/${params.project}/playgrounds`,
    ogTemplate: "product",
  });
}
