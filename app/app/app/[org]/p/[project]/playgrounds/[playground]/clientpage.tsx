"use client";

import { AccessFailed } from "#/ui/access-failed";
import { produce } from "immer";
import { useAnalytics } from "#/ui/use-analytics";
import { OneLineTextPrompt } from "#/ui/dialogs/one-line-text-prompt";
import { MainContentWrapper } from "#/ui/layout/main-content-wrapper";
import { newId } from "#/utils/btapi/btapi";
import { deleteRow, type UpdateRowFn } from "#/utils/mutable-object";
import { useOrg } from "#/utils/user";
import { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { useAvailableModels } from "#/ui/prompts/models";
import {
  type UIFunctionData,
  type PromptData,
  type SyncedPlaygroundBlock,
} from "#/ui/prompts/schema";
import { type TransactionId } from "@braintrust/core";
import {
  METRIC_FIELDS,
  useMountedObject,
} from "#/app/app/[org]/prompt/[prompt]/mounted-object";
import { useUploadDatasetDialog } from "#/app/app/[org]/prompt/[prompt]/upload-dataset-dialog";
import { createDataset } from "../../datasets/[dataset]/createDataset";
import { isEmpty } from "#/utils/object";
import { type PromptSessionMeta } from "./playground-actions";
import { type FunctionType, type Permission } from "@braintrust/core/typespecs";
import { Loading } from "#/ui/loading";
import { type SavedScorer } from "#/utils/scorers";
import { ProjectContext } from "../../projectContext";
import {
  type PlaygroundSettings,
  DEFAULT_MAX_CONCURRENCY,
  DEFAULT_PLAYGROUND_SETTINGS,
} from "../settings";
import { decodeURIComponentPatched } from "#/utils/url";
import { usePlayxState } from "./playx/playx";
import { usePlaygroundVisibility } from "./use-playground-visibility";
import {
  PromptTransactionIdProvider,
  usePromptSession,
} from "./use-prompt-session";
import {
  type PromptSession,
  usePromptSessionData,
} from "./use-prompt-session-data";
import { useSavedPromptMeta } from "./use-saved-prompt-meta";
import { PlaygroundHeader } from "./playground-header";
import { ResizablePanel, ResizablePanelGroup } from "#/ui/resizable";
import { cn } from "#/utils/classnames";
import { PlaygroundControls, sortPrompts } from "./playground-controls";
import { type PanelLayout } from "../../experiments/[experiment]/clientpage";
import { PlaygroundPromptBlocks } from "./playground-prompt-blocks";
import {
  ExperimentTable,
  INITIALLY_VISIBLE_COLUMNS as DEFAULT_INITIALLY_VISIBLE_COLUMNS,
  NEVER_VISIBLE_COLUMNS as DEFAULT_NEVER_VISIBLE_COLUMNS,
  useExperimentTableProps,
} from "../../experiments/[experiment]/experiment-table";
import {
  useActiveRowAndSpan,
  usePlaygroundFullscreenTaskIndex,
  usePlaygroundPromptSheetIndexState,
} from "#/ui/query-parameters";
import { type ViewParams } from "#/utils/view/use-view";
import { useViewStates } from "#/utils/view/use-view";
import { useClauseChecker } from "#/utils/search-btql";
import { checkBTQLClause } from "#/utils/search-btql";
import { Plus, ShieldAlert } from "lucide-react";
import { useComparisonQueries } from "../../experiments/[experiment]/(queries)/(comparisons)/comparison-queries";
import { useTableControls } from "../../experiments/[experiment]/table-controls";
import {
  type Clause,
  useScoreMetricsTopLevelFields,
} from "#/utils/search/search";
import { useModelScan } from "../../experiments/[experiment]/(queries)/models";
import { useTraceFullscreen } from "#/ui/trace/use-trace-fullscreen";
import { useFullyCloseSidePanel } from "#/ui/trace/trace-panel";
import { ProjectPromptsDropdownProvider } from "../prompts-dropdown";
import { usePromptDisplayNames } from "./playx/use-prompt-display-names";
import { type RowId } from "#/utils/diffs/diff-objects";
import PlaygroundTraceSheet, {
  type PlaygroundTracePanelLayout,
} from "./playx/playground-trace-sheet";
import { usePlaygroundLogsDataFromBackend } from "./playx/experiment-data";
import { Button } from "#/ui/button";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { useTableSelection } from "#/ui/table/useTableSelection";
import { TraceSearchProvider } from "#/ui/trace/trace-search-context";
import { useAtom, useAtomValue } from "jotai";
import {
  GRID_INPUT_COLUMN_ID,
  GRID_EXPECTED_COLUMN_ID,
  GRID_METADATA_COLUMN_ID,
  GRID_TAGS_COLUMN_ID,
} from "#/ui/table/formatters/grid-layout-columns";
import { useScorerFunctions } from "#/app/app/[org]/prompt/[prompt]/scorers/open";
import { downloadAsCSV, downloadAsJSON } from "#/utils/download";
import { useJSONMemo } from "#/utils/memo";
import { ErrorBanner } from "#/ui/error-banner";
import { makeRowIdPrimaryOrigin } from "#/ui/table/use-active-row-effects";
import { type CreateExperimentTask } from "#/app/app/[org]/prompt/[prompt]/create-experiment-dialog";
import { PLAYGROUND_EXPERIMENT_FIELDS } from "../../experiments/[experiment]/(queries)/useExperiment";
import { InfoBanner } from "#/ui/info-banner";
import { Provider as JotaiProvider } from "jotai";
import { PromptBlocksSynced } from "../../prompts/synced/prompt-blocks-synced";
import {
  SyncedPromptsProvider,
  useSyncedPrompts,
} from "../../prompts/synced/use-synced-prompts";
import { AgentsDropdownProvider } from "../agents-dropdown";
import {
  isPlaygroundRunningAtom,
  isRunningAtom,
  streamingStateAtom,
  useCompletionPercentageAtom,
} from "./playx/atoms";
import {
  OptimizationProvider,
  type OptimizationProviderProps,
} from "#/utils/optimization/provider";
import { GROUP_BY_NONE_VALUE } from "#/ui/charts/selectionTypes";
import { RemoteEvalsProvider } from "./playx/remote-evals-provider";
import { AppliedScorers } from "./applied-scorers";
import { DEFAULT_ENDPOINT_URL } from "../remote-evals-dropdown";
import { PlaygroundAddTask } from "./add-task";
import {
  type PlaygroundCopilotContext,
  usePlaygroundCopilotContext,
} from "#/ui/copilot/playground";
import { selectAtom, useAtomCallback } from "jotai/utils";
import { type OrgContextT } from "#/utils/user-types";
import useEvent from "react-use-event-hook";
import {
  BodyWrapper,
  HEIGHT_WITH_DOUBLE_TOP_OFFSET,
  HEIGHT_WITH_TOP_OFFSET,
} from "#/app/app/body-wrapper";
import { useIsSidenavDocked } from "#/app/app/[org]/sidenav-state";
import { GlobalChatProvider } from "#/ui/optimization/global-chat-provider";
import {
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { DropdownMenu } from "#/ui/dropdown-menu";
import { GenerateRowsButton } from "./generate-rows-button";
import { BasicTooltip } from "#/ui/tooltip";
import { type SortDirection } from "@tanstack/react-table";
import isEqual from "lodash.isequal";
import { motion } from "motion/react";
import { DockedChatSpacer } from "./docked-chat-spacer";
import { useIsLoopEnabled } from "#/ui/optimization/optimization-chat";
import { newObjectName } from "#/utils/metadata";
import { type Diagnostic } from "@codemirror/lint";
import { ScorersDropdownProvider } from "../scorers-dropdown";
import { makePlaygroundTaskFunctionId } from "#/ui/prompts/hooks";
import { useQueryFunc } from "#/utils/react-query";
import { type fetchPromptListing } from "../playground-actions";
import { TableEmptyState } from "#/ui/table/TableEmptyState";

export type PlaygroundScoreSort = {
  outputName: string;
  scoreName: string;
  dir: SortDirection;
};
export interface Params {
  org: string;
  project: string;
  playground: string;
}

type Props = {
  params: Params;
  promptSessionMeta: PromptSessionMeta | null;
  permissions: Permission[];
  defaultPanelLayout: PanelLayout;
  defaultTracePanelLayout: PlaygroundTracePanelLayout;
  isSidenavDocked?: boolean;
};

export type TaskFunctionDefinition = CreateExperimentTask & { id: string };

const NEVER_VISIBLE_COLUMNS = [...PLAYGROUND_EXPERIMENT_FIELDS];

const NO_DATASET_NEVER_VISIBLE_COLUMNS = [
  ...NEVER_VISIBLE_COLUMNS,
  GRID_INPUT_COLUMN_ID,
  GRID_EXPECTED_COLUMN_ID,
  GRID_METADATA_COLUMN_ID,
  GRID_TAGS_COLUMN_ID,
  "input",
  "expected",
  "metadata",
];

const INITIALLY_VISIBLE_COLUMNS = {
  [GRID_EXPECTED_COLUMN_ID]: false,
  [GRID_METADATA_COLUMN_ID]: false,
  [GRID_TAGS_COLUMN_ID]: true,
  expected: false,
  metadata: false,
  tags: false,
  scores: false,
};

const EMPTY_OUTPUT_NAMES: string[] = [];

export default function PromptPlayground({
  params,
  promptSessionMeta,
  ...rest
}: Props) {
  const orgName = decodeURIComponentPatched(params.org);
  const projectName = decodeURIComponentPatched(params.project);
  const playgroundName = decodeURIComponentPatched(params.playground);

  const org = useOrg();
  const copilotContext = usePlaygroundCopilotContext();

  const {
    allAvailableModels,
    configuredModelsByProvider,
    noConfiguredSecrets,
  } = useAvailableModels({ orgName });

  const promptSession = usePromptSession({
    orgName,
    projectName,
    playgroundName,
    promptSessionMetaServer: promptSessionMeta,
  });
  const { promptSessionId, promptSessionDML, playgroundBlocksDbState } =
    promptSession;

  const savePrompt = useCallback(
    async ({
      id,
      prompt_data,
      function_data,
      function_type,
    }: {
      id: string;
      prompt_data: PromptData;
      function_data: UIFunctionData;
      function_type?: FunctionType | null;
    }) => {
      if (!promptSessionId || !org.id) {
        throw new Error("Missing required inputs: promptSessionId or org.id");
      }

      return await promptSessionDML.update(
        [
          {
            id,
            prompt_session_id: promptSessionId,
            org_id: org.id,
          },
        ],
        Object.entries(prompt_data)
          .filter(([_k, v]) => !isEmpty(v))
          .map(([k, v]) => ({
            path: ["prompt_data", k],
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            newValue: v as unknown,
          }))
          .concat(
            Object.entries(function_data)
              .filter(([_k, v]) => !isEmpty(v))
              .map(([k, v]) => ({
                path: ["function_data", k],
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                newValue: v as unknown,
              })),
          )
          .concat(
            function_type
              ? [{ path: ["function_type"], newValue: function_type }]
              : [],
          ),
      );
    },
    [org.id, promptSessionDML, promptSessionId],
  );

  const saveSyncedPrompt = useCallback(
    ({ id, val }: { id: string; val: SyncedPlaygroundBlock }) => {
      copilotContext.visitPrompt({
        id: id,
        prompt_data: val.prompt_data,
        function_data: val.function_data,
        metadata: {},
      });
      return savePrompt({
        id,
        prompt_data: val.prompt_data,
        function_data: val.function_data,
        function_type: val.function_type,
      });
    },
    [copilotContext, savePrompt],
  );

  const isSidenavDocked = useIsSidenavDocked();

  return (
    <JotaiProvider>
      {/* Initialize SyncedPromptsProvider in a wrapping component so that it can be accessed
       in PromptPlaygroundInner - e.g. for getFunctionPlayX, which needs editor state. */}
      <SyncedPromptsProvider
        externalValue={playgroundBlocksDbState}
        save={saveSyncedPrompt}
        allAvailableModels={allAvailableModels}
      >
        <PromptPlaygroundInner
          allAvailableModels={allAvailableModels}
          configuredModelsByProvider={configuredModelsByProvider}
          noConfiguredSecrets={noConfiguredSecrets}
          promptSessionResult={promptSession}
          copilotContext={copilotContext}
          promptSessionMeta={promptSessionMeta}
          orgName={orgName}
          playgroundName={playgroundName}
          projectName={projectName}
          org={org}
          savePrompt={savePrompt}
          isSidenavDocked={isSidenavDocked}
          {...rest}
        />
      </SyncedPromptsProvider>
    </JotaiProvider>
  );
}

function PromptPlaygroundInner({
  promptSessionMeta: promptSessionMetaServer,
  permissions,
  defaultPanelLayout,
  defaultTracePanelLayout,
  promptSessionResult,
  allAvailableModels,
  configuredModelsByProvider,
  noConfiguredSecrets,
  orgName,
  playgroundName,
  projectName,
  org,
  savePrompt,
  isSidenavDocked,
  copilotContext,
}: Omit<Props, "params"> & {
  promptSessionResult: ReturnType<typeof usePromptSession>;
  allAvailableModels: ReturnType<
    typeof useAvailableModels
  >["allAvailableModels"];
  configuredModelsByProvider: ReturnType<
    typeof useAvailableModels
  >["configuredModelsByProvider"];
  noConfiguredSecrets: ReturnType<
    typeof useAvailableModels
  >["noConfiguredSecrets"];
  orgName: string;
  playgroundName: string;
  projectName: string;
  copilotContext: PlaygroundCopilotContext;
  org: OrgContextT;
  savePrompt: (args: {
    id: string;
    prompt_data: PromptData;
    function_data: UIFunctionData;
  }) => Promise<TransactionId | null>;
}) {
  const {
    promptSessionId,
    promptSessionMeta,
    refreshPromptSessions,
    promptSessionDbState,
    promptSessionTransactionIds,
    promptSessionDML,
    promptSessionReady,
    promptSessionScan,
    savingState,
    setSavingDataset,
    setSavingPromptSession,
    isPromptSessionLoadingInitial,
    promptSessionRecordsHaveLoaded,
  } = promptSessionResult;

  useAnalytics({
    page: promptSessionId
      ? {
          category: "prompt_session",
          props: { prompt_session_id: promptSessionId },
        }
      : undefined,
  });

  const {
    orgDatasets,
    mutateDatasets: refreshDatasets,
    projectSettings,
  } = useContext(ProjectContext);

  const { savedPromptMeta } = useSavedPromptMeta({
    promptSessionDbState,
  });

  const { sortedSyncedPromptsAtom_ROOT, deleteEditor_ROOT_NO_SAVE } =
    useSyncedPrompts();

  const deletePrompt = useCallback(
    async (id: string) => {
      deleteEditor_ROOT_NO_SAVE({ id });
      return deleteRow(promptSessionDML, {
        id,
      });
    },
    [deleteEditor_ROOT_NO_SAVE, promptSessionDML],
  );

  const [isEvalRunning, setIsEvalRunning] = useAtom(isRunningAtom);
  const [topLevelError, setTopLevelError] = useState<string | null>(null);

  const { promptDisplays, promptDisplayNamesById } = usePromptDisplayNames({
    promptSessionState: promptSessionDbState,
    savedPromptMeta,
  });

  const { data: savedPrompts, isLoading: areAllPromptsLoading } = useQueryFunc<
    typeof fetchPromptListing
  >({
    fName: "fetchPromptListing",
    args: { orgName },
  });

  const getFunctions = useAtomCallback(
    useCallback(
      async (
        get,
        _set,
        {
          useInlinePrompts,
        }: {
          useInlinePrompts: boolean;
        },
      ): Promise<(CreateExperimentTask & { id: string })[]> => {
        const promptData = get(sortedSyncedPromptsAtom_ROOT);
        if (!promptSessionId) {
          return [];
        }

        const validToolIds = new Set(savedPrompts?.map((p) => p.id));
        return promptData.map(
          ({ id, prompt_data, function_data, _xact_id, function_type }) => {
            return {
              id,
              ...makePlaygroundTaskFunctionId(
                {
                  id,
                  prompt_data,
                  function_data,
                  _xact_id,
                  function_type,
                },
                promptSessionId,
                useInlinePrompts,
                validToolIds,
              ),
              name:
                (function_data.type === "remote_eval"
                  ? function_data.eval_name
                  : undefined) ?? promptDisplayNamesById[id],
              metadata: prompt_data.options
                ? {
                    model: prompt_data.options.model,
                    ...prompt_data.options.params,
                  }
                : {},
              versionData: {
                id: prompt_data.origin?.prompt_id,
                version: prompt_data.origin?.prompt_version,
                projectId: prompt_data.origin?.project_id,
                objectType:
                  function_data.type === "prompt"
                    ? "project_prompts"
                    : "project_functions",
              },
            };
          },
        );
      },
      [
        sortedSyncedPromptsAtom_ROOT,
        promptSessionId,
        promptDisplayNamesById,
        savedPrompts,
      ],
    ),
  );

  const fullyCloseSidepanel = useFullyCloseSidePanel();

  const [promptSessionDataInitialized, setPromptSessionDataInitialized] =
    useState(false);
  const onPromptSessionDataInitialized = useCallback(() => {
    setPromptSessionDataInitialized(true);
  }, [setPromptSessionDataInitialized]);

  const {
    promptSession,
    promptSessionData,
    setPromptSessionData,
    hasLoaded: hasPromptSessionDataLoaded,
  } = usePromptSessionData({
    promptSessionScan,
    promptSessionReady,
    promptSessionDML,
    promptSessionMeta,
    onInitialized: onPromptSessionDataInitialized,
  });

  const datasetId = promptSessionData?.dataset_id ?? undefined;
  const currentDataset = orgDatasets?.find((d) => d.id === datasetId);
  const hasDataset = !!currentDataset;

  const playgroundSettings = useMemo(
    () => promptSessionData?.settings ?? DEFAULT_PLAYGROUND_SETTINGS,
    [promptSessionData?.settings],
  );
  const setPlaygroundSettings = useCallback(
    (settings: PlaygroundSettings) => {
      setPromptSessionData({ settings });
    },
    [setPromptSessionData],
  );

  const _savedScorers = useJSONMemo(promptSessionData?.scorers);
  const savedScorers = useMemo(() => _savedScorers ?? [], [_savedScorers]);
  const { functions } = useScorerFunctions({});
  const scoreNames = useMemo(() => {
    const rawScoreNames =
      savedScorers?.flatMap((s) =>
        s.type === "global" ? s.name : (functions[s.id]?.name ?? []),
      ) ?? [];

    // Deduplicate scorer names to prevent binder errors when multiple scorers have the same name
    // Use the same aliasing logic as deriveScoreFields to handle duplicates
    const scoreToAlias: Record<string, string> = {};
    const lowercasedCounter: Record<string, number> = {};
    const uniqueScoreNames: string[] = [];

    for (const score of rawScoreNames) {
      const alias = score.toLowerCase();
      const currentCount = lowercasedCounter[alias] ?? 0;
      const uniqueName =
        currentCount > 0 ? `${score} (${currentCount})` : score;
      scoreToAlias[score] = uniqueName;
      lowercasedCounter[alias] = currentCount + 1;
      uniqueScoreNames.push(uniqueName);
    }

    return uniqueScoreNames.toSorted();
  }, [savedScorers, functions]);

  const projectContext = useContext(ProjectContext);
  const projectId = projectContext.projectId;
  const viewParams: ViewParams | undefined = useMemo(
    () =>
      projectId
        ? {
            objectType: "project",
            objectId: projectId,
            viewType: "playground",
          }
        : undefined,
    [projectId],
  );
  const pageIdentifier = "playground" + promptSessionId;
  const { clauseChecker, setTopLevelFields } = useClauseChecker(
    "playground_logs",
    false,
  );
  const viewProps = useViewStates({
    viewParams,
    clauseChecker,
    pageIdentifier,
  });
  const { search, setSearch } = viewProps;

  const hasEverRun =
    Object.keys(promptSessionData?.generations ?? {}).length > 0;
  const isRunning = useAtomValue(isPlaygroundRunningAtom);
  const experimentSearch = useMemo(() => {
    return isRunning ? {} : search;
  }, [search, isRunning]);

  const datasetFilters = useMemo(() => {
    return (experimentSearch.filter ?? []).flatMap((clause) => {
      if (
        clause?.comparison ||
        (clause?.label && typeof clause?.label !== "string")
      ) {
        return [];
      }
      const result = checkBTQLClause({
        clause,
        table: "dataset",
        topLevelFields: [],
        customColumnsSchema: undefined,
      });
      if (result.type !== "checked") {
        return [];
      }

      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      return [{ ...clause, ...result.extraFields }] as [Clause<"filter">];
    });
  }, [experimentSearch.filter]);

  const {
    dml: datasetDML,
    jsonStructure,
    hasLoaded: hasLoadedDatasetRecords,
    addRow: addDatasetRow,
    loadedObject: loadedDataset,
    isLoading: isLoadingDataset,
    tableScanMeta,
    data: loadedDatasetData,
    error: datasetError,
  } = useMountedObject({
    objectType: "dataset",
    objectId: datasetId,
    objectIdReady: !!promptSessionDataInitialized,
    setSavingState: setSavingDataset,
    tableScanProps: {
      scoreNames,
      // default to unique rows in playground because rows with duplicate inputs
      // is very confusing for users
      comparisonKey: projectSettings?.comparison_key ?? "id",
      filters: datasetFilters,
    },
  });

  const [{ r: activeRowId }, setActiveRowAndSpan] = useActiveRowAndSpan();
  const onAddDatasetRow = useCallback(async () => {
    const id = await addDatasetRow(() => {});
    setActiveRowAndSpan({ r: id });
  }, [addDatasetRow, setActiveRowAndSpan]);

  const datasetRecordsHaveLoaded = useMemo(() => {
    return hasLoadedDatasetRecords || !promptSessionData?.dataset_id;
  }, [hasLoadedDatasetRecords, promptSessionData?.dataset_id]);

  const makePromptSessionData = useCallback((): PromptSession => {
    if (!promptSessionMeta) {
      throw new Error("Missing required input: promptSessionMeta");
    }
    return {
      id: newId(),
      prompt_session_id: promptSessionMeta.id,
      prompt_session_data: {
        dataset_id: promptSessionData?.dataset_id ?? null,
        scorers: [],
      },
    };
  }, [promptSessionData?.dataset_id, promptSessionMeta]);

  const updateDataset = useCallback(
    async (datasetId?: string) => {
      setPromptSessionData({ dataset_id: datasetId ?? null });
    },
    [setPromptSessionData],
  );

  const tableSelectionProps = useTableSelection();
  const { setSelectedRows, selectedRowsNumber } = tableSelectionProps;

  const updateSavedScorers = useEvent(
    async (scorers: SavedScorer[]): Promise<TransactionId | null> => {
      const existingPromptSession = promptSession ?? makePromptSessionData();

      const row = {
        ...existingPromptSession,
        prompt_session_data: {
          ...existingPromptSession.prompt_session_data,
          scorers,
        },
      };
      return await promptSessionDML.upsert([row]);
    },
  );

  const getCurrentSavedScorers = useEvent(() => savedScorers);

  // TODO: Remove this in a month or so after March 30, 2025. We just need it for now to bootstrap
  // the new settings.
  const [maxConcurrency_localStorage] = useEntityStorage({
    entityType: "playground",
    entityIdentifier: promptSessionId ?? "",
    key: "maxConcurrency",
    defaultValue: 10,
  });

  const maxConcurrency =
    playgroundSettings.maxConcurrency ??
    maxConcurrency_localStorage ??
    DEFAULT_MAX_CONCURRENCY;

  const [isNewDatasetDialogOpen, setIsNewDatasetDialogOpen] = useState(false);
  const onCreateDataset = useCallback(
    () => setIsNewDatasetDialogOpen(true),
    [setIsNewDatasetDialogOpen],
  );

  const [
    messagePartsWithLintErrorsByTaskId,
    setMessagePartsWithLintErrorsByTaskId,
  ] = useState<Record<string, Set<string>>>({});

  const onLint = useCallback((editorId: string, diagnostics: Diagnostic[]) => {
    const taskIdRegex = /prompt-([^-]+)-/;
    const taskIdMatch = editorId.match(taskIdRegex);
    const taskId = taskIdMatch?.[1];

    if (!taskId) return;

    setMessagePartsWithLintErrorsByTaskId((prev) => {
      const newMessagePartsWithLintErrors = new Set(prev[taskId]);
      if (diagnostics.length > 0) {
        newMessagePartsWithLintErrors.add(editorId);
      } else {
        newMessagePartsWithLintErrors.delete(editorId);
      }
      return {
        ...prev,
        [taskId]: newMessagePartsWithLintErrors,
      };
    });
  }, []);

  const hasLintErrors = Object.values(messagePartsWithLintErrorsByTaskId).some(
    (messageParts) => messageParts.size > 0,
  );
  // Don't show lint errors for missing variables if a dataset is selected,
  // since the prompts will still run and the editor will show the error still.
  const lintError =
    hasLintErrors && !hasDataset
      ? "There is no dataset selected for this playground. To use variables in prompts, select an existing dataset or create a new one."
      : undefined;

  const { modelSpecScan, allAvailableModelCosts } = useModelScan({
    id: promptSessionId ?? "",
  });

  const [generationsInitialized, setGenerationsInitialized] = useState(false);
  const onSetGenerations = useCallback(() => {
    setGenerationsInitialized(true);
  }, []);
  const generationIds = useJSONMemo(
    useMemo(() => {
      const generations = promptSessionData?.generations;
      const sortedPrompts = sortPrompts(promptSessionDbState);
      return promptDisplays.map(({ name, id }, i) => {
        const generationId = generations?.[sortedPrompts[i]?.id];
        return {
          id: generationId ?? id,
          name,
          hasRun: !!generationId,
        };
      });
    }, [promptSessionData?.generations, promptSessionDbState, promptDisplays]),
    onSetGenerations,
  );

  const selectedComparisonExperiments = useMemo(() => {
    return generationIds.slice(1);
  }, [generationIds]);

  const datasetTableParams = useMemo(
    () => ({
      scoreNames: tableScanMeta?.scoreNames ?? [],
      metadataFields: tableScanMeta?.metadataFields ?? [],
      metricNames: METRIC_FIELDS,
    }),
    [tableScanMeta],
  );
  const {
    experimentObject,
    experiment,
    primaryExperimentReady,
    comparisonExperimentData,
    projectedPaths,
    diffMode,
    setDiffMode,
    queryError,
    regressionFilters,
    setRegressionFilters,
    addRegressionFilter,
    clearRegressionFilters,
    hasErrorField,
    onDataLoaded,
    playgroundLogsReady,
    playgroundLogsLoaded,
    channels,
    hasRealtimeError,
  } = usePlaygroundLogsDataFromBackend({
    promptSessionId: promptSessionId ?? null,
    generationIds,
    playgroundName,
    modelSpecScan,
    search: experimentSearch,
    setSearch,
    loadedDataset,
    isEvalRunning,
    datasetTableParams,
    projectSettings,
    setPromptSessionData,
    activeRowId,
  });

  const { runPrompts, stop, generationIdToPromptId } = usePlayxState({
    promptSessionId: promptSessionId ?? null,
    promptToGeneration: promptSessionData?.generations ?? undefined,
    setPromptSessionData,
    getFunctions,
    datasetId: datasetId ?? null,
    numDatasetRecords: loadedDataset.count,
    savedScorers: savedScorers ?? [],
    scorerFunctions: functions,
    setTopLevelError,
    setIsRunning: setIsEvalRunning,
    maxConcurrency,
    strict: playgroundSettings.strict ?? undefined,
    extraMessages: playgroundSettings.extraMessages ?? undefined,
    channels,
    setSearch,
  });

  const scoreFields = useMemo(
    () => experiment?.scoreFields ?? [],
    [experiment?.scoreFields],
  );
  useScoreMetricsTopLevelFields({
    scoreFields,
    setTopLevelFields,
    include: true,
  });

  // https://github.com/bvaughn/react-resizable-panels?tab=readme-ov-file#how-can-i-use-persistent-layouts-with-ssr
  const onPanelLayout = useCallback(
    (sizes: number[]) => {
      const layoutCookie: PanelLayout = {
        ...defaultPanelLayout,
        sidebar: sizes[0],
      };
      document.cookie = `react-resizable-panels:playground-layout=${JSON.stringify(
        layoutCookie,
      )}; path=/`;
    },
    [defaultPanelLayout],
  );

  const { modal: uploadDatasetModal, open: openUploadDatasetDialog } =
    useUploadDatasetDialog({
      orgId: org.id,
      projectName,
      promptName: playgroundName,
      onSuccessfullyUploaded: async (dataset) => {
        refreshDatasets();
        await updateDataset(dataset.id);
      },
    });

  const { isReadOnly, isLoadingVisibility } = usePlaygroundVisibility({
    permissions,
    promptSessionMeta,
  });

  const [isInitialized, setIsInitalized] = useState(false);
  useEffect(() => {
    setIsInitalized(
      (initialized) =>
        initialized ||
        !(
          isLoadingVisibility ||
          isPromptSessionLoadingInitial ||
          !promptSessionRecordsHaveLoaded ||
          !hasPromptSessionDataLoaded ||
          !datasetRecordsHaveLoaded ||
          !loadedDataset.scan ||
          (!generationsInitialized && !promptSessionDataInitialized) ||
          (hasEverRun && !playgroundLogsReady) ||
          areAllPromptsLoading
        ),
    );
  }, [
    isLoadingVisibility,
    isPromptSessionLoadingInitial,
    promptSessionRecordsHaveLoaded,
    datasetRecordsHaveLoaded,
    hasPromptSessionDataLoaded,
    loadedDataset.scan,
    generationsInitialized,
    hasEverRun,
    playgroundLogsReady,
    promptSessionDataInitialized,
    areAllPromptsLoading,
  ]);

  const comparisonKey = useMemo(
    () => (relation: string) => `${relation}."comparison_key"`,
    [],
  );

  const {
    applySearch,
    tableGrouping,
    setTableGrouping,
    tableGroupingOptions,
    runExperimentAISearch,
  } = useTableControls({
    projectedPaths,
    viewProps,
    searchType: "playground_logs",
    clauseChecker,
    setSearch,
    experimentTable: experiment,
    customColumns: undefined,
    pageIdentifier,
  });

  const { summaryBreakdownData, comparisonKeyFilterClause } =
    useComparisonQueries({
      experiment: experiment,
      columnVisibility: viewProps.columnVisibility,
      comparisonExperimentData,
      regressionFilters,
      search: experimentSearch,
      tableGrouping,
      comparisonKey,
      scoreConfig: undefined,
      hasErrorField,
    });

  const defaultSortExprs = useMemo(() => {
    return ["e1.created DESC", "e1.id DESC"];
  }, []);

  const isLoopEnabled = useIsLoopEnabled();

  const experimentTableProps = useExperimentTableProps({
    experiment,
    projectedPaths,
    experimentScanRaw: experimentObject.scan ?? loadedDataset.scan,
    comparisonExperimentData,
    search: experimentSearch,
    diffMode,
    tableGrouping,
    setTableGrouping,
    comparisonKey,
    comparisonKeyFilterClause,
    addRegressionFilter,
    clearRegressionFilters,
    summaryBreakdownData,
    clauseChecker,
    setSearch,
    // TODO:
    // * Make this work. Should we star dataset rows or playground rows?
    // * Make the star column actually readonly instead of hiding it when it's enabled
    //enableStarColumn: !isReadOnly && !!datasetId,
    primaryExperimentReady,
    defaultSortExprs,
    viewProps,
    isPlayground: true,
    isLoopEnabled,
    isTableHidden: promptDisplays.length === 0,
  });

  const setDatasetId = useCallback(
    async (datasetId?: string) => {
      fullyCloseSidepanel();
      await setPromptSessionData({
        dataset_id: datasetId ?? null,
        generations: null,
      });
      stop();
      setSelectedRows({});
      setTopLevelError(null);
      setSearch({});
      setTableGrouping(GROUP_BY_NONE_VALUE);
    },
    [
      fullyCloseSidepanel,
      setPromptSessionData,
      setSelectedRows,
      stop,
      setTopLevelError,
      setSearch,
      setTableGrouping,
    ],
  );

  const initiallyVisibleColumns = useMemo(
    () => ({
      ...(viewProps.layout === "grid" ? INITIALLY_VISIBLE_COLUMNS : {}),
      ...DEFAULT_INITIALLY_VISIBLE_COLUMNS,
    }),
    [viewProps.layout],
  );
  const neverVisibleColumns = useMemo(
    () =>
      new Set([
        ...(datasetId
          ? NEVER_VISIBLE_COLUMNS
          : NO_DATASET_NEVER_VISIBLE_COLUMNS),
        ...DEFAULT_NEVER_VISIBLE_COLUMNS,
      ]),
    [datasetId],
  );
  const columnVisibility = useMemo(() => {
    return {
      ...initiallyVisibleColumns,
      ...viewProps.columnVisibility,
      ...Object.fromEntries(
        [...Array.from(neverVisibleColumns)].map((k) => [k, false]),
      ),
    };
  }, [
    viewProps.columnVisibility,
    initiallyVisibleColumns,
    neverVisibleColumns,
  ]);

  const getRowsForExport = experimentTableProps.getRowsForExport;
  const downloadJSON = useCallback(async () => {
    const rows = await getRowsForExport({
      withComparisons: true,
      objectType: "playground_logs",
      columnVisibility,
    });
    downloadAsJSON(playgroundName, rows ?? []);
  }, [getRowsForExport, playgroundName, columnVisibility]);
  const downloadCSV = useCallback(async () => {
    const rows = await getRowsForExport({
      withComparisons: true,
      objectType: "playground_logs",
      columnVisibility,
    });
    downloadAsCSV(playgroundName, rows ?? []);
  }, [getRowsForExport, playgroundName, columnVisibility]);

  const updateRow = useCallback<UpdateRowFn>(
    async (row, path, newValue) => {
      //const r = row;
      //r.experiment_id = experiment?.id;
      //return await dml.update([r], [{ path, newValue }]);
      return null;
    },
    //[dml, experiment?.id],
    [],
  );

  const updateDatasetRow = useCallback<UpdateRowFn>(
    async (row, path, newValue) => {
      return await datasetDML.update([row], [{ path, newValue }]);
    },
    [datasetDML],
  );

  const streamingContentProps = useMemo(
    () => ({
      generationIdToPromptId,
      havePlaygroundLogsLoaded: playgroundLogsLoaded,
      runPrompts,
    }),
    [generationIdToPromptId, playgroundLogsLoaded, runPrompts],
  );

  const [rowIds, setRowIds] = useState<RowId[]>([]);
  const { isTraceFullscreen } = useTraceFullscreen();
  const streamingState = useAtomValue(streamingStateAtom);

  const [isCreatingScorer, setIsCreatingScorer] = useState(false);
  const [selectedScorerId, setSelectedScorerId] = useState<string | null>(null);

  const selectedOriginIds = useAtomValue(
    useMemo(
      () =>
        selectAtom(
          sortedSyncedPromptsAtom_ROOT,
          (prompts) =>
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
            prompts
              .map((prompt) => prompt.prompt_data.origin?.prompt_id)
              .filter(Boolean) as string[],
          isEqual,
        ),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );

  const primaryName = promptDisplays[0]?.name;
  const vizQueryProps = useMemo(() => {
    const controls = promptSessionMeta && (
      <PlaygroundControls
        projectId={promptSessionMeta?.project_id}
        isReadOnly={isReadOnly}
        isLoopEnabled={isLoopEnabled}
        projectName={projectName}
        updateDataset={setDatasetId}
        setIsNewDatasetDialogOpen={setIsNewDatasetDialogOpen}
        openUploadDatasetDialog={openUploadDatasetDialog}
        datasetId={datasetId}
        savedScorers={savedScorers}
        updateSavedScorers={updateSavedScorers}
        jsonStructure={jsonStructure}
        copilotContext={copilotContext}
        datasets={orgDatasets}
        numDatasetRecords={loadedDataset.count}
        datasetCountLoading={isLoadingDataset}
        maxConcurrency={maxConcurrency}
        setMaxConcurrency={(maxConcurrency) => {
          setPlaygroundSettings(
            produce(playgroundSettings, (draft) => {
              draft.maxConcurrency = maxConcurrency;
            }),
          );
        }}
        strict={playgroundSettings.strict ?? undefined}
        setStrict={(strict) => {
          setPlaygroundSettings(
            produce(playgroundSettings, (draft) => {
              draft.strict = strict;
            }),
          );
        }}
        extraMessages={playgroundSettings.extraMessages ?? undefined}
        setExtraMessages={(extraMessages) => {
          setPlaygroundSettings(
            produce(playgroundSettings, (draft) => {
              draft.extraMessages = extraMessages;
            }),
          );
        }}
        isCreatingScorer={isCreatingScorer}
        setIsCreatingScorer={setIsCreatingScorer}
        selectedScorerId={selectedScorerId}
        setSelectedScorerId={setSelectedScorerId}
        numScorers={savedScorers.length}
        numTasks={promptDisplays.length}
        selectedOriginIds={selectedOriginIds}
        lintError={lintError}
        isDatasetForbidden={datasetError?.isForbidden}
      />
    );

    return {
      isPlayground: true,
      experimentName: primaryName,
      streamingContentProps,
      extraRightControls: (
        <>
          {datasetId && !isReadOnly && selectedRowsNumber === 0 ? (
            isLoopEnabled ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button Icon={Plus} size="xs" variant="ghost">
                    <span className="hidden @lg/controls:block">Row</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start">
                  <DropdownMenuGroup className="flex w-fit flex-col">
                    <DropdownMenuItem onClick={onAddDatasetRow}>
                      <Plus className="size-3" />
                      Blank row
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <GenerateRowsButton asDropdownMenuItem />
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button
                Icon={Plus}
                size="xs"
                variant="ghost"
                onClick={onAddDatasetRow}
              >
                <span className="hidden @lg/controls:block">Row</span>
              </Button>
            )
          ) : null}
          {controls}
        </>
      ),
      className: "pt-3",
      noRowsFoundLabel: datasetError?.isForbidden ? (
        <TableEmptyState
          className="border-0"
          Icon={ShieldAlert}
          label={
            <span>
              The dataset used by this playground with ID{" "}
              <span className="font-medium">{datasetId}</span> does not exist or
              you do not have access to it. Choose a different dataset or run
              without a dataset to continue.
            </span>
          }
        />
      ) : undefined,
      onDataLoaded,
    };
  }, [
    isLoopEnabled,
    primaryName,
    streamingContentProps,
    onAddDatasetRow,
    datasetId,
    isReadOnly,
    onDataLoaded,
    promptSessionMeta,
    projectName,
    setDatasetId,
    setIsNewDatasetDialogOpen,
    openUploadDatasetDialog,
    savedScorers,
    updateSavedScorers,
    copilotContext,
    orgDatasets,
    loadedDataset.count,
    isLoadingDataset,
    maxConcurrency,
    setPlaygroundSettings,
    playgroundSettings,
    isCreatingScorer,
    setIsCreatingScorer,
    selectedScorerId,
    setSelectedScorerId,
    promptDisplays.length,
    selectedOriginIds,
    lintError,
    jsonStructure,
    datasetError,
    selectedRowsNumber,
  ]);

  const [diffEnabled] = useState(false);
  const [fullscreenTaskIndex, setFullscreenTaskIndex] =
    usePlaygroundFullscreenTaskIndex();
  const isTaskFullscreen = fullscreenTaskIndex !== null;

  const { promptSheetIndex } = usePlaygroundPromptSheetIndexState();

  useEffect(() => {
    if (promptSheetIndex === null) {
      setFullscreenTaskIndex(null);
    }
  }, [promptSheetIndex, setFullscreenTaskIndex]);

  const addTaskButton = useMemo(
    () => (
      <PlaygroundAddTask
        numTasks={promptDisplays.length}
        projectName={projectName}
        disabledOriginIds={selectedOriginIds}
      >
        <div>
          <BasicTooltip tooltipContent="Add task">
            <Button
              Icon={Plus}
              variant="default"
              size="xs"
              className="rounded-full"
            />
          </BasicTooltip>
        </div>
      </PlaygroundAddTask>
    ),
    [promptDisplays.length, projectName, selectedOriginIds],
  );

  const promptExtensionsParams = useMemo(
    () => ({
      jsonStructure,
      outputNames: EMPTY_OUTPUT_NAMES,
      expandInputVariables: true,
      onCreateDataset: datasetId ? undefined : onCreateDataset,
      onLint,
    }),
    [jsonStructure, onCreateDataset, onLint, datasetId],
  );

  const firstDatasetRowData = useJSONMemo(
    useMemo(() => loadedDatasetData?.get(0)?.toJSON(), [loadedDatasetData]),
  );

  const beforeToolbarSlot = useMemo(
    () => (
      <PlaygroundPromptBlocks>
        <PromptBlocksSynced
          datasetId={datasetId}
          rowData={firstDatasetRowData}
          isReadOnly={isReadOnly}
          projectId={promptSessionMeta?.project_id}
          orgName={orgName}
          modelOptionsByProvider={configuredModelsByProvider}
          deletePrompt={deletePrompt}
          savedPromptMeta={savedPromptMeta}
          runPrompts={runPrompts}
          copilotContext={copilotContext}
          diffEnabled={diffEnabled}
          addPromptButton={addTaskButton}
          allAvailableModels={allAvailableModels}
          datasetJsonStructure={jsonStructure}
          promptExtensionsParams={promptExtensionsParams}
          extraMessagesPath={playgroundSettings.extraMessages}
        />
        {!isReadOnly && !isTaskFullscreen && promptDisplays.length > 0 && (
          <div className="flex flex-none items-center pl-0.5">
            {addTaskButton}
          </div>
        )}
      </PlaygroundPromptBlocks>
    ),
    [
      datasetId,
      firstDatasetRowData,
      isReadOnly,
      promptSessionMeta?.project_id,
      orgName,
      configuredModelsByProvider,
      deletePrompt,
      savedPromptMeta,
      runPrompts,
      copilotContext,
      diffEnabled,
      addTaskButton,
      allAvailableModels,
      jsonStructure,
      promptExtensionsParams,
      isTaskFullscreen,
      promptDisplays.length,
      playgroundSettings,
    ],
  );
  const afterToolbarSlot = useMemo(
    () =>
      savedScorers.length > 0 ? (
        <div className="ml-auto">
          <AppliedScorers
            savedScorers={savedScorers}
            scorerFunctions={functions}
            updateSavedScorers={updateSavedScorers}
            setSelectedScorerId={setSelectedScorerId}
          />
        </div>
      ) : null,
    [savedScorers, functions, updateSavedScorers, setSelectedScorerId],
  );

  const createPlaygroundDataset = useCallback(
    async (originalName: string | undefined) => {
      if (!org.id) {
        throw new Error(`Missing required inputs: orgId=${org.id}`);
      }
      let name = originalName ?? `Dataset ${orgDatasets.length + 1}`;
      if (orgDatasets.some((d) => d.name === name)) {
        name = newObjectName(originalName ?? "Dataset");
      }

      const { data } = await createDataset({
        orgId: org.id,
        projectName,
        datasetName: name,
      });
      await refreshDatasets();
      const datasetId = data?.dataset?.id;
      if (!datasetId) {
        throw new Error("Failed to create dataset");
      }
      setDatasetId(datasetId);
      return datasetId;
    },
    [org.id, orgDatasets, projectName, refreshDatasets, setDatasetId],
  );

  const completedPercentage = useAtomValue(useCompletionPercentageAtom());

  if (!promptSessionMetaServer) {
    return <AccessFailed objectType="Playground" objectName={playgroundName} />;
  }

  if (!isInitialized || !promptSessionMeta || !loadedDataset.scan) {
    return (
      <MainContentWrapper
        hideFooter
        className={cn(
          "flex items-center justify-center",
          HEIGHT_WITH_TOP_OFFSET,
        )}
      >
        <Loading />
      </MainContentWrapper>
    );
  }

  const createDatasetDialog = isNewDatasetDialogOpen ? (
    <OneLineTextPrompt
      title="Create dataset"
      fieldName="Name"
      onSubmit={async (name) => {
        await createPlaygroundDataset(name);
        setIsNewDatasetDialogOpen(false);
      }}
      onOpenChange={() => setIsNewDatasetDialogOpen(false)}
      open={isNewDatasetDialogOpen}
      defaultValue={`Dataset ${orgDatasets.length + 1}`}
      submitLabel="Create"
    />
  ) : null;

  const header = (
    <PlaygroundHeader
      className="-mx-3 px-3"
      playgroundName={playgroundName}
      projectName={projectName}
      savingState={savingState}
      refreshPromptSessions={refreshPromptSessions}
      promptSessionMeta={promptSessionMeta}
      isReadOnly={isReadOnly}
      downloadJSON={downloadJSON}
      downloadCSV={downloadCSV}
      isLoopEnabled={isLoopEnabled}
      setSavingPromptSession={setSavingPromptSession}
      promptSessionReady={promptSessionReady}
      promptSessionScan={promptSessionScan}
      diffMode={diffMode}
      setDiffMode={setDiffMode}
      diffModeOptionParams={{
        loading: !isInitialized || !experimentObject.schema,
        comparisonExperimentsCount: comparisonExperimentData.length,
        experimentSchema:
          datasetId && viewProps.layout === "grid"
            ? (experimentObject.schema ?? null)
            : null,
      }}
      selectedDatasetId={datasetId}
      getFunctions={getFunctions}
      numPrompts={promptDisplays.length}
      runPrompts={() => runPrompts({})}
      stop={stop}
      isRunning={isRunning}
      savedScorers={savedScorers}
      scorerFunctions={functions}
      isDatasetEmpty={loadedDataset.count === 0}
      maxConcurrency={maxConcurrency}
      strict={playgroundSettings.strict ?? undefined}
      extraMessages={playgroundSettings.extraMessages ?? undefined}
      hasEverRun={hasEverRun}
      lintError={lintError}
      noConfiguredSecrets={noConfiguredSecrets}
    />
  );

  const showRunStuckBanner =
    (!isEvalRunning && !!streamingState && hasRealtimeError) ||
    (streamingState === "eval-finished" &&
      completedPercentage &&
      completedPercentage <= 50);

  return (
    <PlaygroundProvider
      savePrompt={savePrompt}
      runPrompts={runPrompts}
      datasetId={datasetId}
      summaryBreakdownData={experimentTableProps.summaryBreakdownData}
      baseQuery={experimentTableProps.baseQuery}
      comparisonQueries={experimentTableProps.comparisonExperimentData}
      getFunctions={getFunctions}
      datasetDML={datasetDML}
      createDataset={createDataset}
      setDatasetId={setDatasetId}
      refreshDatasets={refreshDatasets}
      updateSavedScorers={updateSavedScorers}
      getCurrentSavedScorers={getCurrentSavedScorers}
      availableScorers={functions}
    >
      <motion.div layout className="flex w-full ">
        <MainContentWrapper
          className={cn(
            "flex flex-col overflow-hidden py-0 flex-1",
            HEIGHT_WITH_TOP_OFFSET,
          )}
          hideFooter
        >
          {header}
          <BodyWrapper
            isSidenavDocked={isSidenavDocked}
            outerClassName={cn("-mx-3", HEIGHT_WITH_DOUBLE_TOP_OFFSET)}
          >
            <ResizablePanelGroup
              autoSaveId="playgroundPanelLayout"
              direction="horizontal"
              className="flex w-full flex-auto"
              onLayout={onPanelLayout}
            >
              <ResizablePanel
                className={cn("relative flex flex-1 min-w-48", {
                  hidden: isTraceFullscreen,
                })}
                id="main"
                order={1}
                defaultSize={defaultPanelLayout.main}
              >
                <PromptTransactionIdProvider
                  promptSessionTransactionIds={promptSessionTransactionIds}
                  promptToGenerationId={
                    promptSessionData?.generations ?? undefined
                  }
                >
                  <div className="flex flex-1 flex-col overflow-auto">
                    {topLevelError ? (
                      <ErrorBanner
                        className={cn("mx-4 mb-2")}
                        onClose={() => setTopLevelError(null)}
                      >{`${topLevelError}`}</ErrorBanner>
                    ) : showRunStuckBanner ? (
                      <InfoBanner className="mx-4 mb-0">
                        Real-time updates have been paused for performance
                        reasons. Please refresh the page to see new results.
                      </InfoBanner>
                    ) : null}
                    <ExperimentTable
                      {...experimentTableProps}
                      beforeToolbarSlot={beforeToolbarSlot}
                      afterToolbarSlot={afterToolbarSlot}
                      isLoading={!hasLoadedDatasetRecords && !!datasetId}
                      regressionFilters={regressionFilters}
                      setRegressionFilters={setRegressionFilters}
                      tableGroupingOptions={tableGroupingOptions}
                      rowIds={rowIds}
                      setRowIds={setRowIds}
                      runExperimentAISearch={runExperimentAISearch}
                      dml={datasetDML}
                      isReadOnly={isReadOnly}
                      pageIdentifier={pageIdentifier}
                      viewParams={viewParams}
                      viewProps={viewProps}
                      queryError={queryError?.message ?? null}
                      //tableScansPaused={tableScansPaused}
                      updateRow={updateRow}
                      vizQueryProps={vizQueryProps}
                      selectedComparisonExperiments={
                        selectedComparisonExperiments
                      }
                      isPlayground
                      playgroundId={promptSessionMeta.id}
                      hasDataset={hasDataset}
                      tableSelectionProps={tableSelectionProps}
                      disableFilters={isRunning}
                      search={search}
                      initiallyVisibleColumns={initiallyVisibleColumns}
                      neverVisibleColumns={neverVisibleColumns}
                      exportName={playgroundName}
                      savedScorers={savedScorers}
                    />
                    {uploadDatasetModal}
                  </div>
                </PromptTransactionIdProvider>
                {uploadDatasetModal}
              </ResizablePanel>
            </ResizablePanelGroup>
          </BodyWrapper>
          <TraceSearchProvider
            activeRowId={makeRowIdPrimaryOrigin(activeRowId)}
          >
            <PlaygroundTraceSheet
              dataset={currentDataset}
              hasRun={hasEverRun}
              isReadOnly={isReadOnly}
              isRunning={isRunning}
              modelSpecScan={modelSpecScan}
              onAddRow={onAddDatasetRow}
              onApplySearch={applySearch}
              onDatasetComment={datasetDML.commentOn}
              onDatasetDeleteComment={datasetDML.deleteComment}
              onStop={stop}
              onRun={runPrompts}
              orgName={orgName}
              playgroundName={playgroundName}
              promptDisplays={promptDisplays}
              promptSessionId={promptSessionId}
              rowIds={rowIds}
              updateDatasetRow={updateDatasetRow}
              defaultPanelLayout={defaultTracePanelLayout}
              generationIds={generationIds}
              savingState={savingState}
              allAvailableModelCosts={allAvailableModelCosts}
            />
          </TraceSearchProvider>
        </MainContentWrapper>
        <DockedChatSpacer />
      </motion.div>
      {createDatasetDialog}
    </PlaygroundProvider>
  );
}

function PlaygroundProvider({
  children,
  ...optimizationProviderProps
}: {
  children: React.ReactNode;
} & OptimizationProviderProps) {
  const { projectSettings } = useContext(ProjectContext);
  return (
    <ProjectPromptsDropdownProvider>
      <RemoteEvalsProvider
        remoteEndpoints={useMemo(
          () => [
            DEFAULT_ENDPOINT_URL,
            ...(projectSettings?.remote_eval_sources ?? []).map(
              (source) => source.url,
            ),
          ],
          [projectSettings?.remote_eval_sources],
        )}
      >
        <AgentsDropdownProvider>
          <ScorersDropdownProvider>
            <OptimizationProvider {...optimizationProviderProps}>
              <GlobalChatProvider>{children}</GlobalChatProvider>
            </OptimizationProvider>
          </ScorersDropdownProvider>
        </AgentsDropdownProvider>
      </RemoteEvalsProvider>
    </ProjectPromptsDropdownProvider>
  );
}
