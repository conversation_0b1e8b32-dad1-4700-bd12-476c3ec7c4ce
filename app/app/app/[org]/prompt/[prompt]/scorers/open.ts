import { type SetStateAction, type Dispatch, useMemo } from "react";

import { type DurableObjects } from "#/utils/mutable-object";
import { type SavingState } from "#/ui/saving";
import { type z } from "zod";
import { promptSchema, type UIFunction } from "#/ui/prompts/schema";
import { type Expr } from "@braintrust/btql/parser";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useFunctions } from "#/ui/functions/use-functions";

export type SavedScorerObjects = DurableObjects & {
  functions: Record<string, UIFunction>;
};

const scorerFilter: Expr = {
  btql: "function_type='scorer'",
};

const originFilter: Expr = {
  btql: "origin.internal IS NULL or NOT origin.internal",
};

export function useScorerFunctions({
  setSavingState,
}: {
  setSavingState?: Dispatch<SetStateAction<SavingState>>;
}): SavedScorerObjects {
  const {
    flags: { customFunctions, functionOrigin },
  } = useFeatureFlags();
  const filters = useMemo(
    () =>
      [customFunctions ? scorerFilter : { btql: "false" }].concat(
        functionOrigin ? [originFilter] : [],
      ),
    [customFunctions, functionOrigin],
  );

  const promptObjects = useFunctions({
    setSavingState,
    functionObjectType: "scorer",
    filters,
  });

  const functions = useMemo(() => {
    return (promptObjects?.objects ?? [])
      .map((obj) => {
        const parsed = promptSchema.safeParse(obj);
        if (!parsed.success) {
          console.error("Invalid prompt object:", obj, parsed.error);
          return null;
        } else {
          return parsed.data;
        }
      })
      .filter((f: z.infer<typeof promptSchema> | null) => !!f);
  }, [promptObjects?.objects]);

  const functionsById = useMemo(() => {
    return Object.fromEntries(functions.map((func) => [func.id, func]));
  }, [functions]);

  return { functions: functionsById, ...promptObjects };
}
