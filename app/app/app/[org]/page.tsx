import ClientPage, { type Params } from "./clientpage";
import { getProjectSummary } from "./org-actions";
import { decodeURIComponentPatched } from "#/utils/url";
import { buildMetadata } from "#/app/metadata";
import { getObjectAclPermissions } from "#/utils/object-acl-permissions";
import { getOrganization } from "../actions";
import { type Permission } from "@braintrust/core/typespecs";

export default async function Page(props: { params: Promise<Params> }) {
  const params = await props.params;
  const org_name = decodeURIComponentPatched(params.org);
  const projectSummary = await getProjectSummary({ org_name });
  const org = await getOrganization({ org_name });

  let orgProjectPermissions: Permission[] = [];
  try {
    orgProjectPermissions =
      (await getObjectAclPermissions({
        objectType: "org_project",
        objectId: org?.id,
      })) ?? [];
  } catch (e) {
    console.error("Failed to get org project permissions", e);
  }

  return (
    <ClientPage
      params={params}
      projectSummary={projectSummary}
      orgProjectPermissions={orgProjectPermissions}
    />
  );
}

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params;
  const orgName = decodeURIComponentPatched(params.org);
  return buildMetadata({
    title: "Projects",
    sections: [orgName],
    description: orgName,
    relativeUrl: `/${params.org}`,
    ogTemplate: "product",
  });
}
