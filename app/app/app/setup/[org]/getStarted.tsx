"use client";

import { buttonVariants } from "#/ui/button";
import { AnimatePresence, motion } from "motion/react";
import { cn } from "#/utils/classnames";
import {
  ArrowLeft,
  FoldVertical,
  MousePointerClick,
  Terminal,
  Timer,
} from "lucide-react";
import { SyntaxHighlight } from "#/ui/syntax-highlighter";
import { TraceTree } from "#/ui/trace/trace-tree";
import { SpanTypeAttribute } from "@braintrust/core";
import { makePreviewSpan, type LoadedTrace, type Span } from "#/ui/trace/graph";
import { type traceCollapseState } from "#/ui/trace/trace";
import { useRef, useState, useCallback } from "react";

// Mock trace data
const createMockSpan = (
  id: string,
  name: string,
  type: SpanTypeAttribute,
  children: Span[] = [],
  start?: number,
  end?: number,
  tokens?: number,
  cost?: number,
): Span => {
  const metrics: Record<string, number> = {};
  if (start !== undefined) metrics.start = start;
  if (end !== undefined) metrics.end = end;
  if (tokens !== undefined) metrics.tokens = tokens;
  if (cost !== undefined) metrics.estimated_cost = cost;

  return {
    id,
    span_id: id,
    root_span_id: "root",
    parent_span_id: null,
    span_parents: [],
    data: {
      id,
      span_id: id,
      root_span_id: "root",
      _xact_id: "mock_xact_id",
      span_attributes: {
        name,
        type,
      },
      scores: {},
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
      metrics: Object.keys(metrics).length > 0 ? (metrics as any) : undefined,
      tags: [],
      input:
        type === SpanTypeAttribute.LLM
          ? "What is the capital of France?"
          : undefined,
      output:
        type === SpanTypeAttribute.LLM
          ? "The capital of France is Paris."
          : undefined,
    },
    scores: {},
    children,
  };
};

const mockSpan: Span = createMockSpan(
  "root",
  "eval",
  SpanTypeAttribute.EVAL,
  [
    createMockSpan("task-1", "Task", SpanTypeAttribute.TASK, [], 0, 0.46),
    createMockSpan(
      "chat_completion-1",
      "Chat Completion",
      SpanTypeAttribute.LLM,
      [],
      0,
      0.45,
      3589,
    ),
    createMockSpan("score-2", "Score", SpanTypeAttribute.SCORE, [], 0, 0.59),
  ],
  0,
  1.05,
);

const mockTrace: LoadedTrace = {
  root: mockSpan,
  spans: { [mockSpan.span_id]: mockSpan },
};

export const GetStarted = ({
  orgName,
  setActiveStep,
  setFlowType,
  hasApiSecrets,
  shouldReduceMotion,
}: {
  orgName: string;
  setActiveStep: (step: number) => void;
  setFlowType: (flowType: "ui" | "sdk") => void;
  hasApiSecrets: boolean;
  shouldReduceMotion: boolean;
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [_selectedSpan, setSelectedSpan] = useState<Span | null>(null);
  const selectedSpan = _selectedSpan ? makePreviewSpan(_selectedSpan) : null;
  const [collapseState, setCollapseState] = useState<traceCollapseState>({
    state: "expanded",
  });

  const handleCollapseStateChange = useCallback(
    (newState: traceCollapseState) => {
      setCollapseState(newState);
    },
    [],
  );

  return (
    <div className="flex w-full flex-col items-center">
      <div className="mb-6 flex w-full flex-col justify-center">
        <AnimatePresence mode="popLayout">
          <motion.h1
            layout
            initial={{ filter: "blur(4px)", opacity: 0 }}
            animate={{ filter: "blur(0px)", opacity: 1 }}
            exit={{ filter: "blur(4px)", opacity: 0 }}
            layoutId="title"
            className="font-planar mb-3 text-3xl font-medium"
          >
            Choose your path, evaluator
          </motion.h1>
        </AnimatePresence>
        <p className="text-base text-primary-600">
          Braintrust is the end-to-end platform for building world-class AI
          apps. Choose an option below to get started.
        </p>
      </div>
      <div className="grid w-full grid-cols-1 gap-2 md:grid-cols-2">
        <motion.button
          className={cn(
            buttonVariants({ variant: "border" }),
            "flex w-full flex-col gap-0 sm:w-full md:w-[300px] h-fit overflow-hidden border border-transparent hover:border-primary-800 group bg-cover bg-center p-4 rounded-xl bg-primary-100 justify-start",
          )}
          onClick={() => {
            setActiveStep(hasApiSecrets ? 2 : 1);
            setFlowType("ui");
          }}
        >
          <motion.div className="z-10 flex w-full flex-col items-start gap-4 text-pretty">
            <div className="flex max-h-[184px] w-full flex-col overflow-hidden rounded-md border border-primary-300/80">
              <div className="flex items-center justify-between border-b p-2 bg-primary-100 border-primary-200">
                <span className="text-xs font-medium">Trace</span>
                <div className="flex items-center gap-1">
                  <Timer className="m-1 size-3 text-primary-500" />
                  <FoldVertical className="m-1 size-3 text-primary-500" />
                  <ArrowLeft className="m-1 size-3 text-primary-500" />
                </div>
              </div>
              <div
                ref={containerRef}
                className="pointer-events-none min-h-0 flex-1 p-1 text-start bg-background"
              >
                <TraceTree
                  trace={mockTrace}
                  seen={new Set()}
                  comparisonClassName=""
                  selectedSpan={selectedSpan}
                  setSelectedSpan={setSelectedSpan}
                  showMetrics={true}
                  collapseState={collapseState}
                  onCollapseStateChange={handleCollapseStateChange}
                  totalDuration={1.5}
                  traceStart={0}
                  containerRef={containerRef}
                  hideCollapseButton
                />
              </div>
            </div>
            <div className="flex flex-col gap-1 text-start">
              <div className="font-planar flex items-center justify-start gap-2 text-xl font-medium">
                <MousePointerClick className="size-5" />
                UI
              </div>
              <span className="text-sm text-primary-600">
                Exploring LLM ideas, prompts, and evals via the UI
              </span>
            </div>
          </motion.div>
        </motion.button>
        <motion.button
          className={cn(
            buttonVariants({ variant: "border" }),
            "flex w-full flex-col gap-0 sm:w-full md:w-[300px] h-fit bg-primary-100 border border-transparent overflow-hidden p-0 hover:border-primary-800 group bg-cover bg-center justify-start p-4 pr-0 rounded-xl",
          )}
          onClick={() => {
            setActiveStep(hasApiSecrets ? 2 : 1);
            setFlowType("sdk");
          }}
        >
          <motion.div className="z-10 flex w-full  flex-col gap-4 text-pretty">
            <div className="max-h-[184px] w-full overflow-hidden rounded-l-md border border-r-0 border-primary-300/80">
              <SyntaxHighlight
                className="w-[1200px] p-4 text-start"
                language="typescript"
                content={`import { Eval } from "braintrust";
import { LevenshteinScorer } from "autoevals";

Eval("Say Hi Bot", {
  data: () => {
    return [
      {
        input: "Foo",
        expected: "Hi Foo",
      },
      {
        input: "Bar",
        expected: "Hello Bar",
      },
    ]; // Replace with your eval dataset
  },
  task: async (input) => {
    return "Hi " + input; // Replace with your LLM call
  },
  scores: [LevenshteinScorer],
});`}
              />
            </div>
            <div className="flex flex-col gap-1 text-start">
              <div className="font-planar flex items-center gap-2 text-xl font-medium">
                <Terminal className="size-5" />
                SDK
              </div>
              <span className="text-sm text-primary-600">
                I have an LLM app in production that I want to trace via the SDK
              </span>
            </div>
          </motion.div>
        </motion.button>
      </div>
    </div>
  );
};
