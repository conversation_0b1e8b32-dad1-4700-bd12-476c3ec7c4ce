"use client";

import { <PERSON><PERSON> } from "#/ui/button";
import { AISecretTypes } from "@braintrust/proxy/schema";
import { cn } from "#/utils/classnames";
import { toast } from "sonner";
import { AnimatePresence, motion } from "motion/react";
import {
  getProviderIcon,
  providerReadableName,
  testKey,
} from "#/app/app/[org]/settings/secrets/utils";
import { Label } from "#/ui/label";
import { Input } from "#/ui/input";
import { useCallback, useState } from "react";
import { modelsByProvider } from "#/ui/prompts/models";

export const AddProvider = ({
  orgName,
  onProviderAdded,
  onSkip,
  shouldReduceMotion,
}: {
  orgName: string;
  onProviderAdded: () => void;
  onSkip: () => void;
  shouldReduceMotion: boolean;
}) => {
  /*
  const defaultSecrets = DefaultSecretNames.map((name, i) => {
    return apiSecretsByName[name] || { name, id: i, type: AISecretTypes[name] };
  }); */
  const DefaultSecretNames = Object.keys(AISecretTypes);

  const [provider, setProvider] = useState<string>(DefaultSecretNames[0]);
  const [apiKey, setApiKey] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  /*   const filteredModelEndpointType = ModelEndpointType.filter(
      (type) => type !== "js",
    ); */

  const createKey = useCallback(
    async ({
      type,
      name,
      value,
      metadata,
    }: {
      type: string;
      name: string;
      value: string;
      metadata?: Record<string, unknown>;
    }) => {
      try {
        try {
          const resp = await testKey({
            provider: type,
            api_key: value,
            model: modelsByProvider[type][0].modelName,
          });
          if (!resp?.ok) {
            const responseBody = await resp?.json();
            const errorMessage =
              responseBody.error?.message ??
              responseBody.error ??
              responseBody.message ??
              responseBody.detail ??
              `${resp?.status}: ${resp?.statusText}`;
            toast.error("Error validating API key", {
              description: errorMessage,
            });
            return;
          }
        } catch (err) {
          toast.error("Error validating API key", {
            description: err instanceof Error ? err.message : "Unknown error",
          });
          return;
        }
        setIsSubmitting(true);
        const resp = await fetch(`/api/ai_secret/register`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            ai_secret_name: name,
            type,
            metadata,
            secret: value,
            update: true,
            org_name: orgName,
          }),
        });
        if (!resp.ok) {
          throw new Error(await resp.text());
        }
        toast.success("Secret saved successfully");
        onProviderAdded();
      } catch (error) {
        toast.error(`Failed to set secret`, {
          description: `${error}`,
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [orgName, onProviderAdded],
  );

  return (
    <div className="flex w-full flex-col">
      <AnimatePresence mode="popLayout">
        <motion.h1
          layoutId="title"
          initial={{ opacity: 0, filter: "blur(10px)" }}
          animate={{ opacity: 1, filter: "blur(0px)" }}
          exit={{ opacity: 0, filter: "blur(10px)" }}
          className="font-planar mb-2 text-3xl font-medium"
        >
          Add an AI provider
        </motion.h1>
      </AnimatePresence>
      <p className="mb-12 text-base text-primary-600">
        Select an AI provider and enter an API key. You&apos;ll be able to add
        custom providers and cloud providers later.
      </p>
      <motion.div
        layoutId="main-container-ui"
        layout="position"
        className="relative"
      >
        <div className="flex flex-col gap-8">
          <div className="flex w-full items-center gap-2">
            <Label className="flex w-full gap-2" htmlFor="provider">
              <span className="min-w-[80px] pt-2 text-sm font-medium">
                Provider
              </span>
              <div className="flex flex-1 flex-wrap gap-2">
                {DefaultSecretNames.map((name) => (
                  <Button
                    key={name}
                    className={cn(
                      "w-[80px] flex-none text-xs flex flex-col items-center",
                      {
                        "border-primary-500": provider === name,
                      },
                    )}
                    onClick={() => setProvider(name)}
                  >
                    {getProviderIcon(AISecretTypes[name], 32)}
                    {providerReadableName(AISecretTypes[name])}
                  </Button>
                ))}
              </div>
            </Label>
          </div>
          <fieldset className="flex w-full items-start gap-2">
            <Label
              className="flex h-10 min-w-[80px] items-center font-medium"
              htmlFor={`api-key`}
            >
              API Key
            </Label>
            <div className="flex w-full flex-col gap-1">
              <Input
                id={`api-key`}
                placeholder={`Enter ${providerReadableName(AISecretTypes[provider])} API key`}
                type="password"
                className="h-10 px-3 text-base"
                value={apiKey}
                onChange={(e) => {
                  setApiKey(e.target.value);
                }}
              />
              <div className="text-xs text-primary-500">
                This secret will be encrypted at rest using{" "}
                <a
                  href="https://en.wikipedia.org/wiki/Transparent_data_encryption"
                  target="_blank"
                  className="text-accent-600"
                >
                  Transparent Data Encryption
                </a>{" "}
                with a{" "}
                <a
                  href="https://libsodium.gitbook.io/doc/secret-key_cryptography/aead"
                  target="_blank"
                  className="text-accent-600"
                >
                  unique 256-bit key and nonce
                </a>
                .
              </div>
            </div>
          </fieldset>
        </div>
        <div className="flex justify-end gap-3 py-8">
          <Button
            variant="ghost"
            className="transition-all text-primary-500"
            onClick={onSkip}
          >
            I&apos;ll do this later
          </Button>
          <Button
            variant="primary"
            disabled={isSubmitting || !apiKey}
            onClick={() => {
              createKey({
                type: AISecretTypes[provider],
                name: provider,
                value: apiKey,
              });
            }}
            isLoading={isSubmitting}
          >
            Add provider
          </Button>
        </div>
      </motion.div>
    </div>
  );
};
