import { type Metadata } from "next";
import { Landing } from "../landing";
import { buildMetadata } from "#/app/metadata";
import { getServerAuthSession } from "#/utils/auth/server-session";

export default async function Page() {
  const session = await getServerAuthSession();

  return <Landing session={session} />;
}

export const metadata: Metadata = buildMetadata({
  skipTitleFormatting: true,
  title:
    "Braintrust - The evals and observability platform for building reliable AI agents",
});
