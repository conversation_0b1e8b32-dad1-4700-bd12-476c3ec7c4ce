import { getPage } from "../source";
import { proseMDXBaseClassName } from "#/ui/prose";
import { cn } from "#/utils/classnames";
import { notFound } from "next/navigation";
import defaultMdxComponents from "fumadocs-ui/mdx";
import { buildMetadata } from "#/app/metadata";
import { getServerAuthSession } from "#/utils/auth/server-session";
import Header from "#/ui/landing/header";
import { LandingFooterFlux } from "../../landing-footer";

export default async function LegalPage(props: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props.params;
  const page = getPage(params.slug);
  const session = await getServerAuthSession();

  if (!page) notFound();

  return (
    <>
      <div className="px-4 sm:px-8">
        <div className="mx-auto max-w-landing font-display text-black">
          <Header session={session} />
          <section className="-mx-4 mb-48 border-t px-4 py-16 border-black sm:-mx-8 sm:px-8">
            <div
              className={cn(
                proseMDXBaseClassName,
                "mb-44 prose-headings:font-display text-black prose-headings:font-medium max-w-screen-md prose-h1:text-6xl",
              )}
            >
              <page.data.body components={defaultMdxComponents} />
            </div>
          </section>
        </div>
      </div>
      <LandingFooterFlux />
    </>
  );
}

export async function generateMetadata(props: {
  params: Promise<{ slug?: string[] }>;
}) {
  const params = await props.params;
  const page = getPage(params.slug);

  if (!page) notFound();

  return buildMetadata({
    title: page.data.title,
    description: page.data.description,
    relativeUrl: `/legal/${params.slug?.join("/") ?? ""}`,
  });
}
