import { useRef, useEffect, useState } from "react";
import <PERSON>tie, { type LottieRefCurrentProps } from "lottie-react";

interface LottieAnimationProps {
  animationData: object; // Your Lottie JSON data
  loop?: boolean;
  autoplay?: boolean;
  className?: string;
  width?: number;
  height?: number;
  speed?: number;
  playOnlyWhenVisible?: boolean; // New prop to control visibility-based playback
}

export const LottieAnimation = ({
  animationData,
  loop = false,
  autoplay = true,
  className = "",
  width,
  height,
  speed = 1,
  playOnlyWhenVisible = true, // Default to true for better performance
}: LottieAnimationProps) => {
  const lottieRef = useRef<LottieRefCurrentProps>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasPlayedOnce, setHasPlayedOnce] = useState(false);

  useEffect(() => {
    if (lottieRef.current) {
      lottieRef.current.setSpeed(speed);
    }
  }, [speed]);

  // Intersection Observer to detect visibility
  useEffect(() => {
    if (!playOnlyWhenVisible) {
      setIsVisible(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasPlayedOnce) {
          setIsVisible(true);
          setHasPlayedOnce(true);
        }
      },
      {
        threshold: 0.25,
        rootMargin: "50px",
      },
    );

    const currentContainer = containerRef.current;
    if (currentContainer) {
      observer.observe(currentContainer);
    }

    return () => {
      if (currentContainer) {
        observer.unobserve(currentContainer);
      }
    };
  }, [playOnlyWhenVisible, hasPlayedOnce]);

  // Control animation playback based on visibility
  useEffect(() => {
    if (lottieRef.current && playOnlyWhenVisible) {
      if (isVisible && autoplay) {
        lottieRef.current.play();
      } else {
        lottieRef.current.pause();
      }
    }
  }, [isVisible, autoplay, playOnlyWhenVisible]);

  // Autoplay when animation data changes
  useEffect(() => {
    if (lottieRef.current && autoplay) {
      setHasPlayedOnce(false); // Reset so it can play again when visible
      if (!playOnlyWhenVisible || isVisible) {
        lottieRef.current.play();
      }
    }
  }, [animationData, autoplay, playOnlyWhenVisible, isVisible]);

  const style = {
    width: width || "100%",
    height: height || "auto",
  };

  return (
    <div ref={containerRef} className={className}>
      <Lottie
        lottieRef={lottieRef}
        animationData={animationData}
        loop={loop}
        autoplay={playOnlyWhenVisible ? false : autoplay} // Disable autoplay if using visibility control
        style={style}
      />
    </div>
  );
};
