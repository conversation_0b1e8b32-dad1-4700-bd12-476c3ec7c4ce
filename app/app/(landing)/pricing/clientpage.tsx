import Header from "#/ui/landing/header";
import { type BtSession } from "#/utils/auth/server-session";
import { cn } from "#/utils/classnames";
import { CustomerLogos } from "../customer-logos";
import { FAQ } from "./faq";
import { PricingSection } from "./pricing";
import { PricingCalculator } from "./pricing-calculator";

export const PricingClientPage = ({ session }: { session: BtSession }) => {
  return (
    <>
      <div className="overflow-x-hidden px-4 font-display bg-black text-white sm:px-8">
        <div className="mx-auto max-w-landing">
          <Header session={session} isInverted />
          <section className="mb-12 mt-24 lg:mb-0">
            <div className="mb-3 font-suisse text-xs uppercase tracking-wider">
              Get started
            </div>
            <h1 className="tracking-snug mb-5 text-balance text-6xl">
              Predictable pricing,
              <br />
              designed to scale
            </h1>
            <p className={cn("max-w-screen-md text-2xl text-balance")}>
              Start building for free, collaborate with your team, and ship
              quality AI products.
            </p>
          </section>
          <PricingSection />
          <div className="mb-6 font-suisse text-xs uppercase tracking-wider">
            Trusted by the best
          </div>
          <div className="z-10 -mx-8 overflow-hidden pb-8">
            <CustomerLogos />
          </div>
        </div>
      </div>
      <div className="overflow-x-hidden px-4 font-display bg-white text-black sm:px-8">
        <div className="mx-auto flex max-w-landing flex-col gap-12">
          <PricingCalculator />
          <FAQ />
        </div>
      </div>
    </>
  );
};
