import { PageTracker } from "#/ui/use-analytics";
import { type Metadata } from "next";
import { PricingClientPage } from "./clientpage";
import { buildMetadata } from "#/app/metadata";
import { getServerAuthSession } from "#/utils/auth/server-session";
import { LandingFooterFlux } from "../landing-footer";

export default async function PricingPage() {
  const session = await getServerAuthSession();

  return (
    <PageTracker category="pricing">
      <PricingClientPage session={session} />
      <LandingFooterFlux />
    </PageTracker>
  );
}

export const metadata: Metadata = buildMetadata({
  title: "Pricing",
  description:
    "Start building AI products for free with Braintrust. Transparent pricing for AI evaluation, monitoring, and observability. No hidden fees, pay as you scale.",
  relativeUrl: "/pricing",
  tags: ["pricing", "AI pricing", "LLM pricing", "free tier", "enterprise"],
});
