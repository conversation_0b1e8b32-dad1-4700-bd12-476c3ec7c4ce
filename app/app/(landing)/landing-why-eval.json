{"v": "5.9.0", "fr": 29.9700012207031, "ip": 0, "op": 90.0000036657751, "w": 1034, "h": 634, "nm": "Web_UI_WhyEval", "ddd": 0, "assets": [{"id": "comp_0", "nm": "Prompt Bar 2", "fr": 29.9700012207031, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON>pe Layer 10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [200, 75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON><PERSON><PERSON> Lay<PERSON> 10: Path 1 [1.1.0]", "np": 3, "mn": "ADBE Layer Control", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}, {"ty": 5, "nm": "<PERSON><PERSON><PERSON> Layer 10: Path 1 [1.1.1]", "np": 3, "mn": "ADBE Layer Control", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}, {"ty": 5, "nm": "<PERSON><PERSON><PERSON> Lay<PERSON> 10: Path 1 [1.1.2]", "np": 3, "mn": "ADBE Layer Control", "ix": 3, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}, {"ty": 5, "nm": "<PERSON><PERSON><PERSON> Lay<PERSON> 10: Path 1 [1.1.3]", "np": 3, "mn": "ADBE Layer Control", "ix": 4, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-112.887, 18.642], [-112.887, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 1, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-109.078, 18.642], [-109.078, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 2, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-105.269, 18.642], [-105.269, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-101.459, 18.642], [-101.459, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-97.65, 18.642], [-97.65, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-93.84, 18.642], [-93.84, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-90.031, 18.642], [-90.031, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-86.222, 18.642], [-86.222, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-82.413, 18.642], [-82.413, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-78.604, 18.642], [-78.604, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-74.794, 18.642], [-74.794, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-70.985, 18.642], [-70.985, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-67.175, 18.642], [-67.175, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 13, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-63.365, 18.642], [-63.365, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-59.556, 18.642], [-59.556, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-55.748, 18.642], [-55.748, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-51.939, 18.642], [-51.939, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-48.129, 18.642], [-48.129, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-44.319, 18.642], [-44.319, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 19, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-40.509, 18.642], [-40.509, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-36.701, 18.642], [-36.701, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-32.892, 18.642], [-32.892, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 22, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-29.083, 18.642], [-29.083, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-25.273, 18.642], [-25.273, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-21.463, 18.642], [-21.463, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-17.652, 18.642], [-17.652, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-13.842, 18.642], [-13.842, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 27, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-10.033, 18.642], [-10.033, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 28, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-6.224, 18.642], [-6.224, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [-2.415, 18.642], [-2.415, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [1.393, 18.642], [1.393, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [5.201, 18.642], [5.201, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [9.01, 18.642], [9.01, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [12.819, 18.642], [12.819, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 34, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [16.628, 18.642], [16.628, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 35, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [20.437, 18.642], [20.437, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 36, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [24.247, 18.642], [24.247, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [28.056, 18.642], [28.056, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [31.865, 18.642], [31.865, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [35.674, 18.642], [35.674, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 40, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [39.483, 18.642], [39.483, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 41, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [43.292, 18.642], [43.292, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [47.102, 18.642], [47.102, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [50.912, 18.642], [50.912, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [54.721, 18.642], [54.721, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [58.529, 18.642], [58.529, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 46, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [62.339, 18.642], [62.339, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 47, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [66.149, 18.642], [66.149, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 48, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [69.959, 18.642], [69.959, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 49, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [73.768, 18.642], [73.768, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 50, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [77.577, 18.642], [77.577, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 51, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [81.386, 18.642], [81.386, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 52, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [85.196, 18.642], [85.196, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [89.005, 18.642], [89.005, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 54, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [92.814, 18.642], [92.814, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [96.621, 18.642], [96.621, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 56, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [100.423, 18.642], [100.423, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [104.232, 18.642], [104.232, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 58, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [108.05, 18.642], [108.05, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 59, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [111.861, 18.642], [111.861, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 62, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 63, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 64, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 65, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 68, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 69, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 71, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 72, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 73, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 74, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 75, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 76, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 77, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 78, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 79, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 80, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 81, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 82, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 83, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 84, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 85, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 86, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 87, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 88, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 89, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 92, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 93, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 94, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 95, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 96, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 97, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 98, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 99, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 100, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 101, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 102, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 103, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 104, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 105, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 106, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 107, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 108, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 109, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 110, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 111, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 112, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 113, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 114, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 115, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 116, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 117, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 118, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 119, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 120, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 121, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 122, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 123, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 124, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 125, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 126, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 127, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 128, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 129, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 130, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 131, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 132, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 133, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}, {"t": 134.000005457932, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-112.887, 4.603], [-112.858, 18.642], [115.67, 18.642], [115.67, 4.603]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235294118, 0.549019607843, 0.952941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.388235294118, 0.549019607843, 0.952941176471, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "rd", "nm": "Round Corners 1", "r": {"a": 0, "k": 2, "ix": 1}, "ix": 2, "mn": "ADBE Vector Filter - RC", "hd": false}], "ip": 0, "op": 135.000005498663, "st": 0, "bm": 0}]}], "fonts": {"list": [{"fName": "SuisseIntlMono-Regular", "fFamily": "Suisse Int'l Mono", "fStyle": "Regular", "ascent": 72.4990844726562}, {"fName": "BraintrustDisplayV1-Regular", "fFamily": "Braintrust Display V1", "fStyle": "Regular", "ascent": 67.999267578125}]}, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [854.142, 317, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-175.433, 117.865], [-175.418, 283.913]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235294118, 0.549019607843, 0.952941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 52.0000021180034, "op": 90.0000036657751, "st": 52.0000021180034, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [517, 317, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-175.433, 117.865], [-175.418, 283.913]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235294118, 0.549019607843, 0.952941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 52.0000021180034, "op": 90.0000036657751, "st": 52.0000021180034, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Icon_Check Outlines 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [632.5, 97.11, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [12, 8.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[9.333, -6.417], [-3.499, 6.417], [-9.333, 0.583]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [11.997, 8.417], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 26.0000010590017, "op": 90.0000036657751, "st": 26.0000010590017, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Icon_Check Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [632.5, 155, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [12, 8.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[9.333, -6.417], [-3.499, 6.417], [-9.333, 0.583]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [11.997, 8.417], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 28.0000011404634, "op": 90.0000036657751, "st": 28.0000011404634, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Icon_X Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [632.874, 212.938, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9, 9, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-7, -7], [7, 7]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[7, -7], [-7, 7]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9, 9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 30.0000012219251, "op": 90.0000036657752, "st": 30.0000012219251, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 5, "nm": "Toxicity", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [678.309, 86.079, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-180.055, -108.754, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [0]}, {"t": 35.0000014255792, "s": [98]}], "ix": 1}}]}], "t": {"d": {"k": [{"s": {"sz": [362.104675292969, 216.853637695312], "ps": [-181.052337646484, -108.426818847656], "s": 32, "f": "BraintrustDisplayV1-Regular", "t": "67.3", "ca": 0, "j": 0, "tr": 10, "lh": 120, "ls": 0, "fc": [1, 1, 1]}, "t": 0}], "x": "var $bm_rt;\n$bm_rt = $bm_sum(effect('Slider Control')('Slider').value.toFixed(0), '% Toxicity');"}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 26.0000010590017, "op": 90.0000036657751, "st": -34.0000013848484, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 5, "nm": "Accuracy", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [678.309, 143.537, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-180.055, -108.754, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 28, "s": [0]}, {"t": 37.0000015070409, "s": [83]}], "ix": 1}}]}], "t": {"d": {"k": [{"s": {"sz": [362.104675292969, 216.853637695312], "ps": [-181.052337646484, -108.426818847656], "s": 32, "f": "BraintrustDisplayV1-Regular", "t": "67.3", "ca": 0, "j": 0, "tr": 10, "lh": 120, "ls": 0, "fc": [1, 1, 1]}, "t": 0}], "x": "var $bm_rt;\n$bm_rt = $bm_sum(effect('Slider Control')('Slider').value.toFixed(0), '% Accuracy');"}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 28.0000011404634, "op": 90.0000036657751, "st": -32.0000013033867, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 5, "nm": "Hallucination", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [678.309, 201.833, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-180.055, -108.754, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [0]}, {"t": 39.0000015885026, "s": [74]}], "ix": 1}}]}], "t": {"d": {"k": [{"s": {"sz": [362.104675292969, 216.853637695312], "ps": [-181.052337646484, -108.426818847656], "s": 32, "f": "BraintrustDisplayV1-Regular", "t": "67.3", "ca": 0, "j": 0, "tr": 10, "lh": 120, "ls": 0, "fc": [1, 1, 1]}, "t": 0}], "x": "var $bm_rt;\n$bm_rt = $bm_sum(effect('Slider Control')('Slider').value.toFixed(0), '% Hallucination');"}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 30.0000012219251, "op": 90.0000036657752, "st": -30.0000012219251, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 5, "nm": "PROMPT A", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 52, "s": [0]}, {"t": 57.0000023216576, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [32.883, 476.189, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-206.026, -97.635, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"sz": [674.274169921875, 195], "ps": [-208, -97.5], "s": 28, "f": "SuisseIntlMono-Regular", "t": "PROMPT A", "ca": 0, "j": 0, "tr": 0, "lh": 47, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": [{"nm": "Animator 1", "s": {"t": 0, "xe": {"a": 0, "k": 0, "ix": 7}, "ne": {"a": 0, "k": 0, "ix": 8}, "a": {"a": 0, "k": 100, "ix": 4}, "b": 3, "rn": 0, "sh": 1, "sm": {"a": 0, "k": 0, "ix": 6}, "s": {"a": 0, "k": 100, "ix": 1}, "r": 1}, "a": {"o": {"a": 0, "k": 0, "ix": 9}}}]}, "ip": 52.0000021180034, "op": 90.0000036657751, "st": 7.00000028511585, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Icon_Check Outlines 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46.929, 448.572, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [12, 8.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[9.333, -6.417], [-3.499, 6.417], [-9.333, 0.583]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [11.997, 8.417], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 52.0000021180034, "op": 90.0000036657751, "st": 52.0000021180034, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 0, "nm": "Prompt Bar 2", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [146.541, 521.344, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [200, 75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.23], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 52, "s": [0]}, {"t": 66.0000026882351, "s": [1.65]}], "ix": 1}}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52, "s": [0]}, {"t": 187.000007616666, "s": [4.505]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = effect('Slider Control')('Slider');"}, "w": 400, "h": 150, "ip": 52.0000021180034, "op": 187.000007616666, "st": 52.0000021180034, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 0, "nm": "Prompt Bar 2", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [146.541, 551.344, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [200, 75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.23], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 53, "s": [0]}, {"t": 67.0000027289659, "s": [2]}], "ix": 1}}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 53, "s": [0]}, {"t": 188.000007657397, "s": [4.505]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = effect('Slider Control')('Slider');"}, "w": 400, "h": 150, "ip": 53.0000021587343, "op": 188.000007657397, "st": 53.0000021587343, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 0, "nm": "Prompt Bar 2", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [146.541, 581.344, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [200, 75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.23], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 54, "s": [0]}, {"t": 68.0000027696968, "s": [1.4]}], "ix": 1}}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 54, "s": [0]}, {"t": 189.000007698128, "s": [4.505]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = effect('Slider Control')('Slider');"}, "w": 400, "h": 150, "ip": 54.0000021994651, "op": 189.000007698128, "st": 54.0000021994651, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 5, "nm": "PROMPT B", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 59, "s": [0]}, {"t": 64.0000026067734, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [372.087, 476.189, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-206.026, -97.635, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"sz": [674.274169921875, 195], "ps": [-208, -97.5], "s": 28, "f": "SuisseIntlMono-Regular", "t": "PROMPT B", "ca": 0, "j": 0, "tr": 0, "lh": 47, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": [{"nm": "Animator 1", "s": {"t": 0, "xe": {"a": 0, "k": 0, "ix": 7}, "ne": {"a": 0, "k": 0, "ix": 8}, "a": {"a": 0, "k": 100, "ix": 4}, "b": 3, "rn": 0, "sh": 1, "sm": {"a": 0, "k": 0, "ix": 6}, "s": {"a": 0, "k": 100, "ix": 1}, "r": 1}, "a": {"o": {"a": 0, "k": 0, "ix": 9}}}]}, "ip": 55.0000022401959, "op": 90.0000036657751, "st": 10.0000004073083, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Icon_X Outlines 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [384.523, 449.093, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9, 9, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-7, -7], [7, 7]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[7, -7], [-7, 7]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9, 9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 55.0000022401959, "op": 90.0000036657751, "st": 55.0000022401959, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 0, "nm": "Prompt Bar 2", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [484.541, 521.344, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [200, 75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.23], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 55, "s": [0]}, {"t": 69.0000028104276, "s": [1.65]}], "ix": 1}}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [0]}, {"t": 190.000007738859, "s": [4.505]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = effect('Slider Control')('Slider');"}, "w": 400, "h": 150, "ip": 55.0000022401959, "op": 190.000007738859, "st": 55.0000022401959, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 0, "nm": "Prompt Bar 2", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [484.541, 551.344, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [200, 75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.23], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 56, "s": [0]}, {"t": 70.0000028511585, "s": [2]}], "ix": 1}}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56, "s": [0]}, {"t": 191.000007779589, "s": [4.505]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = effect('Slider Control')('Slider');"}, "w": 400, "h": 150, "ip": 56.0000022809268, "op": 191.00000777959, "st": 56.0000022809268, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 0, "nm": "Prompt Bar 2", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [484.541, 581.344, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [200, 75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.23], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 57, "s": [0]}, {"t": 71.0000028918893, "s": [1.4]}], "ix": 1}}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 57, "s": [0]}, {"t": 192.00000782032, "s": [4.505]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = effect('Slider Control')('Slider');"}, "w": 400, "h": 150, "ip": 57.0000023216576, "op": 192.00000782032, "st": 57.0000023216576, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 5, "nm": "PROMPT C", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 66, "s": [0]}, {"t": 71.0000028918893, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [709.587, 476.189, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-206.026, -97.635, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"sz": [674.274169921875, 195], "ps": [-208, -97.5], "s": 28, "f": "SuisseIntlMono-Regular", "t": "PROMPT C", "ca": 0, "j": 0, "tr": 0, "lh": 47, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": [{"nm": "Animator 1", "s": {"t": 0, "xe": {"a": 0, "k": 0, "ix": 7}, "ne": {"a": 0, "k": 0, "ix": 8}, "a": {"a": 0, "k": 100, "ix": 4}, "b": 3, "rn": 0, "sh": 1, "sm": {"a": 0, "k": 0, "ix": 6}, "s": {"a": 0, "k": 100, "ix": 1}, "r": 1}, "a": {"o": {"a": 0, "k": 0, "ix": 9}}}]}, "ip": 58.0000023623884, "op": 90.0000036657751, "st": 13.0000005295009, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Icon_X Outlines 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [721.523, 449.093, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9, 9, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-7, -7], [7, 7]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[7, -7], [-7, 7]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [9, 9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 58.0000023623884, "op": 90.0000036657751, "st": 58.0000023623884, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 0, "nm": "Prompt Bar 2", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [821.541, 521.344, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [200, 75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.23], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 58, "s": [0]}, {"t": 72.0000029326201, "s": [1.65]}], "ix": 1}}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58, "s": [0]}, {"t": 193.000007861051, "s": [4.505]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = effect('Slider Control')('Slider');"}, "w": 400, "h": 150, "ip": 58.0000023623884, "op": 193.000007861051, "st": 58.0000023623884, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 0, "nm": "Prompt Bar 2", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [821.541, 551.344, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [200, 75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.23], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 59, "s": [0]}, {"t": 73.000002973351, "s": [2]}], "ix": 1}}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 59, "s": [0]}, {"t": 194.000007901782, "s": [4.505]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = effect('Slider Control')('Slider');"}, "w": 400, "h": 150, "ip": 59.0000024031193, "op": 194.000007901782, "st": 59.0000024031193, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 0, "nm": "Prompt Bar 2", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [821.541, 581.344, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [200, 75, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Slider Control", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.23], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 60, "s": [0]}, {"t": 74.0000030140818, "s": [1.4]}], "ix": 1}}]}], "tm": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"t": 195.000007942513, "s": [4.505]}], "ix": 2, "x": "var $bm_rt;\n$bm_rt = effect('Slider Control')('Slider');"}, "w": 400, "h": 150, "ip": 60.0000024438501, "op": 195.000007942513, "st": 60.0000024438501, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 5, "nm": "SCORES", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 22, "s": [0]}, {"t": 27.0000010997325, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [623.848, 32.411, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-206.026, -97.635, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"sz": [674.274169921875, 195], "ps": [-208, -97.5], "s": 28, "f": "SuisseIntlMono-Regular", "t": "SCORES", "ca": 0, "j": 0, "tr": 0, "lh": 47, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": [{"nm": "Animator 1", "s": {"t": 0, "xe": {"a": 0, "k": 0, "ix": 7}, "ne": {"a": 0, "k": 0, "ix": 8}, "a": {"a": 0, "k": 100, "ix": 4}, "b": 3, "rn": 0, "sh": 1, "sm": {"a": 0, "k": 0, "ix": 6}, "s": {"a": 0, "k": 100, "ix": 1}, "r": 1}, "a": {"o": {"a": 0, "k": 0, "ix": 9}}}]}, "ip": 22.0000008960784, "op": 90.0000036657751, "st": -23.0000009368092, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 5, "nm": "AI IN YOUR APP", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.55], "y": [1]}, "o": {"x": [0.45], "y": [0]}, "t": -20, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.16], "y": [0]}, "t": -9, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 5.00000020365417, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [35.336, 32.411, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-206.026, -97.635, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"sz": [674.274169921875, 195], "ps": [-208, -97.5], "s": 28, "f": "SuisseIntlMono-Regular", "t": "AI IN YOUR APP", "ca": 0, "j": 0, "tr": 0, "lh": 47, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": [{"nm": "Animator 1", "s": {"t": 0, "xe": {"a": 0, "k": 0, "ix": 7}, "ne": {"a": 0, "k": 0, "ix": 8}, "a": {"a": 0, "k": 100, "ix": 4}, "b": 3, "rn": 0, "sh": 1, "sm": {"a": 0, "k": 0, "ix": 6}, "s": {"a": 0, "k": 100, "ix": 1}, "r": 1}, "a": {"o": {"a": 0, "k": 0, "ix": 9}}}]}, "ip": 0, "op": 90.0000036657751, "st": -45.0000018328876, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "Logo_Grok Outlines", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 1, "s": [0]}, {"t": 11.0000004480392, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [335.155, 113.247, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [33.5, 32, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.165, -0.228], [-2.73, -11.756], [0, 0], [6.502, -6.51], [10.803, 7.393], [0, 0], [-5.421, 5.427], [2.725, 6.578], [1.087, -0.803], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-6.968, 9.608], [0, 0], [1.883, 8.002], [-8.197, 8.212], [0, 0], [6.895, 2.711], [5.422, -5.428], [-0.517, -1.248], [0, 0], [0, 0], [0, 0], [0, 0], [-0.165, 0.233]], "v": [[22.956, -29.391], [15.316, -3.329], [15.299, -3.346], [8.667, 20.04], [-23.452, 22.688], [-15.92, 19.197], [3.939, 15.29], [7.852, -4.622], [4.695, -5.38], [-17.474, 11.005], [14.281, -20.928], [14.281, -20.9], [23.452, -30.081]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [43.217, 30.081], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-7.037, 7.045], [-9, -3.708], [-1.354, -0.979], [0, 0], [4.892, -4.898], [-7.353, -7.422], [0, 0], [0, 0], [-1.661, 1.748], [2.838, 6.909]], "o": [[7.316, -7.319], [1.991, 0.74], [0, 0], [-6.997, -2.939], [-6.512, 6.515], [0, 0], [0, 0], [1.342, -1.851], [4.687, -4.933], [-3.8, -9.244]], "v": [[-7.774, -20.016], [19.316, -25.473], [24.396, -22.699], [16.881, -19.225], [-3.024, -15.266], [-3.58, 9.893], [-3.228, 10.25], [-24.396, 29.181], [-19.726, 23.833], [-13.223, 7.105]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [24.396, 34.819], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "Logo_Gemini_Symbol Outlines", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 1, "s": [0]}, {"t": 10.0000004073083, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [248.696, 113.48, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [150, 150, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [34.5, 34.5, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -13.835], [-5.167, 12.171], [-8.999, 8.999], [-12.167, 5.168], [-13.831, 0], [12.167, 5.167], [9.003, 8.999], [5.332, 12.167], [0, 13.835], [5.335, -12.167], [8.999, -8.999], [12.167, -5.332], [13.835, 0], [-12.167, -5.335], [-8.999, -8.999], [-5.164, -12.171]], "o": [[5.335, 12.167], [0, -13.835], [5.332, -12.167], [9.003, -9.003], [12.167, -5.335], [-13.83, 0], [-12.167, -5.332], [-8.999, -8.999], [-5.167, -12.167], [0, 13.835], [-5.164, 12.167], [-8.999, 8.999], [-12.167, 5.163], [13.835, 0], [12.167, 5.168], [8.999, 9.003], [0, 0]], "v": [[-8.001, 60.998], [0.002, 100.002], [7.751, 60.998], [29.249, 29.251], [61, 7.999], [100, 0], [61, -7.753], [29.249, -29.251], [7.751, -61.002], [0.002, -100.002], [-8.001, -61.002], [-29.249, -29.251], [-61, -7.749], [-100, 0], [-61, 7.999], [-29.249, 29.247], [-8.001, 61.002]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [150, 150], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "Logo_OpenAI_Symbol Outlines", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 1, "s": [0]}, {"t": 9.00000036657752, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [165.5, 113.585, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [21.5, 21.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [138.095, 138.095, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.543, -2.555], [0.01, -5.18], [4.573, -2.555], [4.573, 2.693], [0.028, 5.318], [-4.534, 2.693]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.011, 0.018], [0.003, 0.02], [0, 0], [-0.803, 1.26], [-1.354, 0.634], [-1.482, -0.19], [-1.149, -0.955], [0, 0], [0, 0], [0.121, -0.209], [0.002, -0.241]], "o": [[0, 0], [-0.018, -0.011], [-0.012, -0.017], [0, 0], [0.003, -1.495], [0.803, -1.261], [1.353, -0.633], [1.482, 0.19], [0, 0], [0, 0], [-0.208, 0.122], [-0.12, 0.209], [0, 0]], "v": [[-6.463, 1.583], [-9.998, -0.454], [-10.042, -0.497], [-10.064, -0.553], [-10.064, -10.296], [-8.83, -14.512], [-5.528, -17.413], [-1.187, -18.092], [2.842, -16.339], [2.594, -16.199], [-5.768, -11.372], [-6.27, -10.867], [-6.456, -10.18]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0.242, 0], [0.209, -0.122], [0, 0], [0, 0], [-0.009, 0.019], [-0.017, 0.012], [0, 0], [-1.493, -0.065], [-1.226, -0.855], [-0.577, -1.379], [0.253, -1.473]], "o": [[0, 0], [0, 0], [-0.208, -0.122], [-0.241, 0], [0, 0], [0, 0], [-0.002, -0.02], [0.008, -0.019], [0, 0], [1.296, -0.745], [1.494, 0.065], [1.226, 0.855], [0.577, 1.379], [0, 0]], "v": [[15.657, -5.654], [15.411, -5.801], [7.056, -10.671], [6.369, -10.857], [5.682, -10.671], [-4.534, -4.775], [-4.534, -8.857], [-4.524, -8.917], [-4.485, -8.965], [3.967, -13.842], [8.237, -14.883], [12.401, -13.475], [15.161, -10.053], [15.657, -5.687]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0.21, 0.116], [0, 0], [0, 0], [-0.021, 0], [-0.02, -0.011], [0, 0], [-0.692, -1.322], [0.124, -1.487], [0.902, -1.189], [1.399, -0.52], [0, 0], [0.125, 0.205]], "o": [[0, 0], [0, 0], [0.02, -0.011], [0.022, 0], [0, 0], [1.293, 0.745], [0.692, 1.323], [-0.124, 1.487], [-0.901, 1.189], [0, 0], [-0.007, -0.24], [-0.125, -0.205]], "v": [[12.14, -0.363], [1.932, -6.29], [5.458, -8.327], [5.52, -8.342], [5.583, -8.327], [14.035, -3.443], [17.075, -0.277], [17.945, 4.025], [16.374, 8.122], [12.852, 10.74], [12.852, 0.805], [12.65, 0.126]], "c": true}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.745, 0.626], [0, 0], [-0.119, -0.208], [-0.209, -0.118], [0, 0], [0, 0], [0.021, 0], [0.019, 0.01], [0, 0], [0.54, 2.014], [-1.039, 1.809]], "o": [[0.929, -1.603], [0, 0], [-0.004, 0.24], [0.12, 0.208], [0, 0], [0, 0], [-0.019, 0.01], [-0.022, 0], [0, 0], [-1.805, -1.045], [-0.541, -2.015], [0, 0]], "v": [[-16.905, -7.109], [-12.764, -10.561], [-12.764, -0.628], [-12.587, 0.057], [-12.085, 0.555], [-1.909, 6.428], [-5.444, 8.471], [-5.506, 8.486], [-5.568, 8.471], [-14.021, 3.596], [-17.683, -1.182], [-16.905, -7.152]], "c": true}, "ix": 2}, "nm": "Path 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[1.045, 1.806], [-0.319, 1.814], [0, 0], [0, 0], [-0.24, 0], [-0.207, 0.121], [0, 0], [0, 0], [0.009, -0.019], [0.017, -0.013], [0, 0], [2.014, 0.54]], "o": [[-0.923, -1.593], [0, 0], [0, 0], [0.207, 0.121], [0.24, 0], [0, 0], [0, 0], [-0.001, 0.021], [-0.01, 0.019], [0, 0], [-1.807, 1.04], [-2.014, -0.54]], "v": [[-14.7, 11.105], [-15.636, 5.831], [-15.388, 5.98], [-7.018, 10.807], [-6.335, 10.993], [-5.652, 10.807], [4.573, 4.911], [4.573, 8.993], [4.557, 9.053], [4.515, 9.102], [-3.955, 13.986], [-9.923, 14.768]], "c": true}, "ix": 2}, "nm": "Path 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[2.084, -0.005], [1.412, 1.179], [0, 0], [0, 0], [-0.121, 0.209], [-0.001, 0.241], [0, 0], [0, 0], [-0.011, -0.016], [-0.004, -0.019], [0, 0], [1.474, -1.473]], "o": [[-1.839, 0.003], [0, 0], [0, 0], [0.208, -0.122], [0.12, -0.208], [0, 0], [0, 0], [0.018, 0.009], [0.012, 0.016], [0, 0], [-0.005, 2.085], [-1.474, 1.474]], "v": [[2.205, 18.326], [-2.828, 16.506], [-2.581, 16.363], [5.782, 11.538], [6.283, 11.031], [6.468, 10.346], [6.468, -1.445], [10.003, 0.6], [10.047, 0.637], [10.07, 0.691], [10.07, 10.46], [7.761, 16.016]], "c": true}, "ix": 2}, "nm": "Path 7", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[1.495, 1.662], [0.156, 1.482], [0.753, 1.285], [2.047, 0.912], [2.192, -0.47], [1.77, 0.577], [1.822, -0.384], [1.386, -1.244], [0.579, -1.769], [1.206, -0.876], [0.738, -1.295], [-0.234, -2.233], [-1.507, -1.664], [-0.154, -1.482], [-0.752, -1.286], [-2.049, -0.912], [-2.193, 0.471], [-1.361, -0.608], [-1.49, 0.009], [-1.815, 1.323], [-0.686, 2.139], [-1.206, 0.875], [-0.738, 1.294], [0.235, 2.223]], "o": [[0.469, -1.414], [-0.155, -1.481], [-1.117, -1.944], [-2.048, -0.912], [-1.245, -1.385], [-1.771, -0.578], [-1.822, 0.385], [-1.386, 1.243], [-1.46, 0.3], [-1.207, 0.875], [-1.129, 1.941], [0.233, 2.232], [-0.472, 1.414], [0.154, 1.482], [1.118, 1.945], [2.049, 0.912], [0.989, 1.115], [1.36, 0.607], [2.247, 0.002], [1.816, -1.323], [1.46, -0.3], [1.206, -0.875], [1.115, -1.937], [-0.235, -2.223]], "v": [[17.994, -3.74], [18.469, -8.135], [17.091, -12.333], [12.222, -16.727], [5.698, -17.408], [1.096, -20.402], [-4.388, -20.697], [-9.284, -18.212], [-12.283, -13.613], [-16.329, -11.829], [-19.28, -8.538], [-20.657, -2.115], [-17.979, 3.882], [-18.462, 8.277], [-17.087, 12.477], [-12.213, 16.872], [-5.686, 17.551], [-2.12, 20.165], [2.205, 21.072], [8.456, 19.04], [12.306, 13.712], [16.351, 11.93], [19.301, 8.637], [20.656, 2.236]], "c": true}, "ix": 2}, "nm": "Path 8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.359, 21.426], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 10, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "Logo_Anthropic_Symbol Outlines", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 1, "s": [0]}, {"t": 8.00000032584668, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [80.5, 114.085, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [21.5, 14.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [152.74, 152.74, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.201, -13.982], [14.698, 13.982], [21.003, 13.982], [9.506, -13.982]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.506, -13.982], [-21.003, 13.982], [-14.575, 13.982], [-12.223, 8.111], [-0.196, 8.111], [2.154, 13.982], [8.582, 13.982], [-2.914, -13.982]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-6.21, -6.911], [-2.276, 2.917], [-10.144, 2.917]], "c": true}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [21.647, 14.262], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90.0000036657751, "st": 0, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "<PERSON><PERSON><PERSON> 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [502.5, 43, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-14.5, -274.125, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-68, -274.125], [39, -274.125]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235294118, 0.549019607843, 0.952941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.34], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 10, "s": [0]}, {"t": 20.0000008146167, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 10.0000004073083, "op": 90.0000036657751, "st": 10.0000004073083, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 4, "nm": "ArrowHead Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 90, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.34, "y": 1}, "o": {"x": 0.66, "y": 0}, "t": 10, "s": [447.448, 43, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 20.0000008146167, "s": [553.35, 43, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9, 5.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.469, 0.469], [-0.469, 0.468], [0, 0], [-0.469, -0.469], [0, 0], [0.468, -0.469], [0.469, 0.469], [0, 0], [0, 0], [0, 0]], "o": [[-0.469, -0.469], [0, 0], [0.468, -0.469], [0, 0], [0.468, 0.468], [-0.469, 0.469], [0, 0], [0, 0], [0, 0], [-0.468, 0.469]], "v": [[-8.484, 4.688], [-8.484, 2.993], [-0.847, -4.644], [0.848, -4.644], [8.485, 2.993], [8.485, 4.688], [6.789, 4.688], [0.303, -1.796], [-0.302, -1.796], [-6.789, 4.688]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.388235324037, 0.549019607843, 0.952941236309, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [8.999, 5.887], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 10.0000004073083, "op": 90.0000036657751, "st": 10.0000004073083, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [517, 317, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[468.024, -24.114], [468.024, 53.359]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235294118, 0.549019607843, 0.952941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.34], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 40, "s": [0]}, {"t": 50.0000020365418, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 40.0000016292334, "op": 90.0000036657751, "st": 40.0000016292334, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 4, "nm": "ArrowHead Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.34, "y": 1}, "o": {"x": 0.66, "y": 0}, "t": 40, "s": [984.974, 291.181, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 50.0000020365418, "s": [984.974, 367.516, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9, 5.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.469, 0.469], [-0.469, 0.468], [0, 0], [-0.469, -0.469], [0, 0], [0.468, -0.469], [0.469, 0.469], [0, 0], [0, 0], [0, 0]], "o": [[-0.469, -0.469], [0, 0], [0.468, -0.469], [0, 0], [0.468, 0.468], [-0.469, 0.469], [0, 0], [0, 0], [0, 0], [-0.468, 0.469]], "v": [[-8.484, 4.688], [-8.484, 2.993], [-0.847, -4.644], [0.848, -4.644], [8.485, 2.993], [8.485, 4.688], [6.789, 4.688], [0.303, -1.796], [-0.302, -1.796], [-6.789, 4.688]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.388235324037, 0.549019607843, 0.952941236309, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [8.999, 5.887], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 40.0000016292334, "op": 90.0000036657751, "st": 40.0000016292334, "bm": 0}, {"ddd": 0, "ind": 34, "ty": 4, "nm": "S<PERSON>pe Layer 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46.893, 287.175, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-470.25, -29.825, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-470.25, 52.25], [-470.25, -113.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235294118, 0.549019607843, 0.952941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0.8], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.34], "y": [1]}, "o": {"x": [0.66], "y": [0]}, "t": 70, "s": [0]}, {"t": 80.0000032584668, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 70.0000028511585, "op": 90.0000036657752, "st": 70.0000028511585, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 4, "nm": "ArrowHead Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.34, "y": 1}, "o": {"x": 0.66, "y": 0}, "t": 70, "s": [46.863, 372.327, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 80.0000032584668, "s": [46.863, 206.588, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9, 5.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.469, 0.469], [-0.469, 0.468], [0, 0], [-0.469, -0.469], [0, 0], [0.468, -0.469], [0.469, 0.469], [0, 0], [0, 0], [0, 0]], "o": [[-0.469, -0.469], [0, 0], [0.468, -0.469], [0, 0], [0.468, 0.468], [-0.469, 0.469], [0, 0], [0, 0], [0, 0], [-0.468, 0.469]], "v": [[-8.484, 4.688], [-8.484, 2.993], [-0.847, -4.644], [0.848, -4.644], [8.485, 2.993], [8.485, 4.688], [6.789, 4.688], [0.303, -1.796], [-0.302, -1.796], [-6.789, 4.688]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.388235324037, 0.549019607843, 0.952941236309, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [8.999, 5.887], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 70.0000028511585, "op": 90.0000036657752, "st": 70.0000028511585, "bm": 0}, {"ddd": 0, "ind": 36, "ty": 4, "nm": "Shape Layer 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.55], "y": [1]}, "o": {"x": [0.45], "y": [0]}, "t": -20, "s": [0]}, {"t": -9.00000036657752, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [208, 88.196, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [252.071, -482.804, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [414, 174], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 8, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235294118, 0.549019607843, 0.952941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [252.071, -482.804], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 90.0000036657751, "st": -20.0000008146167, "bm": 0}, {"ddd": 0, "ind": 37, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [810, 130.675, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [266.847, -440.325, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [446, 258], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 8, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235294118, 0.549019607843, 0.952941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [266.847, -440.325], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 22.0000008960784, "op": 90.0000036657751, "st": 2.00000008146167, "bm": 0}, {"ddd": 0, "ind": 38, "ty": 4, "nm": "Shape Layer 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [517, 518.095, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [266.847, -459.25, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [1032, 230], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 8, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.388235294118, 0.549019607843, 0.952941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [266.847, -459.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 52.0000021180034, "op": 90.0000036657751, "st": 32.0000013033867, "bm": 0}], "markers": [], "chars": [{"ch": "A", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[37.665, -73.013], [27.393, -73.013], [3.424, 0], [13.696, 0], [18.53, -15.61], [46.426, -15.61], [51.361, 0], [61.633, 0]], "c": true}, "ix": 2}, "nm": "A", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[32.529, -60.223], [43.607, -24.573], [21.35, -24.573]], "c": true}, "ix": 2}, "nm": "A", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "A", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "I", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[9.265, -63.95], [27.695, -63.95], [27.695, -9.064], [9.265, -9.064], [9.265, 0], [55.692, 0], [55.692, -9.064], [37.262, -9.064], [37.262, -63.95], [55.692, -63.95], [55.692, -73.013], [9.265, -73.013]], "c": true}, "ix": 2}, "nm": "I", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "I", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": " ", "size": 28, "style": "Regular", "w": 64.6, "data": {}, "fFamily": "Suisse Int'l Mono"}, {"ch": "N", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[56.094, 0], [56.094, -73.013], [46.527, -73.013], [46.527, -10.876], [23.163, -73.013], [8.862, -73.013], [8.862, 0], [18.43, 0], [18.43, -62.338], [41.794, 0]], "c": true}, "ix": 2}, "nm": "N", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "N", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "Y", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.529, -37.262], [13.696, -73.013], [2.518, -73.013], [27.695, -27.393], [27.695, 0], [37.262, 0], [37.262, -27.393], [62.439, -73.013], [51.26, -73.013]], "c": true}, "ix": 2}, "nm": "Y", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "Y", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "O", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[18.228, 0], [0, -22.055], [-18.228, 0], [0, 22.055]], "o": [[-18.228, 0], [0, 22.055], [18.228, 0], [0, -22.055]], "v": [[32.529, -74.323], [5.237, -36.557], [32.529, 1.309], [59.821, -36.557]], "c": true}, "ix": 2}, "nm": "O", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-12.186, 0], [0, -18.53], [12.186, 0], [0, 18.631]], "o": [[12.186, 0], [0, 18.53], [-12.186, 0], [0, -18.631]], "v": [[32.529, -65.46], [49.951, -36.557], [32.529, -7.553], [15.106, -36.557]], "c": true}, "ix": 2}, "nm": "O", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "O", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "U", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-18.228, 0], [0, 21.35], [0, 0], [0, 0], [0, 0], [12.488, 0], [0, 14.301], [0, 0]], "o": [[0, 0], [0, 22.76], [19.034, 0], [0, 0], [0, 0], [0, 0], [0, 14.502], [-12.387, 0], [0, 0], [0, 0]], "v": [[7.956, -73.013], [7.956, -29.709], [32.428, 1.309], [57.101, -30.515], [57.101, -73.013], [47.534, -73.013], [47.534, -29.709], [32.428, -7.553], [17.523, -28.601], [17.523, -73.013]], "c": true}, "ix": 2}, "nm": "U", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "U", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "R", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 11.38], [15.912, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.712, 0.101]], "o": [[0, 0], [0, 0], [8.057, -2.518], [0, -17.422], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.913, 0], [0, 0]], "v": [[48.441, 0], [59.317, 0], [43.808, -32.629], [57.001, -52.167], [28.802, -73.013], [10.977, -73.013], [10.977, 0], [20.544, 0], [20.544, -30.716], [28.802, -30.716], [34.14, -30.817]], "c": true}, "ix": 2}, "nm": "R", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -11.279], [9.668, 0], [0, 0], [0, 0]], "o": [[9.668, 0], [0, 11.279], [0, 0], [0, 0], [0, 0]], "v": [[29.91, -63.95], [47.131, -51.965], [29.91, -39.78], [20.544, -39.78], [20.544, -63.95]], "c": true}, "ix": 2}, "nm": "R", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "R", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "P", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 18.127], [16.013, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [16.013, 0], [0, -18.127], [0, 0]], "v": [[12.186, -73.013], [12.186, 0], [21.753, 0], [21.753, -28.098], [30.112, -28.098], [58.411, -50.555], [30.112, -73.013]], "c": true}, "ix": 2}, "nm": "P", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -11.884], [9.769, 0], [0, 0], [0, 0]], "o": [[9.769, 0], [0, 11.884], [0, 0], [0, 0], [0, 0]], "v": [[31.32, -63.95], [48.541, -50.555], [31.32, -37.161], [21.753, -37.161], [21.753, -63.95]], "c": true}, "ix": 2}, "nm": "P", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "P", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "S", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -7.654], [7.251, 0], [1.108, 9.769], [0, 0], [-14.905, 0], [0, 13.696], [11.682, 2.618], [0, 0], [0, 6.445], [-6.647, 0], [-1.108, -8.762], [0, 0], [11.279, 0], [0, -12.589], [-9.668, -2.115]], "o": [[8.057, 1.913], [0, 8.459], [-9.366, 0], [0, 0], [0.806, 14.502], [14.099, 0], [0, -13.998], [0, 0], [-5.942, -1.309], [0, -7.553], [6.647, 0], [0, 0], [-1.611, -14.502], [-13.092, 0], [0, 11.682], [0, 0]], "v": [[34.644, -32.529], [48.34, -19.739], [33.636, -7.553], [16.415, -22.76], [6.949, -22.76], [33.334, 1.309], [58.008, -20.444], [35.953, -42.096], [28.702, -43.707], [18.127, -54.584], [31.723, -65.46], [47.031, -53.778], [56.497, -53.778], [32.227, -74.323], [8.258, -54.282], [26.688, -34.442]], "c": true}, "ix": 2}, "nm": "S", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "S", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "C", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [9.265, 0], [0, 18.027], [-10.574, 0], [-1.309, -8.359], [0, 0], [12.589, 0], [0, -22.659], [-17.725, 0], [-2.518, 15.811]], "o": [[-1.309, 8.862], [-11.279, 0], [0, -18.933], [9.265, 0], [0, 0], [-2.417, -14.804], [-17.422, 0], [0, 22.156], [13.394, 0], [0, 0]], "v": [[48.642, -22.659], [33.435, -7.553], [16.516, -36.658], [33.234, -65.46], [48.239, -49.85], [58.008, -49.85], [33.536, -74.423], [6.647, -36.658], [32.831, 1.309], [58.209, -22.659]], "c": true}, "ix": 2}, "nm": "C", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "C", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "E", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[11.279, 0], [54.785, 0], [54.785, -9.064], [20.847, -9.064], [20.847, -33.133], [52.771, -33.133], [52.771, -42.197], [20.847, -42.197], [20.847, -63.95], [54.785, -63.95], [54.785, -73.013], [11.279, -73.013]], "c": true}, "ix": 2}, "nm": "E", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "E", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "M", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[37.363, -25.177], [48.038, -63.647], [48.038, 0], [57.605, 0], [57.605, -73.013], [42.599, -73.013], [32.529, -35.651], [22.458, -73.013], [7.452, -73.013], [7.452, 0], [17.02, 0], [17.02, -63.647], [27.695, -25.177]], "c": true}, "ix": 2}, "nm": "M", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "M", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "T", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[27.695, 0], [37.262, 0], [37.262, -63.95], [59.216, -63.95], [59.216, -73.013], [5.74, -73.013], [5.74, -63.95], [27.695, -63.95]], "c": true}, "ix": 2}, "nm": "T", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "T", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "B", "size": 28, "style": "Regular", "w": 64.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 15.509], [6.345, 2.921], [0, 6.949], [12.186, 0], [0, 0]], "o": [[0, 0], [13.193, 0], [0, -8.963], [4.733, -2.921], [0, -14.502], [0, 0], [0, 0]], "v": [[10.071, 0], [33.838, 0], [58.31, -21.652], [48.34, -38.974], [55.591, -54.987], [31.622, -73.013], [10.071, -73.013]], "c": true}, "ix": 2}, "nm": "B", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -8.56], [9.164, 0], [0, 0]], "o": [[0, 0], [9.97, 0], [0, 8.359], [0, 0], [0, 0]], "v": [[19.638, -33.435], [33.939, -33.435], [48.441, -21.552], [34.644, -9.064], [19.638, -9.064]], "c": true}, "ix": 2}, "nm": "B", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -7.553], [7.654, 0], [0, 0]], "o": [[0, 0], [7.352, 0], [0, 8.56], [0, 0], [0, 0]], "v": [[19.638, -63.95], [33.636, -63.95], [46.024, -53.577], [33.636, -41.995], [19.638, -41.995]], "c": true}, "ix": 2}, "nm": "B", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "B", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Suisse Int'l Mono"}, {"ch": "7", "size": 32, "style": "Regular", "w": 49.1, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[46.426, -68.481], [2.014, -68.481], [2.014, -59.921], [36.658, -59.921], [9.064, 0], [19.135, 0], [46.426, -60.425]], "c": true}, "ix": 2}, "nm": "7", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "7", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "4", "size": 32, "style": "Regular", "w": 57.4, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.028, -25.781], [4.028, -17.221], [35.751, -17.221], [35.751, 0], [45.016, 0], [45.016, -17.221], [54.785, -17.221], [54.785, -25.278], [45.016, -25.278], [45.016, -68.481], [31.522, -68.481]], "c": true}, "ix": 2}, "nm": "4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[14.099, -25.278], [35.751, -59.72], [35.751, -25.278]], "c": true}, "ix": 2}, "nm": "4", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "4", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "%", "size": 32, "style": "Regular", "w": 80, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -8.762], [0, 0], [-8.56, 0], [0, 8.762], [0, 0], [8.56, 0]], "o": [[0, 0], [0, 8.762], [8.56, 0], [0, 0], [0, -8.762], [-8.56, 0]], "v": [[7.05, -54.282], [7.05, -50.858], [22.156, -35.751], [37.262, -50.858], [37.262, -54.282], [22.156, -69.388]], "c": true}, "ix": 2}, "nm": "%", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[10.977, 0.101], [20.645, 0.101], [70.898, -68.481], [61.23, -68.481]], "c": true}, "ix": 2}, "nm": "%", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, -4.129], [0, 0], [3.424, 0], [0, 4.028], [0, 0], [-3.424, 0]], "o": [[0, 0], [0, 4.028], [-3.424, 0], [0, 0], [0, -4.129], [3.424, 0]], "v": [[29.205, -55.188], [29.205, -49.951], [22.156, -43.103], [15.106, -49.951], [15.106, -55.188], [22.156, -62.036]], "c": true}, "ix": 2}, "nm": "%", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, -8.762], [0, 0], [-8.56, 0], [0, 8.762], [0, 0], [8.56, 0]], "o": [[0, 0], [0, 8.762], [8.56, 0], [0, 0], [0, -8.762], [-8.56, 0]], "v": [[44.815, -17.523], [44.815, -14.099], [59.921, 1.007], [75.027, -14.099], [75.027, -17.523], [59.921, -32.73]], "c": true}, "ix": 2}, "nm": "%", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, -4.028], [0, 0], [3.424, 0], [0, 4.028], [0, 0], [-3.424, 0]], "o": [[0, 0], [0, 4.028], [-3.424, 0], [0, 0], [0, -4.028], [3.424, 0]], "v": [[66.971, -18.53], [66.971, -13.193], [59.921, -6.445], [52.972, -13.193], [52.972, -18.53], [59.921, -25.278]], "c": true}, "ix": 2}, "nm": "%", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "%", "np": 8, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": " ", "size": 32, "style": "Regular", "w": 21.5, "data": {}, "fFamily": "Braintrust Display V1"}, {"ch": "H", "size": 32, "style": "Regular", "w": 68.1, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.05, 0], [16.617, 0], [16.617, -31.723], [51.965, -31.723], [51.965, 0], [61.533, 0], [61.533, -68.481], [51.965, -68.481], [51.965, -40.283], [16.617, -40.283], [16.617, -68.481], [7.05, -68.481]], "c": true}, "ix": 2}, "nm": "H", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "H", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "a", "size": 32, "style": "Regular", "w": 51.3, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.525, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.373, 0], [1.108, -9.265], [0, 0], [-5.841, 0], [0, -4.633], [0, 0], [0, 0], [0, -8.057], [-9.164, 0], [-2.014, 4.733]], "o": [[0, 3.525], [0, 0], [0, 0], [0, 0], [0, 0], [0, -11.078], [-9.265, 0], [0, 0], [0.604, -4.028], [6.949, 0], [0, 0], [0, 0], [-14.502, 1.611], [0, 9.467], [7.452, 0], [0, 0]], "v": [[35.852, -6.042], [41.895, 0], [49.649, 0], [49.649, -8.057], [43.707, -8.057], [43.707, -34.241], [24.573, -51.361], [5.841, -36.456], [15.207, -36.456], [24.673, -43.808], [34.845, -35.449], [34.845, -31.622], [22.861, -30.313], [4.028, -13.898], [21.149, 1.108], [35.852, -7.251]], "c": true}, "ix": 2}, "nm": "a", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 5.035], [-5.942, 0.604], [0, 0], [0, 0], [6.647, 0]], "o": [[0, -4.935], [0, 0], [0, 0], [0, 8.258], [-5.539, 0]], "v": [[13.092, -14.603], [22.156, -22.961], [34.845, -24.271], [34.845, -20.444], [22.458, -6.445]], "c": true}, "ix": 2}, "nm": "a", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "a", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "l", "size": 32, "style": "Regular", "w": 21, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.042, 0], [15.106, 0], [15.106, -68.481], [6.042, -68.481]], "c": true}, "ix": 2}, "nm": "l", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "l", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "u", "size": 32, "style": "Regular", "w": 51.4, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-10.172, 0], [0, 14.301], [0, 0], [0, 0], [0, 0], [6.647, 0], [0, 7.956], [0, 0]], "o": [[0, 0], [0, 14.401], [10.172, 0], [0, 0], [0, 0], [0, 0], [0, 7.956], [-6.647, 0], [0, 0], [0, 0]], "v": [[5.539, -50.354], [5.539, -19.437], [25.882, 1.007], [46.225, -19.336], [46.225, -50.354], [37.161, -50.354], [37.161, -19.235], [25.882, -7.352], [14.603, -19.235], [14.603, -50.354]], "c": true}, "ix": 2}, "nm": "u", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "u", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "c", "size": 32, "style": "Regular", "w": 48.7, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-10.876, 0], [-2.921, 8.762], [0, 0], [6.143, 0], [0, 8.661], [0, 0], [-7.452, 0], [-2.719, -4.733], [0, 0], [10.776, 0], [0, -16.617], [0, 0]], "o": [[9.869, 0], [0, 0], [-2.719, 4.33], [-7.452, 0], [0, 0], [0, -8.862], [6.042, 0], [0, 0], [-2.719, -8.258], [-10.876, 0], [0, 0], [0.101, 16.214]], "v": [[25.681, 1.007], [46.024, -13.092], [37.061, -13.092], [25.58, -6.747], [13.092, -20.444], [13.092, -30.313], [25.479, -43.808], [36.96, -37.463], [46.024, -37.463], [25.681, -51.361], [4.028, -27.594], [4.028, -22.76]], "c": true}, "ix": 2}, "nm": "c", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "c", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "i", "size": 32, "style": "Regular", "w": 21.9, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.042, -58.612], [16.013, -58.612], [16.013, -68.481], [6.042, -68.481]], "c": true}, "ix": 2}, "nm": "i", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.445, 0], [15.509, 0], [15.509, -50.354], [6.445, -50.354]], "c": true}, "ix": 2}, "nm": "i", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "i", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "n", "size": 32, "style": "Regular", "w": 52.4, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-6.848, 0], [0, -6.445], [0, 0], [0, 0], [0, 0], [10.071, 0], [2.417, -3.323], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -6.445], [6.546, 0], [0, 0], [0, 0], [0, 0], [0, -10.474], [-7.654, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.042, 0], [15.106, 0], [15.106, -32.025], [26.889, -43.405], [37.665, -32.025], [37.665, 0], [46.729, 0], [46.729, -33.234], [29.205, -51.361], [15.106, -44.714], [15.106, -50.354], [6.042, -50.354]], "c": true}, "ix": 2}, "nm": "n", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "n", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "t", "size": 32, "style": "Regular", "w": 30.9, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-6.244, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 5.237], [0, 0]], "v": [[27.896, 0], [27.896, -8.057], [18.228, -8.057], [18.228, -42.297], [28.601, -42.297], [28.601, -50.354], [18.228, -50.354], [18.228, -64.453], [9.668, -64.453], [9.668, -50.354], [1.007, -50.354], [1.007, -42.297], [9.164, -42.297], [9.164, -8.963], [18.228, 0]], "c": true}, "ix": 2}, "nm": "t", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "t", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "o", "size": 32, "style": "Regular", "w": 52.1, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -13.696], [0, 0], [-12.589, 0], [0, 13.696], [0, 0], [12.589, 0]], "o": [[0, 0], [0, 13.696], [12.589, 0], [0, 0], [0, -13.696], [-12.589, 0]], "v": [[4.028, -27.795], [4.028, -22.559], [26.184, 1.007], [48.441, -22.559], [48.441, -27.795], [26.184, -51.361]], "c": true}, "ix": 2}, "nm": "o", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, -9.869], [0, 0], [7.352, 0], [0, 9.869], [0, 0], [-7.251, 0]], "o": [[0, 0], [0, 9.869], [-7.251, 0], [0, 0], [0, -9.869], [7.352, 0]], "v": [[39.276, -29.407], [39.276, -20.847], [26.184, -6.848], [13.193, -20.847], [13.193, -29.407], [26.184, -43.707]], "c": true}, "ix": 2}, "nm": "o", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "o", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "8", "size": 32, "style": "Regular", "w": 54.1, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -8.762], [-11.682, 0], [0, 13.193], [5.338, 2.115], [0, 7.05], [10.474, 0], [0, -12.689], [-4.834, -2.417]], "o": [[0, 12.991], [11.682, 0], [0, -9.064], [5.237, -2.518], [0, -12.891], [-10.474, 0], [0, 7.251], [-4.935, 1.813]], "v": [[4.532, -19.235], [27.393, 1.007], [50.455, -19.336], [40.182, -35.55], [48.34, -50.153], [27.292, -69.489], [6.445, -50.354], [14.804, -35.55]], "c": true}, "ix": 2}, "nm": "8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 6.949], [-6.143, 0], [0, -6.848], [6.244, 0]], "o": [[0, -6.848], [6.244, 0], [0, 6.949], [-6.143, 0]], "v": [[15.811, -50.052], [27.393, -60.928], [39.175, -50.052], [27.393, -39.175]], "c": true}, "ix": 2}, "nm": "8", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[-8.258, 0], [0, -7.251], [7.15, 0], [0, 7.755]], "o": [[8.661, 0], [0, 8.057], [-6.949, 0], [0, -7.251]], "v": [[27.393, -31.723], [41.391, -19.638], [27.292, -7.352], [13.596, -19.235]], "c": true}, "ix": 2}, "nm": "8", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "8", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "3", "size": 32, "style": "Regular", "w": 56.3, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 13.898], [6.244, 2.921], [0, 7.654], [11.078, 0], [1.41, -11.279], [0, 0], [-6.445, 0], [0, -7.352], [6.848, 0], [0, 0], [0, 0], [0, 0], [0, -8.258], [7.05, 0], [1.511, 7.654], [0, 0], [-11.783, 0]], "o": [[0, -8.459], [5.438, -2.618], [0, -12.286], [-11.682, 0], [0, 0], [2.014, -7.855], [6.445, 0], [0, 7.15], [0, 0], [0, 0], [0, 0], [7.05, 0], [0, 8.157], [-7.05, 0], [0, 0], [2.014, 13.293], [11.783, 0]], "v": [[52.167, -19.839], [40.485, -36.255], [49.85, -50.656], [28.4, -69.489], [5.74, -50.354], [15.308, -50.354], [28.4, -60.928], [40.686, -50.555], [28.601, -40.082], [21.35, -40.082], [21.35, -31.421], [28.5, -31.421], [42.801, -19.739], [28.802, -7.553], [13.596, -19.034], [4.028, -19.034], [28.702, 1.007]], "c": true}, "ix": 2}, "nm": "3", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "3", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "A", "size": 32, "style": "Regular", "w": 63.5, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[38.37, -68.481], [25.58, -68.481], [2.014, 0], [11.682, 0], [17.725, -16.617], [46.225, -16.617], [52.267, 0], [61.935, 0]], "c": true}, "ix": 2}, "nm": "A", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[20.444, -24.673], [31.924, -60.123], [43.506, -24.673]], "c": true}, "ix": 2}, "nm": "A", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "A", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "r", "size": 32, "style": "Regular", "w": 32.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -4.431]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-4.129, 0], [0, 0]], "v": [[6.042, 0], [15.106, 0], [15.106, -41.794], [30.817, -41.794], [30.817, -50.354], [12.387, -50.354], [6.042, -42.801]], "c": true}, "ix": 2}, "nm": "r", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "r", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "y", "size": 32, "style": "Regular", "w": 48.5, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-1.913, 5.136]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [5.539, 0], [0, 0]], "v": [[47.333, -50.354], [37.564, -50.354], [24.976, -13.092], [11.581, -50.354], [1.511, -50.354], [20.544, -1.813], [16.516, 9.064], [6.647, 9.064], [6.647, 17.12], [14.099, 17.12], [24.976, 9.567]], "c": true}, "ix": 2}, "nm": "y", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "y", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "9", "size": 32, "style": "Regular", "w": 53.8, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-12.387, 0], [0, 14.401], [0, 0], [15.61, 0], [0, -11.078], [-14.2, 0], [-2.82, 3.525], [0, 0], [9.366, 0], [1.511, 3.625], [0, 0]], "o": [[15.71, 0], [0, 0], [0, -14.301], [-14.603, 0], [0, 10.977], [6.647, 0], [0, 0], [0, 11.38], [-6.747, 0], [0, 0], [2.518, 7.05]], "v": [[26.587, 1.007], [49.649, -24.774], [49.649, -44.513], [26.486, -69.489], [4.028, -47.031], [25.983, -24.673], [40.585, -31.32], [40.585, -24.673], [26.788, -7.553], [14.502, -14.703], [4.431, -14.703]], "c": true}, "ix": 2}, "nm": "9", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[9.668, 0], [0, 6.949], [-9.869, 0], [0, -7.05]], "o": [[-9.769, 0], [0, -6.949], [9.366, 0], [0, 6.848]], "v": [[26.889, -33.133], [12.891, -47.131], [26.99, -60.928], [40.585, -46.93]], "c": true}, "ix": 2}, "nm": "9", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "9", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "T", "size": 32, "style": "Regular", "w": 55.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[23.969, 0], [33.032, 0], [33.032, -59.921], [53.979, -59.921], [53.979, -68.481], [2.014, -68.481], [2.014, -59.921], [23.969, -59.921]], "c": true}, "ix": 2}, "nm": "T", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "T", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}, {"ch": "x", "size": 32, "style": "Regular", "w": 49.9, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.014, -0.101], [11.581, 0], [24.875, -19.739], [38.068, 0], [48.239, 0], [30.716, -25.58], [47.635, -50.354], [37.967, -50.455], [25.278, -31.119], [12.689, -50.354], [2.316, -50.354], [19.336, -25.479]], "c": true}, "ix": 2}, "nm": "x", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "x", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "Braintrust Display V1"}]}