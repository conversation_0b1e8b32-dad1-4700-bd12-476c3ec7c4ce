"use client";

import { useIsClient } from "#/utils/use-is-client";
import {
  Airtable,
  Coda,
  Coursera,
  Dia,
  Dropbox,
  // Gong,
  Granola,
  Instacart,
  Notion,
  Perplexity,
  Ramp,
  Replit,
  // Robinhood,
  Stripe,
  Vercel,
  Zapier,
} from "./logos";
import Marquee from "react-fast-marquee";

const Logos = () => (
  <div className="mx-8 flex items-center gap-16">
    {/* <Robinhood className="h-7 flex-none" /> */}
    <Airtable isMono className="flex-none" />
    <Notion isMono className="flex-none" />
    <Instacart isMono className="flex-none" />
    <Stripe isMono className="w-24 flex-none" />
    <Zapier isMono className="flex-none" />
    <Vercel isMono className="flex-none" />
    <Dropbox className="h-6 flex-none" />
    <Perplexity className="h-9 flex-none" />
    <Ramp className="flex-none" />
    <Coursera className="h-11 flex-none" />
    {/* <Gong className="h-8 flex-none" /> */}
    <Replit isMono className="flex-none" />
    <Coda className="flex-none" />
    <Granola className="h-9 flex-none" />
    <Dia className="h-6 flex-none" />
  </div>
);

export const CustomerLogos = () => {
  const isClient = useIsClient();

  if (!isClient) {
    return (
      <div className="flex items-center gap-16 overflow-hidden whitespace-nowrap">
        <Logos />
      </div>
    );
  }

  return (
    <Marquee className="whitespace-nowrap" speed={32} autoFill gradient={false}>
      <Logos />
    </Marquee>
  );
};
