import { cn } from "#/utils/classnames";

export const LandingFeature = ({
  className,
  iconSlot,
  title,
  description,
  titleClassName,
  descriptionClassName,
  children,
}: {
  className?: string;
  iconSlot?: React.ReactNode;
  title?: string;
  titleClassName?: string;
  description: string;
  descriptionClassName?: string;
  children?: React.ReactNode;
}) => {
  return (
    <div className={cn("flex flex-col gap-2 w-full", className)}>
      {iconSlot}
      {title && (
        <h4
          className={cn("text-2xl font-semibold text-pretty", titleClassName)}
        >
          {title}
        </h4>
      )}
      <p
        className={cn(
          "text-lg text-pretty leading-normal",
          descriptionClassName,
        )}
      >
        {description}
      </p>
      {children}
    </div>
  );
};
