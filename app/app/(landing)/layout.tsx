import AnalyticsProvider, { GoogleAnalytics } from "#/ui/analytics-provder";
import type { Viewport } from "next";
import { LandingFooter } from "./landing-footer";
import { Toaster } from "sonner";
import { TooltipProvider } from "#/ui/tooltip";
import Script from "#/ui/script";
import { ReactQueryProvider } from "#/ui/query-provider";

export default async function LandingLayout({
  children,
}: React.PropsWithChildren) {
  return (
    <div
      id="landing-layout"
      className="h-full font-display antialiased transition-colors duration-500 bg-white text-black dark:bg-white dark:text-black"
    >
      <div className="flex min-h-full w-full flex-col">
        <TooltipProvider>
          <div suppressHydrationWarning>
            <main>
              <AnalyticsProvider>
                <ReactQueryProvider>{children}</ReactQueryProvider>
              </AnalyticsProvider>
            </main>
          </div>
          <LandingFooter />
        </TooltipProvider>
        <GoogleAnalytics />
        <Toaster />

        {/* Script for https://www.unifygtm.com/ tracking */}
        <Script id="unifygtm">
          {`!function(){window.unify||(window.unify=Object.assign([],["identify","page","startAutoPage","stopAutoPage","startAutoIdentify","stopAutoIdentify"].reduce((function(t,e){return t[e]=function(){return unify.push([e,[].slice.call(arguments)]),unify},t}),{})));var t=document.createElement("script");t.async=!0,t.setAttribute("src","https://tag.unifyintent.com/v1/P4HzpMVD461r39FmtUovLU/script.js"),t.setAttribute("data-api-key","wk_HrJcVefm_CyMhzwRBH6C2WXPMSARbWGQwbPzu9wqq"),t.setAttribute("id","unifytag"),(document.body||document.head).appendChild(t)}();`}
        </Script>
      </div>
    </div>
  );
}

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#000000" },
    { media: "(prefers-color-scheme: dark)", color: "#000000" },
  ],
};
