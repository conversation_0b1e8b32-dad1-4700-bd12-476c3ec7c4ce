import { cn } from "#/utils/classnames";

const SvgShell = ({
  size = 60,
  className,
  children,
}: React.PropsWithChildren<{ size?: number; className?: string }>) => (
  <svg
    width={size}
    height={size}
    className={cn("text-background dark:text-white/20", className)}
    viewBox="0 0 60 60"
    fill="none"
  >
    {children}
  </svg>
);

export const Star = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M23.2156 6.99653C25.7517 1.00115 34.2483 1.00116 36.7844 6.99653L40.4421 15.6434C41.1883 17.4076 42.5924 18.8117 44.3566 19.558L53.0035 23.2157C58.9988 25.7518 58.9988 34.2483 53.0035 36.7844L44.3566 40.4421C42.5924 41.1883 41.1883 42.5924 40.4421 44.3566L36.7844 53.0035C34.2483 58.9989 25.7517 58.9989 23.2156 53.0035L19.5579 44.3566C18.8117 42.5924 17.4076 41.1883 15.6434 40.4421L6.99653 36.7844C1.00116 34.2483 1.00116 25.7518 6.99653 23.2157L15.6434 19.5579C17.4076 18.8117 18.8117 17.4076 19.5579 15.6434L23.2156 6.99653Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Bubble = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M4 30C4 15.6406 15.6406 4 30 4C44.3594 4 56 15.6406 56 30C56 44.3594 44.3594 56 30 56H4V30Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Data = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M54.7503 19.8755C55.9928 19.8753 57 18.8674 57 17.6248V12.0006C57 7.03017 52.9707 3.00017 48.0003 3H11.9997C7.02927 3.00017 3 7.03017 3 12.0006V17.6248C3 18.8674 4.00719 19.8753 5.24969 19.8755H54.7503Z"
      fill="currentColor"
    />
    <path
      d="M54.7503 38.4377C55.9927 38.4376 56.9998 37.4304 57 36.188V30.5629C57 25.5924 52.9707 21.5634 48.0003 21.5632H11.9997C7.02927 21.5634 3 25.5924 3 30.5629V36.188C3.00017 37.4304 4.00729 38.4376 5.24969 38.4377H54.7503Z"
      fill="currentColor"
    />
    <path
      d="M54.7503 57C55.9928 56.9998 57 55.9928 57 54.7503V49.1252C57 44.1547 52.9707 40.1256 48.0003 40.1255H11.9997C7.02929 40.1256 3.00004 44.1547 3 49.1252V54.7503C3.00004 55.9928 4.00722 56.9998 5.24969 57H54.7503Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Grid = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M15.6875 28.6328C22.8672 28.6328 28.6875 22.8125 28.6875 15.6328C28.6875 8.45311 22.8672 2.63281 15.6875 2.63281C8.5078 2.63281 2.6875 8.45311 2.6875 15.6328C2.6875 22.8125 8.5078 28.6328 15.6875 28.6328ZM44.3125 28.6328C51.4922 28.6328 57.3125 22.8125 57.3125 15.6328C57.3125 8.45311 51.4922 2.63281 44.3125 2.63281C37.1328 2.63281 31.3125 8.45311 31.3125 15.6328C31.3125 22.8125 37.1328 28.6328 44.3125 28.6328ZM28.6875 44.3672C28.6875 51.5469 22.8672 57.3672 15.6875 57.3672C8.5078 57.3672 2.6875 51.5469 2.6875 44.3672C2.6875 37.1875 8.5078 31.3672 15.6875 31.3672C22.8672 31.3672 28.6875 37.1875 28.6875 44.3672ZM44.3125 57.3672C51.4922 57.3672 57.3125 51.5469 57.3125 44.3672C57.3125 37.1875 51.4922 31.3672 44.3125 31.3672C37.1328 31.3672 31.3125 37.1875 31.3125 44.3672C31.3125 51.5469 37.1328 57.3672 44.3125 57.3672Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Arrow = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M6.41406 4.38477H41.1314H56.4141V19.6674L56.4141 54.3848H41.1314L41.1314 28.8781L14.3924 55.617L3.58594 44.8105L28.729 19.6674L6.41406 19.6674V4.38477Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Score = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M45.2202 33.4495C51.7257 33.4495 57 38.722 57 45.2252C56.9998 51.7282 51.7255 57 45.2202 57C38.715 56.9998 33.4415 51.7281 33.4413 45.2252C33.4413 38.7221 38.7148 33.4496 45.2202 33.4495Z"
      fill="currentColor"
    />
    <path
      d="M43.4129 6.12821C46.2695 3.27255 50.9016 3.27266 53.7583 6.12821C56.615 8.98392 56.615 13.6143 53.7583 16.47L16.3567 53.8587C13.5 56.7139 8.86877 56.7139 6.01218 53.8587C3.15548 51.003 3.15548 46.3726 6.01218 43.5169L43.4129 6.12821Z"
      fill="currentColor"
    />
    <path
      d="M14.7789 3C21.2843 3 26.5585 8.27173 26.5587 14.7748C26.5587 21.278 21.2844 26.5505 14.7789 26.5505C8.27355 26.5504 3 21.2779 3 14.7748C3.00018 8.27183 8.27366 3.00017 14.7789 3Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Snake = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M20.0039 23C20.0039 21.3431 21.3471 20 23.0039 20L37.0039 20C38.6608 20 40.0039 21.3431 40.0039 23V51C40.0039 52.6569 41.347 54 43.0039 54L51.0039 54C52.6608 54 54.0039 52.6569 54.0039 51L54.0039 16C54.0039 10.4772 49.5268 6 44.0039 6L16.0039 6C10.4811 6 6.00391 10.4772 6.00391 16L6.00391 44C6.00391 49.5229 10.4811 54 16.0039 54H27.0039C32.5268 54 37.0039 49.5229 37.0039 44L37.0039 25.2285C37.0039 23.5717 35.6608 22.2285 34.0039 22.2285L26.0039 22.2285C24.3471 22.2285 23.0039 23.5717 23.0039 25.2285L23.0039 38.5C23.0039 39.3284 22.3323 40 21.5039 40C20.6755 40 20.0039 39.3284 20.0039 38.5L20.0039 23Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const X = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M19.1973 30.3968L4.09554 45.4985L14.902 56.305L30.0037 41.2033L45.1059 56.3055L55.9124 45.499L40.8102 30.3968L55.9142 15.2928L45.1077 4.48633L30.0037 19.5903L14.9002 4.4868L4.09375 15.2933L19.1973 30.3968Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Tool = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M60 30.0003L45 55.9811H15L0 30.0003L15 4.01953L45 4.01953L60 30.0003ZM30 43C37.1797 43 43 37.1797 43 30C43 22.8203 37.1797 17 30 17C22.8203 17 17 22.8203 17 30C17 37.1797 22.8203 43 30 43Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Square = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <rect x="5" y="5" width="50" height="50" fill="currentColor" />
  </SvgShell>
);

export const Log = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path d="M0 5H48V19H0V5Z" fill="currentColor" />
    <path d="M12 41H60V55H12V41Z" fill="currentColor" />
    <path d="M54 23H6V37H54V23Z" fill="currentColor" />
  </SvgShell>
);

export const Play = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M51.5 27.402C53.5 28.5567 53.5 31.4434 51.5 32.5981L15.5 53.3827C13.5 54.5374 11 53.094 11 50.7846L11 9.21541C11 6.90601 13.5 5.46264 15.5 6.61734L51.5 27.402Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Check = ({
  size,
  className,
}: {
  size?: number;
  className?: string;
}) => (
  <SvgShell size={size} className={className}>
    <path
      d="M22.4901 55.3064L2 34.8162L14.8065 22.0098L23.4447 30.648L47.1279 6.96484L58.9344 18.7713L22.4901 55.3064Z"
      fill="currentColor"
    />
  </SvgShell>
);

export const Graph = ({ className }: { className?: string }) => (
  <svg viewBox="0 0 25 24" className={className}>
    <path
      d="M4.08203 16C6.1531 16 7.83203 17.6789 7.83203 19.75V20.25C7.83203 22.3211 6.1531 24 4.08203 24H1.33203C0.779747 24 0.332031 23.5523 0.332031 23V19.75C0.332031 17.6789 2.01096 16 4.08203 16Z"
      fill="currentColor"
    />
    <path
      d="M12.332 8C14.4031 8 16.082 9.67893 16.082 11.75V20.25C16.082 22.3211 14.4031 24 12.332 24H9.58203C9.02975 24 8.58203 23.5523 8.58203 23V11.75C8.58203 9.67893 10.261 8 12.332 8Z"
      fill="currentColor"
    />
    <path
      d="M20.582 0C22.6531 6.87251e-08 24.332 1.67893 24.332 3.75V20.25C24.332 22.3211 22.6531 24 20.582 24H17.832C17.2797 24 16.832 23.5523 16.832 23V3.75C16.832 1.67893 18.511 1.48367e-07 20.582 0Z"
      fill="currentColor"
    />
  </svg>
);

export const Collab = ({ className }: { className?: string }) => (
  <svg viewBox="0 0 25 24" className={className}>
    <path
      d="M18.293 0C21.8138 0 24.668 2.85418 24.668 6.375C24.668 8.64383 23.4822 10.6349 21.6971 11.7646C21.5283 11.8714 21.5283 12.1286 21.6971 12.2354C23.4822 13.3651 24.668 15.3562 24.668 17.625C24.668 21.1458 21.8138 24 18.293 24C16.0241 24 14.0331 22.8143 12.9034 21.0291C12.7966 20.8603 12.5394 20.8603 12.4325 21.0291C11.3028 22.8143 9.3118 24 7.04297 24C3.52215 24 0.667969 21.1458 0.667969 17.625C0.667969 15.3564 1.85325 13.3652 3.63803 12.2354C3.80682 12.1286 3.80683 11.8714 3.63803 11.7646C1.85325 10.6348 0.667969 8.64358 0.667969 6.375C0.667969 2.85418 3.52215 0 7.04297 0C9.31155 0 11.3028 1.18528 12.4325 2.97006C12.5394 3.13886 12.7965 3.13886 12.9034 2.97006C14.0332 1.18528 16.0244 0 18.293 0ZM12.9034 9.7791C12.7966 9.61026 12.5394 9.61026 12.4325 9.77909C11.9263 10.579 11.247 11.2583 10.4471 11.7646C10.2782 11.8714 10.2782 12.1286 10.4471 12.2354C11.2469 12.7415 11.9262 13.4204 12.4325 14.2201C12.5394 14.3889 12.7965 14.3889 12.9034 14.2201C13.4095 13.4206 14.0885 12.7416 14.8881 12.2354C15.0569 12.1286 15.0569 11.8714 14.8881 11.7646C14.0884 11.2583 13.4095 10.5789 12.9034 9.7791Z"
      fill="currentColor"
    />
  </svg>
);

export const Head = ({ className }: { className?: string }) => (
  <svg viewBox="0 0 28 24" className={className}>
    <path
      d="M4.03224 3.51375C6.21488 1.3425 9.23063 0 12.563 0C17.3336 0 21.4557 2.75437 23.4122 6.75L23.857 7.77374L27.1875 15.4537C27.4024 15.9487 27.0367 16.5 26.4958 16.5H24.626V23.25C24.626 23.6644 24.2886 24 23.8721 24H12.7421C6.0641 24 0.520782 18.6788 0.50005 12.0356C0.490625 8.70563 1.84205 5.6925 4.03224 3.51375ZM17.4636 12.75C18.504 12.75 19.3484 11.91 19.3484 10.875C19.3484 9.84 18.504 9 17.4636 9C16.4232 9 15.5788 9.84 15.5788 10.875C15.5788 11.91 16.4232 12.75 17.4636 12.75Z"
      fill="currentColor"
    />
  </svg>
);

export const Shield = ({ className }: { className?: string }) => (
  <svg viewBox="0 0 24 24" className={className}>
    <path
      d="M12.0018 23C9.11546 23 3.50189 20.5 3.50189 16C3.50327 12.2111 3.50032 7.5156 3.5 3.6396C3.49997 3.31431 3.80708 3.07713 4.12584 3.14202C7.69744 3.86903 9.4598 2.71321 12.0018 1.5C14.5627 2.72222 16.3311 3.8862 20.3892 3.1255C20.7036 3.06656 21.0018 3.302 21.0018 3.62189C21.0018 7.59167 21.0018 13.1522 21.0018 16C21.0018 21 14.8882 23 12.0018 23Z"
      fill="currentColor"
    />
  </svg>
);

export const Lock = ({ className }: { className?: string }) => (
  <svg viewBox="0 0 24 24" className={className}>
    <path
      d="M12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2ZM12 7C10.8954 7 10 7.6856 10 8.53125V15.5312C10.0002 16.3767 10.8956 17.0625 12 17.0625C13.1044 17.0625 13.9998 16.3767 14 15.5312V8.53125C14 7.6856 13.1046 7 12 7Z"
      fill="currentColor"
    />
  </svg>
);

export const Hybrid = ({ className }: { className?: string }) => (
  <svg viewBox="0 0 28 24" className={className}>
    <path
      d="M25.9056 15.8966C27.0623 15.8966 28 16.8151 28 17.9483C28 19.0814 27.0623 20 25.9056 20H16.3313C15.1746 20 14.2369 19.0814 14.2369 17.9483C14.2369 16.8151 15.1746 15.8966 16.3313 15.8966H25.9056Z"
      fill="currentColor"
    />
    <path
      d="M11.6687 13.2586C12.8254 13.2586 13.7631 14.1772 13.7631 15.3103C13.7631 16.4435 12.8254 17.3621 11.6687 17.3621H2.09439C0.937691 17.3621 1.00093e-07 16.4435 0 15.3103C-3.23028e-08 14.1772 0.93769 13.2586 2.09439 13.2586H11.6687Z"
      fill="currentColor"
    />
    <path
      d="M25.756 10.6207C26.9953 10.6207 28 11.6049 28 12.819C28 14.033 26.9953 15.0172 25.756 15.0172H16.4809C15.2415 15.0172 14.2369 14.033 14.2369 12.819C14.2369 11.6049 15.2415 10.6207 16.4809 10.6207H25.756Z"
      fill="currentColor"
    />
    <path
      d="M11.5191 7.98276C12.7585 7.98276 13.7631 8.96696 13.7631 10.181C13.7631 11.3951 12.7585 12.3793 11.5191 12.3793H2.24399C1.00467 12.3793 1.11717e-07 11.3951 0 10.181C-3.46102e-08 8.96696 1.00467 7.98276 2.24399 7.98276H11.5191Z"
      fill="currentColor"
    />
    <path
      d="M25.9056 5.63793C27.0623 5.63793 28 6.55652 28 7.68966C28 8.82279 27.0623 9.74138 25.9056 9.74138H16.3313C15.1746 9.74138 14.2369 8.82279 14.2369 7.68966C14.2369 6.55652 15.1746 5.63793 16.3313 5.63793H25.9056Z"
      fill="currentColor"
    />
    <path
      d="M11.6687 3C12.8254 3 13.7631 3.91859 13.7631 5.05172C13.7631 6.18486 12.8254 7.10345 11.6687 7.10345H2.09439C0.93769 7.10345 -3.23028e-08 6.18486 0 5.05172C9.94499e-08 3.91859 0.937691 3 2.09439 3H11.6687Z"
      fill="currentColor"
    />
  </svg>
);
