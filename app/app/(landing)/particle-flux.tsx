"use client";

import { cn } from "#/utils/classnames";
import React, { memo, useCallback, useEffect, useRef } from "react";

// CONSTANTS
const rowOvershoot = 0.6;
const distortedRowCount = 3;
const rowDistortionCycleDuration = 100; // frames for complete cycle
const simultaneousPulls = 1;
const pullMinPx = 10;
const pullMaxPx = 30;
const timeScale = 0.4;
const emissionRate = 6;
const particleRadius = 70;
const scaleDecayFrames = 400;
const colorDecayFrames = 400;
const emissionDirection = 180;
const emissionSpeed = 5;
const directionChaos = 0.4;
const positionChaos = 0.9;
const wiggleStrength = 2;
const cols = 60;
const rows = 6;
const gridScaleMultiplier = 1.8;

const bgColor = "#ffffff";
const rowScaleMin = 4;
const rowScaleMax = 10;
const rowDistortionContrast = 100;
const animColor = "#cccccc";
const animEase = 0.25;
const decayFrames = 200;
const waveAmplitude = 80;
const waveDuration = 300;
const highlightTargetCount = 18;
const zeroPxThreshold = 1;
const highlightColors = [
  "rgb(44 31 234)",
  "rgb(204 255 0)",
  "rgb(67 16 29)",
  "rgb(9 65 53)",
];
const TWO_PI = Math.PI * 2;

// Performance constants
const TRIG_TABLE_SIZE = 360;

// UTILITY FUNCTIONS
// note: legacy map() removed; direct particle influence now used

function constrain(value: number, min: number, max: number) {
  return Math.max(min, Math.min(max, value));
}

function lerp(start: number, end: number, amt: number) {
  return start + amt * (end - start);
}

function random(min?: number, max?: number) {
  if (typeof min === "undefined") return Math.random();
  if (typeof max === "undefined") return Math.random() * min;
  return Math.random() * (max - min) + min;
}

function floor(value: number) {
  return Math.floor(value);
}

function min(a: number, b: number) {
  return Math.min(a, b);
}

function max(a: number, b: number) {
  return Math.max(a, b);
}

function pow(base: number, exp: number) {
  return Math.pow(base, exp);
}

function sqrt(value: number) {
  return Math.sqrt(value);
}

function abs(value: number) {
  return Math.abs(value);
}

function radians(degrees: number) {
  return degrees * (Math.PI / 180);
}

function easeInOutCubic(t: number) {
  return t < 0.5 ? 4 * t * t * t : 1 - pow(-2 * t + 2, 3) / 2;
}

// PERFORMANCE OPTIMIZATIONS

// Pre-computed trigonometric tables
class TrigTable {
  private sinTable: number[];
  private cosTable: number[];

  constructor(size: number) {
    this.sinTable = new Array(size);
    this.cosTable = new Array(size);
    for (let i = 0; i < size; i++) {
      const angle = (i / size) * TWO_PI;
      this.sinTable[i] = Math.sin(angle);
      this.cosTable[i] = Math.cos(angle);
    }
  }

  sin(angle: number): number {
    const normalized = ((angle % TWO_PI) + TWO_PI) % TWO_PI;
    const index = Math.floor((normalized / TWO_PI) * this.sinTable.length);
    return this.sinTable[index];
  }

  cos(angle: number): number {
    const normalized = ((angle % TWO_PI) + TWO_PI) % TWO_PI;
    const index = Math.floor((normalized / TWO_PI) * this.cosTable.length);
    return this.cosTable[index];
  }
}

// Particle object pool
interface Particle {
  x: number;
  y: number;
  vx: number;
  vy: number;
  birth: number;
  wiggle: number;
  active: boolean;
}

class ParticlePool {
  private pool: Particle[] = [];
  private active: Particle[] = [];
  private maxPoolSize = 1000;

  acquire(): Particle {
    let particle = this.pool.pop();
    if (!particle) {
      particle = {
        x: 0,
        y: 0,
        vx: 0,
        vy: 0,
        birth: 0,
        wiggle: 0,
        active: false,
      };
    }
    particle.active = true;
    this.active.push(particle);
    return particle;
  }

  release(particle: Particle) {
    particle.active = false;
    const index = this.active.indexOf(particle);
    if (index > -1) {
      this.active.splice(index, 1);
    }
    if (this.pool.length < this.maxPoolSize) {
      this.pool.push(particle);
    }
  }

  getActive(): Particle[] {
    return this.active;
  }

  cleanup(currentTime: number, maxAge: number) {
    for (let i = this.active.length - 1; i >= 0; i--) {
      const particle = this.active[i];
      if (currentTime - particle.birth > maxAge) {
        this.release(particle);
      }
    }
  }
}

// Image data cache for expensive getImageData operations
// note: ImageDataCache removed; we no longer read canvas pixels

export const ParticleFlux = memo(function ParticleFlux(props: {
  rows?: number;
  cols?: number;
  highlightColors?: string[];
  color?: string;
  bgColor?: string;
  rightBuffer?: number;
  className?: string;
  gridScaleMultiplier?: number;
  highlightTargetCount?: number;
  waveAmplitude?: number;
}) {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particleCanvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);

  const effectiveRows = props.rows ?? rows;
  const effectiveHighlightColors = props.highlightColors ?? highlightColors;
  const effectiveColor = props.color ?? animColor;
  const effectiveGridScaleMultiplier =
    props.gridScaleMultiplier ?? gridScaleMultiplier;
  const effectiveBgColor = props.bgColor ?? bgColor;
  const effectiveHighlightTargetCount =
    props.highlightTargetCount ?? highlightTargetCount;
  const effectiveWaveAmplitude = props.waveAmplitude ?? waveAmplitude;

  const getResponsiveColumns = useCallback(
    (containerWidth: number) => {
      const baseCols = props.cols ?? cols;

      let scaleFactor: number = 1.0;
      if (containerWidth < 480) {
        scaleFactor = 0.2;
      } else if (containerWidth < 768) {
        scaleFactor = 0.6;
      } else if (containerWidth < 1024) {
        scaleFactor = 0.8;
      } else if (containerWidth < 1440) {
        scaleFactor = 0.9;
      }

      return Math.round(baseCols * scaleFactor);
    },
    [props.cols],
  );

  const getResponsiveParticleRadius = useCallback((containerWidth: number) => {
    const baseRadius = particleRadius;
    if (containerWidth < 480) return baseRadius * 0.6;
    return baseRadius;
  }, []);

  const getResponsiveEmissionRate = useCallback((containerWidth: number) => {
    const baseRate = emissionRate;
    if (containerWidth < 480) return baseRate * 0.6;
    return baseRate;
  }, []);

  useEffect(() => {
    const canvas = canvasRef.current;
    const particleCanvas = particleCanvasRef.current;
    if (!canvas || !particleCanvas) return;

    const ctx = canvas.getContext("2d");
    const particleCtx = particleCanvas.getContext("2d", {
      willReadFrequently: true,
    });
    if (!ctx || !particleCtx) return;

    // Performance optimization instances
    const trigTable = new TrigTable(TRIG_TABLE_SIZE);
    const particlePool = new ParticlePool();
    // Pixel readback removed: compute activations directly from particles

    // Frame timing
    let lastFrameTime = 0;
    // frame count no longer needed

    // Cached calculations
    let cachedWavePhase = 0;
    let cachedDecayRatio = 0;
    let cachedEaseNow = 0;
    let cachedCellW = 0;
    let cachedGridScale = effectiveGridScaleMultiplier;
    let cachedParticleRadius = particleRadius;
    let cachedEmissionRate = emissionRate;

    // EMITTER INPUT SETTINGS
    let emitterX = 20;
    let emitterY = 5;
    let emitterRadius = 50;
    let emissionAcc = 0;

    // Canvas dimensions
    let canvasWidth = 1920;
    let canvasHeight = 1080;

    // Visibility and motion preferences
    let isTabVisible = true;
    let isInViewport = true;
    let isHiddenByStyle = false;

    // Adaptive quality state
    let qualityLevel = 3; // 1 = low, 2 = medium, 3 = high
    let emaFrameMs = 16.7;
    let lastQualityAdjust = 0;
    // keep quality scaling for content density, but do not cap DPR
    let adaptiveColScale = 1;
    let adaptiveEmissionScale = 1;
    let adaptiveParticleScale = 1;
    // image sampling cadence removed

    function applyQuality(level: number) {
      const q = Math.max(1, Math.min(3, Math.round(level)));
      qualityLevel = q;
      if (qualityLevel === 1) {
        adaptiveColScale = 0.6;
        adaptiveEmissionScale = 0.5;
        adaptiveParticleScale = 0.6;
      } else if (qualityLevel === 2) {
        adaptiveColScale = 0.8;
        adaptiveEmissionScale = 0.75;
        adaptiveParticleScale = 0.8;
      } else {
        adaptiveColScale = 1;
        adaptiveEmissionScale = 1;
        adaptiveParticleScale = 1;
      }
      // Update runtime parameters in place to avoid reinitializing grid/canvas
      cachedParticleRadius =
        getResponsiveParticleRadius(canvasWidth) * adaptiveParticleScale;
      cachedEmissionRate =
        getResponsiveEmissionRate(canvasWidth) * adaptiveEmissionScale;
    }

    // INTERNAL STATE
    let t = 0;
    const startScales: number[] = [],
      targetScales: number[] = [],
      currentScales: number[] = [];
    const rowHeights: number[] = [],
      rowYPositions: number[] = [];
    const grid: Array<
      Array<{
        x: number;
        activation: number;
        highlighted: boolean;
        highlightColor: string | null;
        grew: boolean;
      }>
    > = [];
    let distortionCycleStartFrame = 0;
    const activePulls: Array<{
      row: number;
      startAmt: number;
      targetAmt: number;
      current: number;
    }> = [];
    let activeScaleRows: number[] = [];
    let effectiveCols = cols; // Will be updated based on responsive calculation

    function updateCanvasDimensions() {
      if (!canvas || !particleCanvas || !ctx || !particleCtx) return;

      const rect = canvas.getBoundingClientRect();
      const dpr = window.devicePixelRatio || 1;

      canvasWidth = rect.width;
      canvasHeight = rect.height;

      // Update effective columns based on responsive calculation
      effectiveCols = Math.max(
        4,
        Math.round(getResponsiveColumns(canvasWidth) * adaptiveColScale),
      );

      canvas.width = canvasWidth * dpr;
      canvas.height = canvasHeight * dpr;
      particleCanvas.width = canvasWidth * dpr;
      particleCanvas.height = canvasHeight * dpr;

      ctx.scale(dpr, dpr);
      particleCtx.scale(dpr, dpr);

      const emitterXRatio = 2720 / 1920;
      const emitterYRatio = 540 / 1080;

      emitterX = canvasWidth * emitterXRatio;
      emitterY = canvasHeight * emitterYRatio;
      emitterRadius = Math.min(canvasWidth, canvasHeight) * 0.625;

      // Update cached cell width
      cachedCellW = canvasWidth / effectiveCols;

      // Update responsive grid scale
      cachedGridScale = effectiveGridScaleMultiplier;
      cachedParticleRadius =
        getResponsiveParticleRadius(canvasWidth) * adaptiveParticleScale;
      cachedEmissionRate =
        getResponsiveEmissionRate(canvasWidth) * adaptiveEmissionScale;

      initializeGrid();
    }

    function initializeGrid() {
      const bufferCols =
        Math.ceil(pullMaxPx / (canvasWidth / effectiveCols)) + 20;
      const totalCols = effectiveCols + bufferCols * 2;

      grid.length = 0;

      for (let i = 0; i < totalCols; i++) {
        grid[i] = [];
        for (let j = 0; j < effectiveRows; j++) {
          grid[i][j] = {
            x: 0,
            activation: 0,
            highlighted: false,
            highlightColor: null,
            grew: false,
          };
        }
      }
    }

    function initializeWithContent() {
      // Create initial particles spread across the canvas for immediate visual content
      const initialParticleCount = 18;

      for (let i = 0; i < initialParticleCount; i++) {
        const particle = particlePool.acquire();

        // Spread particles across the canvas width and height
        particle.x = random(0, canvasWidth);
        particle.y = random(0, canvasHeight);

        // Give them varied velocities
        const angle = random(0, TWO_PI);
        const speed = random(1, 3);
        particle.vx = trigTable.cos(angle) * speed;
        particle.vy = trigTable.sin(angle) * speed;

        // Stagger birth times so they don't all disappear at once
        particle.birth = t - random(0, scaleDecayFrames * 0.8);
        particle.wiggle = random(0, 1000);
      }

      // Immediately render the initial particles to the particle canvas
      if (particleCtx) {
        drawEmitInput(timeScale);
      }

      // Pre-activate some grid elements for immediate visual feedback
      const bufferCols = Math.ceil(pullMaxPx / cachedCellW) + 20;
      const totalCols = effectiveCols + bufferCols * 2;

      // Ensure grid is initialized
      if (grid.length === 0) {
        initializeGrid();
      }

      // Pre-activate random grid elements with varying intensities
      const initialActivations = Math.min(60, totalCols * effectiveRows * 0.3);

      for (let a = 0; a < initialActivations; a++) {
        const i = Math.floor(random(bufferCols, bufferCols + effectiveCols));
        const j = Math.floor(random(0, effectiveRows));

        if (i < grid.length && grid[i] && grid[i][j]) {
          // Set initial activation and size for immediate visual content
          grid[i][j].activation = random(0.2, 0.8);
          grid[i][j].x =
            cachedCellW *
            grid[i][j].activation *
            cachedGridScale *
            random(0.5, 1.0);
        }
      }
    }

    function setup() {
      updateCanvasDimensions();

      if (!canvas || !particleCanvas || !ctx || !particleCtx) return;

      ctx.imageSmoothingEnabled = true;
      particleCtx.imageSmoothingEnabled = true;

      for (let j = 0; j < effectiveRows; j++) {
        startScales[j] = targetScales[j] = currentScales[j] = 1;
      }

      setupNextDistortionCycle();

      // Initialize row layout for proper positioning
      updateRowLayout();

      // Initialize with content so animation appears immediately
      initializeWithContent();

      const resizeObserver = new ResizeObserver(() => {
        updateCanvasDimensions();
        // If visually hidden (opacity 0) on small screens, avoid running
        if (wrapperRef.current) {
          const style = getComputedStyle(wrapperRef.current);
          isHiddenByStyle = parseFloat(style.opacity) < 0.05;
        }
      });
      resizeObserver.observe(canvas);

      // Observe visibility of the wrapper in viewport
      const intersectionObserver = new IntersectionObserver(
        (entries) => {
          for (const entry of entries) {
            isInViewport = entry.isIntersecting;
            maybeStart();
          }
        },
        { root: null, threshold: 0 },
      );
      if (wrapperRef.current) intersectionObserver.observe(wrapperRef.current);

      // Pause when tab hidden
      const onVisibilityChange = () => {
        isTabVisible = !document.hidden;
        if (!isTabVisible) stop();
        else maybeStart();
      };
      document.addEventListener("visibilitychange", onVisibilityChange);

      // Initialize quality (lower on very small devices)
      if (
        navigator?.hardwareConcurrency &&
        navigator.hardwareConcurrency <= 4
      ) {
        applyQuality(2);
      } else {
        applyQuality(3);
      }

      maybeStart();

      return () => {
        resizeObserver.disconnect();
        intersectionObserver.disconnect();
        document.removeEventListener("visibilitychange", onVisibilityChange);
      };
    }

    function maintainHighlights(totalCols: number) {
      let active = 0;
      const candidates: Array<{ i: number; j: number }> = [];

      // Viewport culling for highlight maintenance
      const bufferCols = Math.ceil(pullMaxPx / cachedCellW) + 20;
      const visibleStartCol = Math.max(0, bufferCols - 5);
      const visibleEndCol = Math.min(totalCols, bufferCols + effectiveCols + 5);

      for (let i = visibleStartCol; i < visibleEndCol && i < grid.length; i++) {
        for (let j = 0; j < effectiveRows; j++) {
          if (grid[i] && grid[i][j] && grid[i][j].highlighted) {
            active++;
          } else if (grid[i] && grid[i][j] && grid[i][j].x <= zeroPxThreshold) {
            candidates.push({ i, j });
          }
        }
      }

      if (active >= effectiveHighlightTargetCount || !candidates.length) return;

      // Shuffle candidates (Fisher-Yates)
      for (let k = candidates.length - 1; k > 0; k--) {
        const j = Math.floor(Math.random() * (k + 1));
        [candidates[k], candidates[j]] = [candidates[j], candidates[k]];
      }

      const need = min(
        effectiveHighlightTargetCount - active,
        candidates.length,
      );
      for (let k = 0; k < need; k++) {
        const { i, j } = candidates[k];
        grid[i][j].highlighted = true;
        grid[i][j].highlightColor =
          effectiveHighlightColors[
            floor(random(0, effectiveHighlightColors.length))
          ];
        grid[i][j].grew = false;
      }
    }

    function animate(currentTime: number) {
      // Calculate actual delta time for smooth animation
      const frameMs = lastFrameTime ? currentTime - lastFrameTime : 16.67;
      emaFrameMs = lerp(emaFrameMs, frameMs, 0.05);
      const actualDeltaTime = lastFrameTime ? Math.min(frameMs / 16.67, 3) : 1; // Cap at 3x normal speed
      const dt = timeScale * actualDeltaTime;

      t += dt;
      // frameCount removed
      lastFrameTime = currentTime;

      // Early stop if not supposed to run
      if (!isTabVisible || !isInViewport || isHiddenByStyle) {
        animationRef.current = null;
        return;
      }

      // Adapt quality roughly every 750ms with hysteresis
      if (currentTime - lastQualityAdjust > 750) {
        lastQualityAdjust = currentTime;
        if (emaFrameMs > 26 && qualityLevel > 1) {
          applyQuality(qualityLevel - 1);
        } else if (emaFrameMs < 15 && qualityLevel < 3) {
          applyQuality(qualityLevel + 1);
        }
      }

      // Cache frequently used calculations
      cachedWavePhase = (TWO_PI * t) / waveDuration;
      cachedDecayRatio = dt / decayFrames;
      cachedEaseNow = animEase * actualDeltaTime;

      const bufferCols = Math.ceil(pullMaxPx / cachedCellW) + 20;
      const totalCols = effectiveCols + bufferCols * 2;

      if (!ctx || !particleCtx) return;

      // Clear canvas
      ctx.fillStyle = effectiveBgColor;
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      maintainHighlights(totalCols);
      updateRowDistortion();
      updateRowLayout();

      emitParticles(dt);
      drawEmitInput(dt);

      const offTop = Array(effectiveRows).fill(0),
        offBot = Array(effectiveRows).fill(0);
      for (const p of activePulls) {
        offTop[p.row] = offBot[p.row] = p.current;
        if (p.row > 0) offBot[p.row - 1] += p.current;
        if (p.row < effectiveRows - 1) offTop[p.row + 1] += p.current;
      }

      // Viewport culling for grid processing
      const startCol = bufferCols;
      const visibleStartCol = Math.max(0, startCol - 2);
      const visibleEndCol = Math.min(totalCols, startCol + effectiveCols + 2);

      // Update grid with direct particle influence sampling
      const activeParticles = particlePool.getActive();
      const sigma2 = cachedParticleRadius * cachedParticleRadius;
      const influenceR = cachedParticleRadius * 3;
      const influenceR2 = influenceR * influenceR;
      for (let i = visibleStartCol; i < visibleEndCol && i < grid.length; i++) {
        for (let j = 0; j < effectiveRows; j++) {
          if (!grid[i] || !grid[i][j]) continue;

          const cellX =
            (i - bufferCols) * cachedCellW - (props.rightBuffer ?? 200);
          const cellY = rowYPositions[j];
          const cellH = rowHeights[j];

          // Single-point sampling per cell center against particle influences
          const px = cellX + cachedCellW * 0.5;
          const py = cellY + cellH * 0.5;
          let sum = 0;
          for (let p = 0; p < activeParticles.length; p++) {
            const part = activeParticles[p];
            const dx = part.x - px;
            const dy = part.y - py;
            const d2 = dx * dx + dy * dy;
            if (d2 > influenceR2) continue;
            // Gaussian falloff
            sum += Math.exp(-d2 / (2 * sigma2));
            if (sum >= 1) break;
          }
          const target = constrain(sum, 0, 1);
          grid[i][j].activation = lerp(
            grid[i][j].activation,
            target,
            Math.min(cachedDecayRatio, 0.15),
          );
          const wTarget = cachedCellW * grid[i][j].activation * cachedGridScale;
          grid[i][j].x += (wTarget - grid[i][j].x) * cachedEaseNow;

          // Handle highlight state transitions
          if (grid[i][j].highlighted) {
            if (!grid[i][j].grew && grid[i][j].x > zeroPxThreshold) {
              grid[i][j].grew = true;
            }
            if (grid[i][j].grew && grid[i][j].x <= zeroPxThreshold) {
              grid[i][j].highlighted = false;
              grid[i][j].highlightColor = null;
            }
          }
        }
      }

      // Draw grid with viewport culling
      ctx.imageSmoothingEnabled = true;
      for (let i = visibleStartCol; i < visibleEndCol && i < grid.length; i++) {
        for (let j = 0; j < effectiveRows; j++) {
          if (!grid[i] || !grid[i][j]) continue;

          const waveX =
            trigTable.sin(cachedWavePhase + j * 0.2) * effectiveWaveAmplitude;
          const yT = rowYPositions[j];
          const yB = yT + rowHeights[j];
          const baseX =
            (i - bufferCols) * cachedCellW + (props.rightBuffer ?? 200);
          const xT = baseX + waveX + offTop[j];
          const xB = baseX + waveX + offBot[j];
          const w = grid[i][j].x;

          // Choose color based on highlight state
          if (grid[i][j].highlighted && grid[i][j].highlightColor) {
            ctx.fillStyle = grid[i][j].highlightColor!;
          } else {
            ctx.fillStyle = effectiveColor;
          }

          const leftTop = xT + (cachedCellW - w) * 0.5;
          const rightTop = xT + (cachedCellW + w) * 0.5;
          const leftBot = xB + (cachedCellW - w) * 0.5;
          const rightBot = xB + (cachedCellW + w) * 0.5;

          // Additional viewport culling for drawing
          if (rightTop > 0 && leftTop < canvasWidth) {
            ctx.beginPath();
            ctx.moveTo(leftTop, yT);
            ctx.lineTo(rightTop, yT);
            ctx.lineTo(rightBot, yB);
            ctx.lineTo(leftBot, yB);
            ctx.closePath();
            ctx.fill();
          }
        }
      }

      animationRef.current = requestAnimationFrame(animate);
    }

    function stop() {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
    }

    function maybeStart() {
      if (
        animationRef.current == null &&
        isTabVisible &&
        isInViewport &&
        !isHiddenByStyle
      ) {
        animationRef.current = requestAnimationFrame(animate);
      }
    }

    function drawEmitInput(dt: number) {
      // Update particles without drawing to an offscreen buffer
      // Cleanup old particles using pool
      const maxAge = max(scaleDecayFrames, colorDecayFrames);
      particlePool.cleanup(t, maxAge);

      const activeParticles = particlePool.getActive();

      for (const p of activeParticles) {
        const age = t - p.birth;
        if (age > maxAge) continue;

        // Use trigonometric table for wiggle calculation
        const wiggleAngle = t * 0.1 + p.wiggle;
        p.x += p.vx * dt + trigTable.sin(wiggleAngle) * wiggleStrength * dt;
        p.y += p.vy * dt + trigTable.cos(wiggleAngle) * wiggleStrength * dt;

        const sT = constrain(age / scaleDecayFrames, 0, 1);
        const fac = max(1 - sT, 0);
        if (fac <= 0.001) continue;

        // No draw: visual input is now computed analytically
      }
    }

    function emitParticles(dt: number) {
      emissionAcc += (cachedEmissionRate / 60) * dt;
      while (emissionAcc >= 1) {
        const base = radians(emissionDirection);
        const ang = base + random(-directionChaos, directionChaos);
        const r = emitterRadius * sqrt(random(0, 1));
        const randomAngle = random(0, TWO_PI);
        const ox = r * trigTable.cos(randomAngle) * positionChaos;
        const oy = r * trigTable.sin(randomAngle) * positionChaos;

        const particle = particlePool.acquire();
        particle.x = emitterX + ox;
        particle.y = emitterY + oy;
        particle.vx = trigTable.cos(ang) * emissionSpeed;
        particle.vy = trigTable.sin(ang) * emissionSpeed;
        particle.birth = t;
        particle.wiggle = random(0, 1000);

        emissionAcc -= 1;
      }
    }

    function updateRowDistortion() {
      if (t - distortionCycleStartFrame >= rowDistortionCycleDuration) {
        setupNextDistortionCycle();
        distortionCycleStartFrame = t;
      }

      const cycleFraction =
        (t - distortionCycleStartFrame) / rowDistortionCycleDuration;
      const easedT = easeInOutCubic(cycleFraction);

      for (const p of activePulls) {
        p.current = lerp(p.startAmt, p.targetAmt, easedT);
      }
    }

    function setupNextDistortionCycle() {
      for (const p of activePulls) {
        p.startAmt = p.current;
      }

      for (let j = 0; j < effectiveRows; j++) {
        startScales[j] = currentScales[j];
      }

      if (activePulls.length === 0) {
        const usedRows: number[] = [];
        for (let i = 0; i < min(simultaneousPulls, effectiveRows); i++) {
          const availableRows = [];
          for (let r = 0; r < effectiveRows; r++) {
            if (!usedRows.some((used) => abs(used - r) <= 1)) {
              availableRows.push(r);
            }
          }
          if (availableRows.length === 0) break;

          const row = availableRows[floor(random(0, availableRows.length))];
          usedRows.push(row);

          activePulls.push({
            row,
            startAmt: 0,
            targetAmt:
              random(pullMinPx, pullMaxPx) * (random(0, 1) < 0.5 ? -1 : 1),
            current: 0,
          });
        }
      } else {
        for (const p of activePulls) {
          p.targetAmt =
            random(pullMinPx, pullMaxPx) * (random(0, 1) < 0.5 ? -1 : 1);
        }
      }

      activeScaleRows = [];
      while (activeScaleRows.length < min(distortedRowCount, effectiveRows)) {
        const idx = floor(random(0, effectiveRows));
        if (!activeScaleRows.includes(idx)) activeScaleRows.push(idx);
      }

      for (let j = 0; j < effectiveRows; j++) {
        if (activeScaleRows.includes(j)) {
          const baseScale = (rowScaleMin + rowScaleMax) / 2;
          const range = (rowScaleMax - rowScaleMin) / 2;
          const variation =
            (random(0, 1) - 0.5) * 2 * range * (rowDistortionContrast / 100);
          targetScales[j] = constrain(
            baseScale + variation,
            rowScaleMin,
            rowScaleMax,
          );
        } else {
          targetScales[j] = 1 + (random(0, 1) - 0.5) * 0.2;
        }
      }
    }

    function updateRowLayout() {
      const cycleFraction =
        (t - distortionCycleStartFrame) / rowDistortionCycleDuration;
      const easedT = easeInOutCubic(cycleFraction);

      for (let j = 0; j < effectiveRows; j++) {
        currentScales[j] = lerp(startScales[j], targetScales[j], easedT);
      }

      const total = currentScales.reduce((sum, s) => sum + s, 0);
      let yOff = 0;

      for (let j = 0; j < effectiveRows; j++) {
        const baseHeight = (currentScales[j] / total) * canvasHeight;
        if (j < effectiveRows - 1) {
          rowHeights[j] = baseHeight + rowOvershoot;
        } else {
          rowHeights[j] = canvasHeight - yOff;
        }
        rowYPositions[j] = yOff;
        yOff += baseHeight;
      }
    }

    const cleanup = setup();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (cleanup) {
        cleanup();
      }
    };
  }, [
    effectiveRows,
    effectiveHighlightColors,
    effectiveColor,
    props.rightBuffer,
    props.cols,
    effectiveGridScaleMultiplier,
    effectiveBgColor,
    effectiveHighlightTargetCount,
    getResponsiveColumns,

    getResponsiveParticleRadius,
    getResponsiveEmissionRate,
    effectiveWaveAmplitude,
  ]);

  return (
    <div
      ref={wrapperRef}
      className={cn(
        "pointer-events-none absolute inset-0 z-[-1]",
        props.className,
      )}
    >
      <canvas
        ref={particleCanvasRef}
        className="absolute inset-0"
        style={{ width: "100%", height: "100%" }}
      />
      <canvas
        ref={canvasRef}
        className="absolute inset-0"
        style={{ width: "100%", height: "100%" }}
      />
    </div>
  );
});
