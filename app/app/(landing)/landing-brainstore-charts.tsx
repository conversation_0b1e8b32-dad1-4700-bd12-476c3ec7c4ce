import Reveal from "react-awesome-reveal";
import { keyframes } from "@emotion/react";

const customAnimation = keyframes`
  from {
    max-width: 5px;
  }
  to {
    max-width: 100%;
  }
`;

export const LandingBrainstoreCharts = ({
  brainstoreValue,
  competitorValue,
}: {
  brainstoreValue: number;
  competitorValue: number;
}) => {
  return (
    <div className="mb-6 flex flex-col gap-4">
      <div className="flex gap-3">
        <div className="w-24 flex-none font-suisse text-xs uppercase tracking-wider">
          Brainstore
        </div>
        <Reveal
          keyframes={customAnimation}
          duration={2000}
          triggerOnce
          className="flex flex-none overflow-hidden rounded-sm ease-in bg-transparent"
          style={{
            minWidth: 5,
            width: `calc(${(brainstoreValue / competitorValue) * 100}% - 200px)`,
          }}
        >
          <div className="flex-1 rounded-sm bg-brandOrange" />
        </Reveal>
        <div className="flex-none font-suisse text-xs uppercase tracking-wider">
          {brainstoreValue.toLocaleString()} ms
        </div>
      </div>
      <div className="flex gap-3 text-brandTerracotta">
        <div className="w-24 flex-none font-suisse text-xs uppercase tracking-wider">
          Competition
        </div>
        <Reveal
          keyframes={customAnimation}
          duration={2000}
          triggerOnce
          className="flex flex-none overflow-hidden rounded-sm ease-in bg-transparent"
          style={{
            width: `calc(100% - 200px)`,
          }}
        >
          <div
            className="flex-1"
            style={{
              backgroundImage: `repeating-linear-gradient(
              -55deg,
              transparent,
              transparent 3px,
              rgba(201, 90, 76, 0.6) 3px,
              rgba(201, 90, 76, 0.6) 4px
            )`,
            }}
          />
        </Reveal>
        <div className="flex-none font-suisse text-xs uppercase tracking-wider">
          {competitorValue.toLocaleString()} ms
        </div>
      </div>
    </div>
  );
};
