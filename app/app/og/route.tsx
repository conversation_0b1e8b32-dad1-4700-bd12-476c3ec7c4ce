import { <PERSON><PERSON>, <PERSON><PERSON> } from "#/ui/landing/logo";
import { ImageResponse } from "next/og";
import { type NextRequest } from "next/server";

async function loadBTSansFont() {
  return await fetch(
    new URL("../../assets/BraintrustDisplayV2-Regular.otf", import.meta.url),
  ).then((res) => res.arrayBuffer());
}

async function loadSuisseMonoFont() {
  return await fetch(
    new URL("../../assets/SuisseIntlMono-Regular.otf", import.meta.url),
  ).then((res) => res.arrayBuffer());
}

export const runtime = "edge";

export async function GET(req: NextRequest) {
  const title = req.nextUrl.searchParams.get("title");
  const description = req.nextUrl.searchParams.get("description");
  const template = req.nextUrl.searchParams.get("template");

  const fontData = await loadBTSansFont();
  const monoFontData = await loadSuisseMonoFont();

  if (template === "product") {
    return new ImageResponse(
      (
        <div
          tw="flex flex-col w-full h-full bg-white"
          style={{ color: "#2C1FEB" }}
        >
          <div
            tw="flex flex-col flex-1"
            style={{
              paddingLeft: 56,
              paddingRight: 56,
              paddingTop: 48,
              paddingBottom: 48,
            }}
          >
            <div tw="flex flex-1 gap-6 overflow-hidden">
              <div
                tw="text-7xl font-medium w-full flex text-balance"
                style={{
                  lineHeight: 1,
                  fontFamily: "display",
                  // wrap title up to two lines (satori does not support line-clamp)
                  wordBreak: "break-word",
                  overflow: "hidden",
                  fontSize: title?.length && title.length < 20 ? 96 : 72,
                }}
              >
                {title ?? ""}
              </div>
              <Icon size={64} />
            </div>
            <div
              tw="text-3xl tracking-widest flex-none leading-tight uppercase truncate"
              style={{
                fontFamily: "mono",
                color: "#2C1FEB",
              }}
            >
              {description ?? "End-to-end AI platform"}
            </div>
          </div>
          <div
            tw="flex-none border-t-2 z-10 flex"
            style={{ borderColor: "#2C1FEB" }}
          >
            <ProductFlux />
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
        fonts: [
          {
            name: "display",
            data: fontData,
            style: "normal",
          },
          {
            name: "mono",
            data: monoFontData,
            style: "normal",
          },
        ],
      },
    );
  }

  if (template === "docs") {
    return new ImageResponse(
      (
        <div tw="z-20 relative flex flex-col w-full h-full bg-black text-white">
          <div
            style={{
              color: "#313131",
            }}
            tw="absolute top-0 right-0 bottom-0 z-10 flex flex-col"
          >
            <DocsFlux />
          </div>
          <div
            tw="flex flex-col flex-1 justify-between"
            style={{ padding: 56 }}
          >
            <div tw="flex items-center justify-between">
              <Logo width={280} />
            </div>
            <div
              tw="text-7xl font-medium leading-tighter w-full"
              style={{
                fontFamily: "display",
                // wrap title up to two lines (satori does not support line-clamp)
                wordBreak: "break-word",
                maxHeight: 240,
                overflow: "hidden",
                fontSize: title?.length && title.length < 20 ? 96 : 72,
              }}
            >
              {title ?? ""}
            </div>
          </div>
          <div
            tw="text-3xl tracking-widest flex-none font-medium leading-tight uppercase truncate border-t-2"
            style={{
              fontFamily: "mono",
              color: "#A6A6A6",
              padding: 56,
              borderColor: "#313131",
            }}
          >
            Documentation
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
        fonts: [
          {
            name: "display",
            data: fontData,
            style: "normal",
          },
          {
            name: "mono",
            data: monoFontData,
            style: "normal",
          },
        ],
      },
    );
  }

  if (template === "blog") {
    return new ImageResponse(
      (
        <div
          tw="z-20 relative flex flex-col w-full h-full bg-black text-white"
          style={{
            backgroundColor: "#00392D",
          }}
        >
          <div tw="absolute top-0 left-0 right-0 z-10 flex flex-col">
            <BlogFlux />
          </div>
          <div tw="flex flex-col flex-1" style={{ padding: 56 }}>
            <div
              tw="text-7xl font-medium leading-tighter w-full"
              style={{
                fontFamily: "display",
                // wrap title up to two lines (satori does not support line-clamp)
                wordBreak: "break-word",
                overflow: "hidden",
                fontSize: 96,
              }}
            >
              {title ?? ""}
            </div>
          </div>
          <div
            tw="text-3xl tracking-widest flex-none font-medium leading-tight uppercase flex justify-between items-center"
            style={{
              fontFamily: "mono",
              color: "#CCFF00",
              padding: 56,
            }}
          >
            Blog
            <Logo width={260} />
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
        fonts: [
          {
            name: "display",
            data: fontData,
            style: "normal",
          },
          {
            name: "mono",
            data: monoFontData,
            style: "normal",
          },
        ],
      },
    );
  }

  return new ImageResponse(
    (
      <div
        style={{
          background: "white",
          color: "black",
          width: "100%",
          height: "100%",
        }}
        tw="items-center justify-center flex relative z-20"
      >
        <Logo width={440} />
        <div tw="absolute bottom-0 left-0 right-0 z-10 flex">
          <GenericFlux />
        </div>
      </div>
    ),
    {
      width: 1200,
      height: 630,
    },
  );
}

const DocsFlux = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg viewBox="0 0 249 630" {...props}>
      <path
        d="M249 0H79V36.168H0L0 205.918H79V249.665H0L0 271.832H79L79 353H249V0ZM249 369H79V425.833H0L0 502.25H79L79 564H249L249 369ZM248.999 572.873H81.1162V572.835H0.000976562L0.000976562 618.918H78.999V630H248.999V572.873Z"
        fill="currentColor"
      />
    </svg>
  );
};

const GenericFlux = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg viewBox="0 0 1163 143" {...props}>
      <path
        d="M6.00391 142.245H0.00390625L0.00292969 52.2451H6.00293L6.00391 142.245ZM33.0039 142.245H23.0039L23.0029 52.2451H33.0029L33.0039 142.245ZM58.0039 142.245H47.0039L47.0029 52.2451H58.0029L58.0039 142.245ZM84.0039 142.245H71.0039V52.2451H84.0029L84.0039 142.245ZM119.693 52.249H110.003L110.004 142.245H95.0039L95.0029 52.2451H109.29V0.00390625H119.693V52.249ZM145.816 52.2451H157.362V0.00390625H171.367V52.2451H181.814V0.00390625H196.79V52.2451H206.174V0.00390625H222.309L222.31 52.2451H230.144V0.00390625H248.2V52.2451H254.978V0.00390625H273.244V52.2451H280.318V0.00390625H297.78L297.781 52.2451H306.312V0.00390625H321.662L321.663 52.2451H332.206V0.00390625H345.635V52.249H334.003L334.004 142.245H320.004L320.003 52.249H310.003L310.004 142.245H295.004V52.249H286.003L286.004 142.245H269.004V52.249H262.003L262.004 142.245H243.004V52.249H237.003L237.004 142.245H218.004V52.249H212.003L212.004 142.245H193.004V52.249H187.003L187.004 142.245H168.004V52.249H161.003L161.004 142.245H144.004V52.249H136.003L136.004 142.245H120.004V52.2451H133.043V0.00390625H145.815L145.816 52.2451ZM369.324 52.249H359.003L359.004 142.245H346.004L346.003 52.2451H358.387V0.00390625H369.324V52.249ZM383.004 142.245H371.004V52.2451H383.003L383.004 142.245ZM407.004 142.245H397.004V52.2451H407.003L407.004 142.245ZM432.004 142.245H422.004V52.2451H432.003L432.004 142.245ZM456.004 142.245H448.004V52.2451H456.003L456.004 142.245ZM480.004 142.245H474.004V52.2451H480.003L480.004 142.245ZM505.004 142.245H499.004L499.003 52.2451H505.003L505.004 142.245ZM529.004 142.245H525.004L525.003 52.2451H529.003L529.004 142.245ZM553.004 142.245H550.004L550.003 52.2451H553.003L553.004 142.245ZM578.004 142.245H575.004L575.003 52.2451H578.003L578.004 142.245ZM603.004 142.245H600.004L600.003 52.2451H603.003L603.004 142.245ZM627.004 142.245H626.004V52.2451H627.003L627.004 142.245ZM652.004 142.245H650.004L650.003 52.2451H652.003L652.004 142.245ZM678.004 142.245H675.004L675.003 52.2451H678.003L678.004 142.245ZM703.004 142.245H700.004L700.003 52.2451H703.003L703.004 142.245ZM727.004 142.245H725.004V52.2451H727.003L727.004 142.245ZM752.004 142.245H750.004L750.003 52.2451H752.003L752.004 142.245ZM777.004 142.245H775.004L775.003 52.2451H777.003L777.004 142.245ZM802.004 142.245H800.004L800.003 52.2451H802.003L802.004 142.245ZM827.004 142.245H825.004V52.2451H827.003L827.004 142.245ZM852.004 142.245H850.004L850.003 52.2451H852.003L852.004 142.245ZM877.004 142.245H875.004L875.003 52.2451H877.003L877.004 142.245ZM902.004 142.245H900.004L900.003 52.2451H902.003L902.004 142.245ZM927.004 142.245H925.004L925.003 52.2451H927.003L927.004 142.245ZM952.004 142.245H950.004L950.003 52.2451H952.003L952.004 142.245ZM977.004 142.245H974.004L974.003 52.2451H977.003L977.004 142.245ZM1002 142.245H999.004L999.003 52.2451H1002L1002 142.245ZM1026 142.245H1024L1024 52.2451H1026L1026 142.245ZM1051 142.245H1049L1049 52.2451H1051L1051 142.245ZM1077 142.245H1074V52.2451H1077L1077 142.245ZM1102 142.245H1099L1099 52.2451H1102L1102 142.245ZM1126 142.245H1124L1124 52.2451H1126L1126 142.245ZM1151 142.245H1149L1149 52.2451H1151L1151 142.245ZM41.1211 52.249H38.248V0.00390625H41.1201L41.1211 52.249ZM68.0068 52.249H61.2373V0.00390625H68.0068V52.249ZM94.0908 52.249H85.0195V0.00390625H94.0908V52.249ZM393.944 52.249H383.643V0.00390625H393.943L393.944 52.249ZM418.32 52.249H409.136V0.00390625H418.32V52.249ZM442.607 52.249H434.725V0.00390625H442.606L442.607 52.249ZM465.86 52.249H461.342V0.00390625H465.859L465.86 52.249ZM490.095 52.249H486.978V0.00390625H490.095V52.249ZM514.08 52.249H512.874V0.00390625H514.079L514.08 52.249ZM813.451 52.249H811.979V0.00390625H813.451V52.249ZM838.303 52.249H837.003V0.00390625H838.302L838.303 52.249ZM863.327 52.249H861.844V0.00390625H863.327V52.249ZM913.165 52.249H911.751V0.00390625H913.164L913.165 52.249ZM938.003 52.249H936.783V0.00390625H938.003V52.249ZM963.041 52.249H961.619V0.00390625H963.04L963.041 52.249ZM987.976 52.249H986.556V0.00390625H987.976V52.249ZM1012.81 52.249H1011.59V0.00390625H1012.81V52.249ZM1037.82 52.249H1036.46V0.00390625H1037.82V52.249ZM1062.75 52.249H1061.4V0.00390625H1062.75L1062.75 52.249ZM1087.7 52.249H1086.33V0.00390625H1087.7L1087.7 52.249ZM1112.64 52.249H1111.26V0.00390625H1112.64L1112.64 52.249ZM1137.95 52.249H1135.81V0.00390625H1137.95V52.249ZM1162.89 52.2451H1160.75V0H1162.89L1162.89 52.2451Z"
        fill="#C5C5C5"
      />
    </svg>
  );
};

const ProductFlux = (props: React.SVGProps<SVGSVGElement>) => (
  <svg viewBox="0 0 1095 155" {...props}>
    <path
      d="M1.50391 154.137H0V76.5938H1.50391V154.137ZM25.9521 154.137H24.4482V76.5938H25.9521V154.137ZM50.3994 154.137H48.8955V76.5938H50.3994V154.137ZM75.2363 154.137H72.9648V76.5938H75.2363V154.137ZM99.6826 154.137H97.4111V76.5938H99.6826V154.137ZM124.129 154.137H121.857V76.5938H124.129V154.137ZM148.724 154.137H146.154V76.5938H148.724V154.137ZM173.172 154.137H170.602V76.5938H173.172V154.137ZM197.618 154.137H195.048V76.5938H197.618V154.137ZM222.44 154.137H219.118V76.5938H222.44V154.137ZM246.694 154.137H243.762V76.5938H246.694V154.137ZM270.934 154.137H268.416V76.5938H270.934V154.137ZM295.439 154.137H292.804V76.5938H295.439V154.137ZM319.778 154.137H317.36V76.5938H319.778V154.137ZM344.228 154.137H341.81V76.5938H344.228V154.137ZM368.674 154.137H366.254V76.5938H368.674V154.137ZM393.321 154.137H390.495V76.5938H393.321V154.137ZM417.55 154.137H415.162V76.5938H417.55V154.137ZM441.837 154.137H439.769V76.5938H441.837V154.137ZM466.397 154.137H464.102V76.5938H466.397V154.137ZM490.7 154.137H488.7V76.5938H490.7V154.137ZM515.146 154.137H513.146V76.5938H515.146V154.137ZM539.319 154.137H537.863V76.5938H539.319V154.137ZM564.036 154.137H562.038V76.5938H564.036V154.137ZM588.353 154.137H586.62V76.5938H588.353V154.137ZM612.674 154.137H611.186V76.5938H612.674V154.137ZM637.11 154.137H635.649V76.5938H637.11V154.137ZM710.344 154.137H709.1V76.5938H710.344V154.137ZM734.921 154.137H733.416V76.5938H734.921V154.137ZM759.271 154.137H757.96V76.5938H759.271V154.137ZM881.532 154.137H880.167V76.5938H881.532V154.137ZM18.6191 77.543H16.667V0H18.6191V77.543ZM43.0635 77.543H41.1113V0H43.0635V77.543ZM67.6729 77.543H65.4014V0H67.6729V77.543ZM92.1182 77.543H89.8467V0H92.1182V77.543ZM116.567 77.543H114.296V0H116.567V77.543ZM141.161 77.543H138.592V0H141.161V77.543ZM165.845 77.543H162.803V0H165.845V77.543ZM190.099 77.543H187.445V0H190.099V77.543ZM214.339 77.543H212.101V0H214.339V77.543ZM238.921 77.543H236.411V0H238.921V77.543ZM263.187 77.543H261.033V0H263.187V77.543ZM287.847 77.543H285.274V0H287.847V77.543ZM312.297 77.543H309.72V0H312.297V77.543ZM336.522 77.543H334.387V0H336.522V77.543ZM360.805 77.543H358.994V0H360.805V77.543ZM385.381 77.543H383.316V0H385.381V77.543ZM409.672 77.543H407.912V0H409.672V77.543ZM434.12 77.543H432.358V0H434.12V77.543ZM458.703 77.543H456.677V0H458.703V77.543ZM483.023 77.543H481.245V0H483.023V77.543ZM507.342 77.543H505.823V0H507.342V77.543ZM531.788 77.543H530.271V0H531.788V77.543ZM556.113 77.543H554.839V0H556.113V77.543ZM580.665 77.543H579.177V0H580.665V77.543ZM605.147 77.543H603.592V0H605.147V77.543ZM629.482 77.543H628.154V0H629.482V77.543ZM654.028 77.543H652.502V0H654.028V77.543ZM678.369 77.543H677.052V0H678.369V77.543ZM751.741 77.543H750.361V0H751.741V77.543ZM776.193 77.543H774.812V0H776.193V77.543ZM800.548 77.543H799.349V0H800.548V77.543ZM873.925 77.543H872.654V0H873.925V77.543ZM898.37 77.543H897.102V0H898.37V77.543ZM996.297 77.543H994.753V0H996.297V77.543ZM1094.13 77.543H1092.49V0H1094.13V77.543Z"
      fill="#2C1FEB"
    />
  </svg>
);

const BlogFlux = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    width="1200"
    height="426"
    viewBox="0 0 1200 426"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.7822 426.002H16.0059V364.727H16.7822V426.002ZM33.0859 426.002H32.3096V364.727H33.0859V426.002ZM49.3906 426.002H48.6143V364.727H49.3906V426.002ZM65.8428 426.002H64.7715V364.727H65.8428V426.002ZM82.2686 426.002H80.9658V364.727H82.2686V426.002ZM98.5723 426.002H97.2695V364.727H98.5723V426.002ZM114.876 426.002H113.573V364.727H114.876V426.002ZM131.184 426.002H129.881V364.727H131.184V426.002ZM147.591 426.002H146.076V364.727H147.591V426.002ZM164.055 426.002H162.225V364.727H164.055V426.002ZM180.359 426.002H178.529V364.727H180.359V426.002ZM196.636 426.002H194.866V364.727H196.636V426.002ZM212.798 426.002H211.305V364.727H212.798V426.002ZM229.106 426.002H227.613V364.727H229.106V426.002ZM245.285 426.002H244.039V364.727H245.285V426.002ZM261.854 426.002H260.082V364.727H261.854V426.002ZM278.049 426.002H276.504V364.727H278.049V426.002ZM294.205 426.002H292.955V364.727H294.205V426.002ZM310.505 426.002H309.259V364.727H310.505V426.002ZM326.794 426.002H325.584V364.727H326.794V426.002ZM343.28 426.002H341.708V364.727H343.28V426.002ZM359.487 426.002H358.112V364.727H359.487V426.002ZM375.874 426.002H374.337V364.727H375.874V426.002ZM392.004 426.002H390.809V364.727H392.004V426.002ZM408.232 426.002H407.196V364.727H408.231L408.232 426.002ZM424.537 426.002H423.501V364.727H424.536L424.537 426.002ZM441.023 426.002H439.624V364.727H441.023V426.002ZM457.331 426.002H455.933V364.727H457.331V426.002ZM473.382 426.002H472.484V364.727H473.382V426.002ZM489.687 426.002H488.788V364.727H489.687V426.002ZM505.988 426.002H505.101V364.727H505.988V426.002ZM522.293 426.002H521.404V364.727H522.293V426.002ZM538.554 426.002H537.759V364.727H538.554V426.002ZM554.924 426.002H553.996V364.727H554.924V426.002ZM571.229 426.002H570.3V364.727H571.229V426.002ZM587.468 426.002H586.671V364.727H587.468V426.002ZM603.837 426.002H602.912V364.727H603.837V426.002ZM636.469 426.002H635.499V364.727H636.469V426.002ZM652.712 426.002H651.862V364.727H652.712V426.002ZM669.074 426.002H668.111V364.727H669.074V426.002ZM864.648 426.002H863.86V364.727H864.648V426.002ZM881.581 426.002H879.543V364.727H881.581V426.002ZM898.344 426.002H895.39V364.727H898.344V426.002ZM915.748 426.002H910.594V364.727H915.748V426.002ZM932.48 426.002H926.475V364.727H932.48V426.002ZM949.148 426.002H942.413V364.727H949.148V426.002ZM965.663 426.002H958.512V364.727H965.663V426.002ZM982.782 426.002H974.001V364.727H982.782V426.002ZM999.713 426.002H989.676V364.727H999.713V426.002ZM1016.71 426.002H1005.29V364.727H1016.71V426.002ZM1033.28 426.002H1021.33V364.727H1033.28V426.002ZM1049.52 426.002H1037.71V364.727H1049.52V426.002ZM1065.19 426.002H1054.64V364.727H1065.19V426.002ZM1081.12 426.002H1071.33V364.727H1081.12V426.002ZM1097.11 426.002H1087.95V364.727H1097.11V426.002ZM1113.01 426.002H1104.66V364.727H1113.01V426.002ZM1128.54 426.002H1121.74V364.727H1128.54V426.002ZM1144.41 426.002H1138.48V364.727H1144.41V426.002ZM1159.96 426.002H1155.53V364.727H1159.96V426.002ZM1174.99 426.002H1173.11V364.727H1174.99V426.002ZM-8.18066 364.728H-8.96191V170.559H-8.18066V364.728ZM8.27051 364.728H7.19922V170.559H8.27051V364.728ZM24.5781 364.728H23.5068V170.559H24.5781V364.728ZM40.8779 364.728H39.8066V170.559H40.8779V364.728ZM57.1865 364.728H56.1152V170.559H57.1865V364.728ZM73.7676 364.728H72.1504V170.559H73.7676V364.728ZM513.876 364.728H512.505V170.559H513.876V364.728ZM530.184 364.728H528.81V170.559H530.184V364.728ZM546.489 364.728H545.117V170.559H546.489V364.728ZM563.018 364.728H561.194V170.559H563.018V364.728ZM579.135 364.728H577.692V170.559H579.135V364.728ZM595.439 364.728H593.996V170.559H595.439V364.728ZM611.745 364.728H610.305V170.559H611.745V364.728ZM628.044 364.728H626.612V170.559H628.044V364.728ZM644.144 364.728H643.122V170.559H644.144V364.728ZM660.693 364.728H659.183V170.559H660.693V364.728ZM677.054 364.728H675.428V170.559H677.054V364.728ZM693.299 364.728H691.799V170.559H693.299V364.728ZM709.384 364.728H708.321V170.559H709.384V364.728ZM725.742 364.728H724.571V170.559H725.742V364.728ZM742.31 364.728H740.619V170.559H742.31V364.728ZM758.613 364.728H756.923V170.559H758.613V364.728ZM774.677 364.728H773.466V170.559H774.677V364.728ZM790.981 364.728H789.771V170.559H790.981V364.728ZM807.641 364.728H805.73V170.559H807.641V364.728ZM823.96 364.728H822.018V170.559H823.96V364.728ZM89.9053 170.547H90.0713V364.716H88.4541V170.547H88.8281V0H89.9053V170.547ZM106.255 170.547H106.375V364.716H104.758V170.547H105.085V0H106.255V170.547ZM122.559 170.547H122.684L122.685 364.716H121.062V170.547H121.389V0H122.559V170.547ZM138.976 170.547H139.091V364.716H137.261V170.547H137.588V0H138.975L138.976 170.547ZM155.28 170.547H155.562V364.716H153.396V170.547H153.893V0H155.279L155.28 170.547ZM171.651 170.547H171.742V364.716H169.835V170.547H170.133V0H171.65L171.651 170.547ZM187.802 170.547H188.006V364.716H186.177V170.547H186.584V0H187.802V170.547ZM204.105 170.547H204.311V364.716H202.481V170.547H202.888V0H204.105V170.547ZM220.518 170.552H220.493V364.716H218.907V170.547H219.096V0H220.518V170.552ZM236.905 170.547H236.979V364.716H235.034V170.547H235.315V0H236.905V170.547ZM253.212 170.547H253.264V364.716H251.359V170.547H251.62V0H253.212V170.547ZM269.516 170.547H269.567V364.716H267.663V170.547H267.924V0H269.516V170.547ZM285.74 170.552H285.62V364.716H284.224V170.547H284.312V0H285.74V170.552ZM302.166 170.552H301.929V364.716H300.527V170.552H300.494V0H302.166V170.552ZM318.471 170.552H318.411V364.716H316.655V170.547H316.798V0H318.471V170.552ZM334.69 170.547H334.716V364.716H332.96V170.547H333.194V0H334.69V170.547ZM351.208 170.552H350.927V364.716H349.355V170.552H349.28V0H351.208V170.552ZM367.433 170.552H367.07V364.716H365.819V170.552H365.673V0H367.432L367.433 170.552ZM383.736 170.552H383.378V364.716H382.123V170.552H381.977V0H383.735L383.736 170.552ZM400.04 170.552H399.884V364.716H398.23V170.547H398.28V0H400.04V170.552ZM416.577 170.552H416.188V364.716H414.534V170.552H414.357V0H416.577V170.552ZM432.964 170.552H432.242V364.716H431.086V170.552H430.582V0H432.964V170.552ZM449.001 170.552H448.625V364.716H447.318V170.552H447.15V0H449.001V170.552ZM465.306 170.552H464.93V364.716H463.623V170.552H463.454V0H465.306V170.552ZM481.856 170.552H481.452V364.716H479.713V170.552H479.52V0H481.856V170.552ZM498.237 170.552H497.679V364.716H496.092V170.552H495.748V0H498.237V170.552ZM841.13 170.552H840.143V364.716H838.438V170.552H837.662V0H841.13V170.552ZM857.489 170.552H856.944V364.716H854.247V170.552H853.92V0H857.489V170.552ZM873.793 170.547H873.878V364.716H869.926V170.547H870.22V0H873.793V170.547ZM889.811 170.547H890.309V364.716H886.108V170.547H886.813V0H889.81L889.811 170.547ZM906.47 170.547H907.061V364.716H901.963V170.547H902.765V0H906.47V170.547ZM922.895 170.547H924.086V364.716H917.545V170.547H918.951V0H922.895V170.547ZM938.956 170.547H940.588V364.716H933.656V170.547H935.499V0H938.956V170.547ZM955.794 170.547H957.271V364.716H949.587V170.547H951.274V0H955.793L955.794 170.547ZM972.42 170.547H974.071V364.716H965.392V170.547H967.255V0H972.42V170.547ZM988.423 170.547H990.797V364.716H981.284V170.547H983.865V0H988.423V170.547ZM1005.48 170.547H1007.21V364.716H997.479V170.547H999.418V0H1005.48V170.547ZM1021.42 170.547H1024.22V364.716H1013.07V170.547H1016.09V0H1021.42V170.547ZM1038.13 170.547H1041.32V364.716H1028.59V170.547H1031.99V0H1038.13V170.547ZM1054.89 170.547H1057.65V364.716H1044.87V170.547H1047.83V0H1054.89V170.547ZM1071.22 170.547H1073.61V364.716H1061.52V170.547H1064.12L1064.12 0H1071.22L1071.22 170.547ZM1087.58 170.547H1089.9V364.716H1077.84V170.547H1080.37L1080.37 0H1087.58L1087.58 170.547ZM1104.36 170.547H1105.73V364.716H1094.62V170.547H1096.2L1096.2 0H1104.36L1104.36 170.547ZM1121.11 170.547H1121.62V364.716H1111.33V170.547H1112.06L1112.05 0H1121.11L1121.11 170.547ZM1137.78 170.547H1137.8V364.716H1127.77V170.547H1128V0H1137.78V170.547ZM1153.74 170.552H1153.38V364.716H1144.79V170.552H1144.65L1144.65 0H1153.73L1153.74 170.552ZM1169.65 170.552H1168.98V364.716H1161.81V170.552H1161.35V0H1169.65V170.552ZM1185.51 170.552H1184.89V364.716H1178.51V170.552H1178.1V0H1185.51V170.552ZM1200 170.552H1199.96V364.716H1196.05V170.547H1196.22V0H1200V170.552ZM-7.92871 170.552H-9V0H-7.92871V170.552ZM8.375 170.552H7.30371V0H8.375V170.552ZM24.6787 170.552H23.6074V0H24.6787V170.552ZM41.1475 170.552H39.7568V0H41.1475V170.552ZM57.4512 170.552H56.0605V0H57.4512V170.552ZM73.7402 170.552H72.3818V0H73.7402V170.552ZM514.461 170.552H512.132V0H514.461V170.552ZM530.576 170.552H528.629V0H530.576V170.552ZM547.21 170.552H544.605V0H547.209L547.21 170.552ZM563.513 170.552H560.909V0H563.513V170.552ZM579.82 170.552H577.214V0H579.82V170.552ZM595.858 170.552H593.782V0H595.858V170.552ZM612.49 170.552H609.767V0H612.49V170.552ZM628.796 170.552H626.071V0H628.796V170.552ZM645.098 170.552H642.375V0H645.098V170.552ZM661.699 170.552H658.385V0H661.698L661.699 170.552ZM677.771 170.552H674.924V0H677.771V170.552ZM694.078 170.552H691.232V0H694.078V170.552ZM710.438 170.552H707.478V0H710.438V170.552ZM726.819 170.552H723.706V0H726.819V170.552ZM743.056 170.552H740.077V0H743.056V170.552ZM759.416 170.552H756.327V0H759.416V170.552ZM775.722 170.552H772.635V0H775.722V170.552ZM792.104 170.552H788.863V0H792.104V170.552ZM808.415 170.552H805.159V0H808.415V170.552ZM824.738 170.552H821.446V0H824.738V170.552Z"
      fill="#3F672F"
    />
  </svg>
);
