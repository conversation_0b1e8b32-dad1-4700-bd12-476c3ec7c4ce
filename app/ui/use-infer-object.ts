import { withErrorTiming } from "#/utils/btapi/type-error";
import { useQuery } from "@tanstack/react-query";
import * as Query from "#/utils/btql/query-builder";
import {
  dataObjectPageShape,
  fetchBtql,
  fetchInferBtql,
  rowWithIdsSchema,
} from "#/utils/btql/btql";
import { useBtqlFlags, useIsFeatureEnabled } from "#/lib/feature-flags";
import { useOrg } from "#/utils/user";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { useSessionToken } from "#/utils/auth/session-token";
import { useMemo } from "react";
import { makeRowIdPrimary, type RowId } from "#/utils/diffs/diff-objects";
import z from "zod";
import { unfoldNestedFields } from "#/utils/queries/metadata";
import { type NestedField } from "@braintrust/local/query";
import { isReservedMetadataPath } from "#/utils/metadata";

export function useInferRowData({
  objectType,
  objectId,
  rowId,
}: {
  objectType: DataObjectType;
  objectId: string | null;
  rowId?: RowId | null;
}) {
  const org = useOrg();
  const btqlFlags = useBtqlFlags();
  const { getOrRefreshToken } = useSessionToken();
  const hasSchemaInference = useIsFeatureEnabled("schemaInference");

  const shape = dataObjectPageShape(objectType);
  const primaryRowId = rowId && makeRowIdPrimary(rowId);

  const { data: inferRowData } = useQuery({
    queryKey: ["inferObjectSchemaFirstRow", objectType, objectId, shape],
    queryFn: withErrorTiming(
      async ({ signal }: { signal: AbortSignal }) =>
        objectId && primaryRowId
          ? await fetchBtql({
              args: {
                query: {
                  from: Query.from(objectType, [objectId], shape),
                  select: [{ op: "star" }],
                  sort: [{ expr: { btql: "_pagination_key" }, dir: "desc" }],
                  filter: {
                    op: "eq",
                    left: { op: "ident", name: ["id"] },
                    right: { op: "literal", value: primaryRowId },
                  },
                  ...(shape === "summary" ? { preview_length: -1 } : {}),
                },
                brainstoreRealtime: true,
              },
              btqlFlags,
              apiUrl: org.api_url,
              getOrRefreshToken,
              schema: rowWithIdsSchema,
              signal,
            })
          : null,
      "Infer object schema (first row data)",
      {
        objectType,
        shape,
      },
    ),
    enabled: !!objectId && hasSchemaInference && !!primaryRowId,
    gcTime: 1000 * 30,
    staleTime: 1000 * 60,
  });

  return inferRowData;
}

export function useInferObjectSchemaPaths({
  objectType,
  objectId,
  rowId,
  paths,
}: {
  objectType: DataObjectType;
  objectId: string | null;
  rowId?: RowId | null;
  paths: string[];
}) {
  const org = useOrg();
  const btqlFlags = useBtqlFlags();
  const { getOrRefreshToken } = useSessionToken();
  const hasSchemaInference = useIsFeatureEnabled("schemaInference");

  const inferRowData = useInferRowData({
    objectType,
    objectId,
    rowId,
  });

  const inferPaths = useMemo(() => {
    return [
      "input",
      "expected",
      "metadata",
      ...(objectType !== "dataset" ? ["output"] : []),
    ];
  }, [objectType]);

  const { data: inferSchemaData } = useQuery({
    queryKey: ["inferObjectSchema", objectType, objectId],
    queryFn: withErrorTiming(
      async ({ signal }: { signal: AbortSignal }) =>
        objectId
          ? await fetchInferBtql({
              args: {
                query: {
                  from: Query.from(objectType, [objectId]),
                  filter: {
                    op: "eq",
                    left: { op: "ident", name: ["is_root"] },
                    right: { op: "literal", value: true },
                  },
                  infer: inferPaths.map((path) => ({
                    op: "ident" as const,
                    name: [path],
                  })),
                  sort: [{ expr: { btql: "_pagination_key" }, dir: "desc" }],
                },
                brainstoreRealtime: true,
              },
              btqlFlags,
              apiUrl: org.api_url,
              getOrRefreshToken,
              signal,
            })
          : null,
      "Infer object schema",
      {
        objectType,
      },
    ),
    enabled: !!objectId && hasSchemaInference,
    gcTime: 1000 * 30,
    staleTime: 1000 * 60,
  });

  const placeholderSchema: NestedField[] | undefined = useMemo(() => {
    const firstRowData = inferRowData?.data[0];
    if (!firstRowData) {
      return undefined;
    }
    const all = paths.flatMap((path) => {
      try {
        const firstRowPlaceholder = firstRowData[path];
        const data = z.record(z.unknown()).nullish().parse(firstRowPlaceholder);
        return data ? unfoldNestedFields([path], data) : [];
      } catch {
        return [];
      }
    });
    return all;
  }, [inferRowData, paths]);

  return useMemo(() => {
    if (!inferSchemaData) {
      return placeholderSchema;
    }

    const inferredFields = inferSchemaData.data.reduce<string[][]>(
      (acc, { name }) => {
        if (paths.includes(name[0]) && !isReservedMetadataPath(name)) {
          acc.push(name);
        }
        return acc;
      },
      [],
    );

    if (!placeholderSchema) {
      return inferredFields;
    }

    const existingSet = new Set(
      inferredFields.map((field) => JSON.stringify(field)),
    );
    const merged = [...inferredFields];

    for (const path of placeholderSchema) {
      const pathString = JSON.stringify(path);
      const isDuplicate = existingSet.has(pathString);

      if (!isReservedMetadataPath(path) && !isDuplicate) {
        merged.push(path);
        existingSet.add(pathString);
      }
    }

    return merged;
  }, [paths, inferSchemaData, placeholderSchema]);
}
