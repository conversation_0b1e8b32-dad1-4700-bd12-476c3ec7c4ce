import { type SVGProps } from "react";

interface Props extends SVGProps<SVGSVGElement> {
  width?: number | string;
}

export const Logo = ({ className, width = 220, ...props }: Props) => {
  return (
    <svg
      fill="none"
      width={width}
      height={typeof width === "number" ? (width * 18) / 120 : undefined}
      viewBox="0 0 120 18"
      className={className}
      {...props}
    >
      <path
        d="M41.3807 4.48606H44.636V17.6822H41.3807V4.48606ZM35.54 7.60128H38.2878V17.6822H35.0598V15.8618C34.5267 16.996 32.7922 17.9459 30.9774 17.9459C27.189 17.9459 24.4668 15.1738 24.4668 11.3216C24.4668 7.46943 27.5889 4.48606 31.4833 4.48606H35.0051V7.07221C35.0051 7.36295 35.2187 7.59959 35.5383 7.59959L35.54 7.60128ZM35.0051 8.26049C35.0051 7.94441 34.7915 7.73312 34.472 7.73312H31.5362C29.3216 7.73312 27.934 9.131 27.934 11.2692C27.934 13.2486 29.2139 14.7259 31.4559 14.7259C33.3766 14.7259 35.0051 13.3804 35.0051 11.2422V8.26049ZM15.6887 8.81491V17.6822H18.944V8.1557C18.944 7.83961 19.1576 7.62832 19.4771 7.62832H23.932V4.48775H19.6634C16.8079 4.48775 15.6887 5.93971 15.6887 8.8166V8.81491ZM13.8209 10.8467C13.8209 14.7005 10.6989 17.6822 6.80451 17.6822H3.28264V15.0961C3.28264 14.78 3.06904 14.5687 2.74949 14.5687H0V0H3.28093V6.17466C3.86705 4.98638 5.49556 4.22237 7.31032 4.22237C11.0988 4.22237 13.8209 6.99446 13.8209 10.8467ZM10.352 10.8991C10.352 8.91971 9.07211 7.44239 6.83014 7.44239C4.90943 7.44239 3.28093 8.78787 3.28093 10.9261V13.9078C3.28093 14.2239 3.49453 14.4352 3.81408 14.4352H6.74983C8.96446 14.4352 10.352 13.0373 10.352 10.8991ZM56.4012 4.48606H51.7054C48.8243 4.48606 47.7034 5.93802 47.7034 8.81491V17.6822H50.9586V8.1557C50.9586 7.83961 51.1722 7.62832 51.4918 7.62832H56.5875C56.907 7.62832 57.1206 7.83961 57.1206 8.1557V17.6822H60.3759V8.81491C60.3759 6.17635 59.4959 4.48606 56.4012 4.48606ZM104.961 9.76486L102.72 9.36933C101.494 9.15804 101.013 8.76251 101.013 8.04921C101.013 7.3359 101.76 6.67668 103.308 6.67668C104.962 6.67668 105.87 7.49479 105.923 8.68307H109.178C108.937 5.75378 106.775 4.16997 103.334 4.16997C99.8923 4.16997 97.7307 5.88562 97.7307 8.39234C97.7307 10.8991 99.6514 11.7966 102.052 12.1921L104.159 12.5353C105.494 12.7466 106.241 13.2486 106.241 14.0667C106.241 14.9913 105.147 15.4916 103.626 15.4916C101.919 15.4916 100.798 14.6211 100.69 13.301H97.3018C97.541 16.521 100.263 18 103.624 18C106.986 18 109.494 16.5227 109.494 13.8047C109.494 11.3774 107.76 10.2162 104.957 9.76655L104.961 9.76486ZM92.0198 14.0143C92.0198 14.3304 91.8062 14.5416 91.4867 14.5416H86.6576C86.338 14.5416 86.1244 14.3304 86.1244 14.0143V4.48606H82.8691V13.328C82.8691 15.9666 83.7491 17.6822 86.8438 17.6822H91.2731C94.1541 17.6822 95.2751 16.2303 95.2751 13.3534V4.48606H92.0198V14.0126V14.0143ZM73.0246 8.81491V17.6822H76.2799V8.1557C76.2799 7.83961 76.4935 7.62832 76.8131 7.62832H81.0014V4.48775H76.9993C74.1439 4.48775 73.0246 5.93971 73.0246 8.8166V8.81491ZM67.582 0H64.3267V4.48606H61.8729V7.49479H64.3267V13.6441C64.3267 16.6528 65.7399 17.6822 68.4091 17.6822H70.7843V14.6735H69.4498C68.1152 14.6735 67.582 14.092 67.582 12.853V7.49648H71.2098V4.48775H67.582V0ZM41.2218 3.08818H44.771V0H41.2218V3.08818ZM116.372 4.48606V0H113.117V4.48606H110.663V7.49479H113.117V13.6441C113.117 16.6528 114.53 17.6822 117.199 17.6822H119.575V14.6735H118.24C116.905 14.6735 116.372 14.092 116.372 12.853V7.49648H120V4.48775H116.372V4.48606Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const Icon = ({ size = 24 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 100 100" fill="none">
    <path
      d="M14 50C14 30.1178 30.1178 14 50 14C69.8823 14 86 30.1178 86 50C86 69.8823 69.8823 86 50 86H20.0546C16.7107 86 14 83.2893 14 79.9454C14 69.9636 14 59.9818 14 50Z"
      fill="currentColor"
    />
  </svg>
);
