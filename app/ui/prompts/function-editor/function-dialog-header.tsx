import { CopyToClipboardButton } from "#/ui/copy-to-clipboard-button";
import { DialogHeader, DialogTitle } from "#/ui/dialog";
import { type FunctionObjectType } from "@braintrust/core/typespecs";
import { Bolt, MessageCircle, Percent, Route } from "lucide-react";
import { type UIFunction } from "#/ui/prompts/schema";
import { prettifyXact } from "@braintrust/core";
import { TransactionIdField } from "@braintrust/local/query";
import { type Mode } from "./types";
import { Skeleton } from "#/ui/skeleton";
import { NIL as NIL_UUID } from "uuid";
import { cn } from "#/utils/classnames";

export function FunctionDialogHeader({
  title,
  type,
  sourcePrompt,
  mode,
}: {
  title?: string;
  type: FunctionObjectType;
  sourcePrompt?: UIFunction | null;
  mode: Mode;
}) {
  const isUpdate = mode.type === "update";
  const isReadOnly = mode.type === "view_saved" || mode.type === "view_unsaved";
  const hasSavedVersions = mode.type === "update" || mode.type === "view_saved";

  const dialogTitle = (() => {
    if (title) return title;
    if (sourcePrompt?.name) return sourcePrompt.name;
    if (isReadOnly) return `Untitled ${type}`;
    if (isUpdate && !sourcePrompt?.name)
      return <Skeleton className="h-[18px] w-1/4" />;
    return `Create ${type}`;
  })();
  const idText = sourcePrompt?.id ?? NIL_UUID;
  const renderedId = sourcePrompt?.id ?? (
    <Skeleton className="h-[18px] w-[260px]" />
  );
  const versionNumberText = prettifyXact(
    sourcePrompt?.[TransactionIdField] ?? "0",
  );
  const renderedVersionNumber = sourcePrompt?.[TransactionIdField] ? (
    versionNumberText
  ) : (
    <Skeleton className="h-[18px] w-[116px]" />
  );

  return (
    <DialogHeader className="mb-5 flex-none">
      <DialogTitle className="mt-1 flex min-h-6 flex-wrap items-center gap-2">
        {type === "scorer" && (
          <Percent className="size-4 text-lime-600 dark:text-lime-400" />
        )}
        {type === "prompt" && (
          <MessageCircle className="size-4 text-cyan-600 dark:text-cyan-400" />
        )}
        {type === "tool" && (
          <Bolt className="size-4 text-amber-600 dark:text-amber-400" />
        )}
        {type === "agent" && <Route className="size-4 text-bad-600" />}
        <div className="flex flex-1">{dialogTitle}</div>
        {FunctionDialogHeaderMetadata({
          hasSavedVersions,
          idText,
          versionNumberText,
          sourcePrompt,
          renderedId,
          renderedVersionNumber,
        })}
      </DialogTitle>
    </DialogHeader>
  );
}

export const FunctionDialogHeaderMetadata = ({
  hasSavedVersions,
  idText,
  versionNumberText,
  sourcePrompt,
  renderedId,
  renderedVersionNumber,
  className,
}: {
  hasSavedVersions: boolean;
  idText: string;
  versionNumberText: string;
  sourcePrompt?: UIFunction | null;
  renderedId: React.ReactNode;
  renderedVersionNumber: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("-mx-2 flex w-full flex-wrap gap-1", className)}>
      {hasSavedVersions && (
        <CopyToClipboardButton
          size="xs"
          variant="ghost"
          copyMessage="Copy ID to clipboard"
          textToCopy={idText}
          className="font-mono text-xs font-medium text-primary-500"
          disabled={!sourcePrompt?.id}
        >
          <span className="font-inter text-[10px] uppercase tracking-wider opacity-60">
            ID
          </span>
          {renderedId}
        </CopyToClipboardButton>
      )}
      {hasSavedVersions && (
        <CopyToClipboardButton
          size="xs"
          variant="ghost"
          copyMessage="Copy version to clipboard"
          textToCopy={versionNumberText}
          className="font-mono text-xs font-medium text-primary-500"
          disabled={!sourcePrompt?.[TransactionIdField]}
        >
          <span className="font-inter text-[10px] uppercase tracking-wider opacity-60">
            Version
          </span>
          {renderedVersionNumber}
        </CopyToClipboardButton>
      )}
    </div>
  );
};
