import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useMemo,
} from "react";
import { Button } from "./button";
import { Checkbox } from "./checkbox";
import { SearchIcon } from "lucide-react";
import { useFeatureFlags } from "#/lib/feature-flags";
import { type Search, type Clause } from "#/utils/search/search";
import { Bubble } from "./table/bubble";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { Input } from "./input";
import { Spinner } from "./icons/spinner";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { cn } from "#/utils/classnames";
import { pluralizeWithCount } from "#/utils/plurals";
import { isEmpty } from "@braintrust/core";

export interface AdvancedSearchState {
  disableScroll: boolean;
  setDisableScroll: Dispatch<SetStateAction<boolean>>;
  searchMatch: Clause<"match"> | undefined;
  setSearchMatch: (clause: Clause<"match"> | undefined) => void;
}
export function useAdvancedSearchState({
  search: searchProp,
  setSearch: setSearchProp,
}: {
  search: Search;
  setSearch: React.Dispatch<React.SetStateAction<Search>>;
}): AdvancedSearchState {
  const [disableScroll, setDisableScroll] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: "disableInfiniteScroll-global",
    key: "disableInfiniteScroll",
    defaultValue: false,
  });

  const searchMatch = useMemo(() => {
    return searchProp?.match?.[0];
  }, [searchProp]);
  const setSearchMatch = useCallback(
    (clause: Clause<"match"> | undefined) => {
      setSearchProp((s) => {
        return {
          ...s,
          match: clause && clause.text.length > 0 ? [clause] : [],
        };
      });
    },
    [setSearchProp],
  );

  return useMemo(
    () => ({
      disableScroll,
      setDisableScroll,
      searchMatch,
      setSearchMatch,
    }),
    [disableScroll, searchMatch, setDisableScroll, setSearchMatch],
  );
}

export const AdvancedSearchComponents = ({
  disableScroll,
  setDisableScroll,
  searchMatch,
  setSearchMatch,
  objectType,
  isSearchTransitioning,
  totalCount,
}: AdvancedSearchState & {
  objectType: DataObjectType;
  totalCount?: number;
  isSearchTransitioning: boolean;
}) => {
  const {
    flags: { advancedSearch, brainstore },
  } = useFeatureFlags();

  if (!brainstore) {
    return null;
  }

  return (
    <div className="flex flex-1 gap-2">
      <div className="relative flex flex-1 gap-2">
        {isSearchTransitioning ? (
          <Spinner className="absolute left-2 top-2 !size-3 text-primary-500" />
        ) : (
          <SearchIcon className="pointer-events-none absolute left-2 top-1/2 size-3 -translate-y-1/2 text-primary-500" />
        )}
        <Input
          className={cn(
            "h-7 flex-1 border-none pl-7 text-xs transition-colors bg-primary-50 hover:bg-primary-100 focus-visible:ring-0 focus-visible:bg-primary-100",
            {
              "pr-7": advancedSearch,
            },
          )}
          placeholder={`Search ${objectType === "project_logs" ? (!isEmpty(totalCount) ? pluralizeWithCount(totalCount, "log") : "logs") : objectType}`}
          value={searchMatch?.text ?? ""}
          onChange={(e) => {
            if (e.target.value === "") {
              setSearchMatch(undefined);
              return;
            }
            setSearchMatch({
              type: "match",
              text: e.target.value,
              label: e.target.value,
              originType: "btql",
              bubble: new Bubble({
                type: "match",
                hidden: true,
                label: e.target.value,
                clear: () => {
                  setSearchMatch(undefined);
                },
              }),
            });
          }}
        />
      </div>
      {advancedSearch && (
        <Button
          size="xs"
          variant="ghost"
          className="gap-2 text-primary-500"
          onClick={() => setDisableScroll(!disableScroll)}
        >
          <Checkbox
            inputClassName="!size-3"
            checked={!disableScroll}
            readOnly
          />
          Infinite scroll
        </Button>
      )}
    </div>
  );
};
