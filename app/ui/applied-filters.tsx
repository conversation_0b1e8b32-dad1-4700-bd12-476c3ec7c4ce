import { type Dispatch, type SetStateAction, useMemo } from "react";
import { type ClientOptions } from "openai";

import { type AISearchResult } from "@braintrust/local";
import type {
  Search,
  ClauseChe<PERSON>,
  Clause,
  ClauseType,
} from "#/utils/search/search";
import { type BubbleType, BubbleChip } from "./table/bubbles";
import { getBubbles } from "#/utils/search/search-bubbles";
import { cn } from "#/utils/classnames";
import { Button } from "./button";
import { Code } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";
import { CopyToClipboardButton } from "./copy-to-clipboard-button";
import { BasicTooltip } from "./tooltip";

export default function AppliedFilters({
  className,
  clauseChecker,
  setSearch,
  runAISearch,
  search,
  extraBubbles,
  baseExperiment,
  comparisonExperiments,
  fromClause,
  onClear,
  onClearAll,
  disableFullBtqlQueryHint,
  disabled,
  isPlayground,
  scoreNames,
  matchesLast,
  hideMatchQueries,
}: {
  className?: string;
  search: Search;
  extraBubbles?: BubbleType[];
  clauseChecker: ClauseChecker | null;
  setSearch: Dispatch<SetStateAction<Search>>;
  onClear?: (clause: Clause<ClauseType>) => void;
  onClearAll?: () => void;
  runAISearch?: (
    openAIOpts: ClientOptions,
    query: string,
  ) => Promise<AISearchResult>;
  baseExperiment?: {
    id: string;
    name: string;
  };
  comparisonExperiments?: {
    id: string;
    name: string;
  }[];
  fromClause: string;
  disableFullBtqlQueryHint?: boolean;
  disabled?: boolean;
  isPlayground?: boolean;
  scoreNames?: string[];
  matchesLast?: boolean;
  hideMatchQueries?: boolean;
}) {
  const bubbles = useMemo(() => {
    return getBubbles({
      search,
      setSearch,
      comparisonExperiments,
      onClear,
      hideMatchQueries,
    })
      .concat(extraBubbles ?? [])
      .sort((a, b) => {
        if (matchesLast) {
          if (a.type === "match" && b.type !== "match") {
            return 1;
          }
          if (a.type !== "match" && b.type == "match") {
            return -1;
          }
        }
        return a.id - b.id;
      });
  }, [
    search,
    extraBubbles,
    setSearch,
    comparisonExperiments,
    onClear,
    matchesLast,
    hideMatchQueries,
  ]);

  const fullBtqlQuery = useMemo(() => {
    const filters = bubbles.filter((b) => b.type === "filter");
    const sorts = bubbles.filter((b) => b.type === "sort");
    if (filters.length === 0 && sorts.length === 0) return null;
    return `select: *
from: ${fromClause}
${filters.length ? `filter: ${filters.map((b) => b.text).join(" AND ")}` : ""}
${sorts.length ? `sort: ${sorts.map((b) => b.text).join(" AND ")}` : ""}`;
  }, [bubbles, fromClause]);

  if (bubbles.length === 0) return null;

  return (
    <div
      className={cn(
        "relative mb-2 flex min-h-7 w-full max-w-full flex-none flex-wrap overflow-hidden gap-x-2 gap-y-1 @2xl/controls:flex-1",
        className,
      )}
    >
      {bubbles.map(
        ({
          id,
          type,
          clear,
          text,
          label,
          originType,
          hidden,
          comparisonId,
        }) => {
          return (
            <BubbleChip
              key={id}
              type={type}
              text={text}
              comparisonId={comparisonId}
              baseExperiment={baseExperiment}
              comparisonExperiments={comparisonExperiments}
              originType={originType}
              onClose={clear}
              runAISearch={runAISearch}
              setSearch={setSearch}
              clauseChecker={clauseChecker}
              disabled={disabled}
              label={label}
              hidden={hidden}
              isPlayground={isPlayground}
              scoreNames={scoreNames}
            />
          );
        },
      )}
      {bubbles.length > 0 && (
        <BasicTooltip content="Clear all filters">
          <Button
            className="text-primary-400"
            variant="ghost"
            size="xs"
            onClick={() => {
              setSearch({});
              onClearAll?.();
            }}
          >
            Clear
          </Button>
        </BasicTooltip>
      )}
      {fullBtqlQuery && !disableFullBtqlQueryHint && (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="xs"
              Icon={Code}
              className="text-primary-400"
            />
          </PopoverTrigger>
          <PopoverContent
            align="start"
            onOpenAutoFocus={(e) => e.preventDefault()}
          >
            <div className="mb-1 flex items-center justify-between text-xs text-primary-500">
              Full BTQL query
              <CopyToClipboardButton
                textToCopy={fullBtqlQuery}
                className="text-primary-400"
                variant="ghost"
                size="xs"
              />
            </div>
            <div className="whitespace-pre-wrap break-words font-mono text-xs leading-normal text-emerald-800 dark:text-emerald-200">
              {fullBtqlQuery}
            </div>
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
}
