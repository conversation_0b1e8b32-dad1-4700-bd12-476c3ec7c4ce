import {
  createParser,
  createSerializer,
  parseAsArrayOf,
  parseAsBoolean,
  parseAsInteger,
  parseAsJson,
  parseAsString,
  parseAsStringLiteral,
  useQueryState,
  useQueryStates,
} from "nuqs";
import type { RowId } from "#/utils/diffs/diff-objects";
import { z } from "zod";
import { urlSearchSchema } from "#/utils/search/search";
import {
  createDiffObjectSchema,
  type DiffObjectType,
} from "#/utils/diffs/diff-objects";
import { decodeURIComponentPatched } from "#/utils/url";
import {
  type RegressionFilter,
  regressionFiltersSchema,
} from "#/app/app/[org]/p/[project]/experiments/[experiment]/regressions-query";
import {
  GROUP_BY_NONE,
  GROUP_BY_NONE_VALUE,
  type SelectionType,
  selectionTypeSchema,
} from "./charts/selectionTypes";
import { useTransition } from "react";
import { tableLayoutTypes } from "./table/layout-type-control";

export const DiffModeOptions = z.enum([
  "between_experiments",
  "expected_output",

  // deprecated
  "off",
]);
export type DiffMode = z.infer<typeof DiffModeOptions>;
export type DiffModeState = {
  enabled: boolean;
  // Keep track of the last non-off diff mode option we had selected, so toggling diff mode on and off
  // will not change diff mode types
  enabledValue: DiffMode;
};

export const noopParser = createParser({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  parse(): any {
    return null;
  },
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  serialize(_: any) {
    return "";
  },
});

export const parseDiffModeState = createParser({
  parse(queryValue): DiffModeState {
    const parts = queryValue.split("|");
    if (parts.length < 2) {
      const parsed = DiffModeOptions.safeParse(parts[0]);
      return {
        enabled: parsed.success && parsed.data !== "off",
        enabledValue:
          !parsed.success || parsed.data === "off"
            ? ("between_experiments" as const)
            : parsed.data,
      };
    }

    const parsed = DiffModeOptions.safeParse(parts[1]);
    return {
      enabled: false,
      enabledValue:
        !parsed.success || parsed.data === "off"
          ? ("between_experiments" as const)
          : parsed.data,
    };
  },
  serialize({ enabled, enabledValue }: DiffModeState) {
    return enabled ? enabledValue : `off|${enabledValue}`;
  },
});

const defaultDiffModeState: DiffModeState = {
  enabled: false,
  enabledValue: "between_experiments",
};
export const useDiffModeState = () =>
  useQueryState("diff", parseDiffModeState.withDefault(defaultDiffModeState));

const BooleanOptions = ["1"] as const;
export const useHumanReviewState = () =>
  useQueryState("review", parseAsStringLiteral(BooleanOptions));

const rowDiffObjectSchema = createDiffObjectSchema(z.string());

function parseJsonEncodedValues<T>(
  query: string,
  parser?: (value: unknown) => T,
) {
  const obj = JSON.parse(query, (_, v) =>
    v && typeof v === "string" ? decodeURIComponentPatched(v) : v,
  );
  if (typeof parser === "function") {
    return parser(obj);
  }
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  return obj as T;
}
// encode in case values have escaped characters like \" which don't render as links well in slack
// encoding the entire json string is too long
// https://github.com/47ng/nuqs/commit/65ad3c9788fdec910eade62411eb50ec4fd2a64f
// copied from https://github.com/47ng/nuqs/blob/b128078203e7ec66762f650e3bd645df17eada43/packages/nuqs/src/parsers.ts#L334
export function parseAsJsonEncoded<T>(parser: (value: unknown) => T) {
  const jsonParser = parseAsJson(parser);
  return {
    ...jsonParser,
    parse: (query: string) => {
      try {
        return parseJsonEncodedValues(query, parser);
      } catch {}

      try {
        // for backwards compatibility reasons since we changed the encoding scheme
        return parseJsonEncodedValues(decodeURIComponentPatched(query), parser);
      } catch {}

      return null;
    },
    serialize: (value: T) =>
      JSON.stringify(value, (_, v) =>
        v && typeof v === "string" ? encodeURIComponent(v) : v,
      ),
  };
}

export function activeRowParserParse(queryValue: string) {
  try {
    queryValue = decodeURIComponentPatched(queryValue);
    const parsedValue = JSON.parse(queryValue);
    const parseResult = rowDiffObjectSchema.safeParse(parsedValue);
    if (parseResult.success) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      return parseResult.data as DiffObjectType<string>;
    } else {
      // It's not a valid diff string, so just treat it as an opaque string.
      return queryValue;
    }
  } catch {
    if (typeof queryValue === "string") {
      return queryValue;
    } else {
      console.warn("Invalid activeRowId query param", queryValue);
      return null;
    }
  }
}

export function activeRowParserSerialize(value: RowId) {
  if (typeof value === "string") return value;
  if (rowDiffObjectSchema.safeParse(value).success) {
    return encodeURIComponent(JSON.stringify(value));
  }
  console.warn("Unable to serialize activeRowId", value);
  return "";
}

const activeRowParser = createParser({
  parse: activeRowParserParse,
  serialize: activeRowParserSerialize,
});

export const useActiveRowAndSpan = () =>
  useQueryStates({
    r: { ...activeRowParser, history: "push" },
    s: parseAsString,
  });

export const useScrollTo = () => useQueryState("st", parseAsString);

// Page will attempt to select this object ID.
export const useNavToObjectId = () => useQueryState("oid");

export const usePlaygroundViewContext = () =>
  useQueryState("vd", parseAsString.withDefault("datagrid"));

export const usePlaygroundSelectedRowIndex = () =>
  useQueryState("rn", parseAsInteger);

export const usePlaygroundFullscreenTaskIndex = () =>
  useQueryState("fti", parseAsInteger);

export const useExperimentComparisonIds = () =>
  useQueryState<string[]>("c", parseAsArrayOf(parseAsString));

export const useComparisonExperimentId = () =>
  useQueryState("ce", parseAsString.withDefault(""));

export const useActiveCommentId = () => useQueryState("cm", parseAsString);

export const useSelectedProjectState = () => useQueryState("p", parseAsString);

export const useActivePromptRowState = () => useQueryState("pr", parseAsString);
export const useSearchState = () =>
  useQueryState("search", parseAsJsonEncoded(urlSearchSchema.parse));

const emptyArray: RegressionFilter[] = [];
export const useRegressionFilterState = () =>
  useQueryState("rf", {
    ...parseAsJson(
      regressionFiltersSchema.nullish().default(emptyArray).parse,
    ).withDefault(emptyArray),
  });

export const regressionFilterSerialize = createSerializer({
  rf: parseAsJson(regressionFiltersSchema.parse),
});

export const useFullscreenTraceState = () => useQueryState("fs", parseAsString);
export const useFullscreenMonitorCardState = () =>
  useQueryState("fsc", parseAsString);

// comparison_key -> experimentId -> compareRowId
type RowComparisonParams = Record<string, Record<string, string>>;

const parseAsRowComparisonParams = createParser<RowComparisonParams>({
  parse(queryValue) {
    const map = queryValue.split(",").reduce((acc: RowComparisonParams, v) => {
      const parts = v.split("|");
      if (parts.length !== 3) {
        return acc;
      }
      if (parts.some((p) => !p)) {
        return acc;
      }

      acc[parts[0]] = {
        [parts[1]]: parts[2],
      };
      return acc;
    }, {});

    return map;
  },
  serialize(p) {
    if (!p) {
      return "";
    }
    return Object.entries(p)
      .flatMap(([comparisonKey, v]) =>
        Object.entries(v).map(
          ([experimentId, compareRowId]) =>
            `${comparisonKey}|${experimentId}|${compareRowId}`,
        ),
      )
      .join(",");
  },
});

export const useRowComparisonParams = () =>
  useQueryState("rc", { ...parseAsRowComparisonParams, history: "replace" });

const parseAsSelectedEditorLine = (identifier?: string) =>
  createParser({
    parse(queryValue) {
      if (!identifier) return null;
      const parts = queryValue.split("|");
      if (parts.length !== 2 || parts[0] !== identifier) return null;

      const line = parseInt(parts[1]);
      if (Number.isNaN(line)) {
        return null;
      }
      return line;
    },
    serialize(value) {
      return `${identifier}|${value}`;
    },
  });

export const useSelectedEditorLineState = (identifier?: string) => {
  return useQueryState("L", parseAsSelectedEditorLine(identifier));
};

export const createSelectionTypeParser = (
  defaultValue: SelectionType = GROUP_BY_NONE,
) => {
  return (queryValue: string): SelectionType | null => {
    if (!queryValue) return null;
    if (
      queryValue === GROUP_BY_NONE_VALUE ||
      queryValue === "none" ||
      !queryValue.includes("|")
    )
      return defaultValue;
    const parts = queryValue.split("|");
    const parsed = selectionTypeSchema.safeParse({
      type: parts[0],
      value: parts[1],
    });
    return parsed.success ? parsed.data : null;
  };
};

export const parseSelectionTypeState = () => {
  return createParser({
    parse: createSelectionTypeParser(),
    serialize(v: SelectionType) {
      if (!v) return "";
      return `${v.type}|${v.value}`;
    },
  });
};

export const useColumnOrderOverrideState = () =>
  useQueryState("co", parseAsStringLiteral(["regression"]));

export const useTraceViewTypeState = () =>
  useQueryState("tvt", {
    ...parseAsStringLiteral(["thread", "timeline", "trace"]).withDefault(
      "trace",
    ),
    clearOnDefault: true,
  });

export const useBrainstoreBackfillState = () =>
  useQueryState("backfill_status", parseAsBoolean.withDefault(false));

export const usePlaygroundPromptSheetIndexState = () => {
  const [isPromptSheetTransitioning, startPromptSheetTransition] =
    useTransition();
  const [promptSheetIndex, setPromptSheetIndex] = useQueryState("psi", {
    ...parseAsInteger,
    startTransition: startPromptSheetTransition,
  });

  return {
    promptSheetIndex,
    setPromptSheetIndex,
    isPromptSheetTransitioning,
  };
};

export const useExpandedFrameState = () =>
  useQueryState("frame", parseAsString);

export const useAttachmentBrowser = () => useQueryState("ab", parseAsBoolean);

// this should only be used to override the layout specified in the view
export const useLayoutTypeOverrideState = () => {
  return useQueryState("lt", parseAsStringLiteral(tableLayoutTypes));
};

export const useAutomationIdState = () => useQueryState("aid", parseAsString);
export const useAutomationIdStatusState = () =>
  useQueryState("automation-status", parseAsString);

export const useIsLiveState = (defaultState?: boolean) =>
  useQueryState("live", parseAsBoolean.withDefault(defaultState ?? false));
