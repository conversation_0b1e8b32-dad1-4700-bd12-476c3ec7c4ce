import { type ScaleLinear } from "d3";
import { type StackedTimeBuckets } from "#/ui/charts/timeseries-data/get-timeseries-stacked-time";
import { BAR_WIDTH_FRACTION } from "./viz.constants";

interface GetStackTotalPathProps {
  stackedData: StackedTimeBuckets;
  xScale: ScaleLinear<number, number>;
  yScale: ScaleLinear<number, number>;
  timeIndex: number;
}

export const genStackTotalRect = ({
  stackedData,
  xScale,
  yScale,
  timeIndex,
}: GetStackTotalPathProps) => {
  const { timestamps, seriesStackedSum, bucketDuration } = stackedData;
  const start = timestamps[timeIndex];
  const end = start + bucketDuration;
  const stackTotal = seriesStackedSum[timeIndex]?.at(-1);
  if (!stackTotal) {
    return null;
  }
  return {
    x: xScale(start),
    width: (xScale(end) - xScale(start)) * BAR_WIDTH_FRACTION,
    y: yScale(stackTotal),
    height: yScale(0) - yScale(stackTotal),
  };
};
