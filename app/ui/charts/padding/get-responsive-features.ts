import { type ChartSize } from "#/ui/charts/chart.types";

export interface ResponsiveFeatures {
  hasXAxis: boolean;
  hasYAxis: boolean;
  hasBottomLegend: boolean;
}

const Y_AXIS_WIDTH_LIMIT = 150;
const Y_AXIS_HEIGHT_LIMIT = 80;

const X_AXIS_WIDTH_LIMIT = 100;
const X_AXIS_HEIGHT_LIMIT = 40;

const BOTTOM_LEGEND_HEIGHT_LIMIT = 30;

export const getResponsiveFeatures = (
  chartSize: ChartSize,
  possibleFeatures: Partial<ResponsiveFeatures>,
): ResponsiveFeatures => {
  const { width, height } = chartSize;
  const { hasXAxis, hasYAxis, hasBottomLegend } = possibleFeatures;
  return {
    hasYAxis:
      Boolean(hasYAxis) &&
      width > Y_AXIS_WIDTH_LIMIT &&
      height > Y_AXIS_HEIGHT_LIMIT,
    hasXAxis:
      <PERSON><PERSON><PERSON>(hasXAxis) &&
      width > X_AXIS_WIDTH_LIMIT &&
      height > X_AXIS_HEIGHT_LIMIT,
    hasBottomLegend:
      Boolean(hasBottomLegend) && height > BOTTOM_LEGEND_HEIGHT_LIMIT,
  };
};
