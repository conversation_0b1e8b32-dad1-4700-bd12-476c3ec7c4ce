import React from "react";
import { buttonVariants } from "#/ui/button";
import { cn } from "#/utils/classnames";
import { ArrowUpRight } from "lucide-react";

export const ApplyButton = ({
  url = "https://jobs.ashbyhq.com/Braintrust/form/application",
}: {
  url?: string;
}) => {
  return (
    <a
      href={url}
      target="_blank"
      className={cn(
        buttonVariants({
          variant: "primary",
          size: "lg",
        }),
        "bg-brandBlue !text-white hover:bg-black hover:text-white no-underline px-4",
      )}
      style={{ textDecoration: "none" }}
    >
      Apply
      <ArrowUpRight className="size-4" />
    </a>
  );
};
