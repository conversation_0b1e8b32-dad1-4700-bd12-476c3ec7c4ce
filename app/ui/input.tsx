import { cn } from "#/utils/classnames";
import {
  type ChangeEvent,
  forwardRef,
  type InputHTMLAttributes,
  useCallback,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {}

const inputWidthSpacingClassNames = "px-3 text-sm";
export const inputClassName =
  "flex h-10 w-full rounded-md border border-primary-200 bg-primary-50 dark:bg-primary-100 px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground placeholder:opacity-75 focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 tabular-nums [-moz-appearance:textfield] [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none";

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(inputClassName, inputWidthSpacingClassNames, className)}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = "Input";

type AutosizeInputProps = {
  minWidthClassName?: string;
  spacerClassName?: string;
} & InputProps;

export const AutosizeInput = forwardRef<HTMLInputElement, AutosizeInputProps>(
  (
    { minWidthClassName, spacerClassName, ...props }: AutosizeInputProps,
    ref,
  ) => {
    const { onChange } = props;
    const inputRef = useRef<HTMLInputElement | null>(null);
    useImperativeHandle(ref, () => inputRef.current!, []);

    const [_inputText, _setInputText] = useState(props.defaultValue ?? "");
    const inputText = props.value ?? _inputText ?? props.defaultValue;
    const setInputText = useCallback(
      (e: ChangeEvent<HTMLInputElement>) => {
        if (onChange) {
          onChange(e);
          return;
        }
        _setInputText(e.target.value);
      },
      [_setInputText, onChange],
    );

    return (
      <div
        className={cn("flex flex-col", {
          [minWidthClassName ?? ""]: !inputText,
        })}
      >
        <Input
          {...props}
          ref={(node) => {
            inputRef.current = node ?? null;
          }}
          value={inputText}
          onChange={setInputText}
        />
        <span
          className={cn(
            "h-0 self-start whitespace-pre",
            inputWidthSpacingClassNames,
            spacerClassName,
          )}
        >
          {inputText}
        </span>
      </div>
    );
  },
);
AutosizeInput.displayName = "AutosizeInput";
