import { Settings2 } from "lucide-react";
import { <PERSON><PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuGroup,
  DropdownMenuSeparator,
} from "#/ui/dropdown-menu";
import { type ToolName } from "@braintrust/local/optimization/tools";
import { INITIAL_LOOP_TOOLS } from "#/ui/optimization/global-chat-provider";
import { BasicTooltip } from "#/ui/tooltip";
export const SettingsDropdown = ({
  allowRunningWithoutConsent,
  setAllowRunningWithoutConsent,
  currentTool = INITIAL_LOOP_TOOLS,
  setCurrentTool,
  implementedTools,
}: {
  allowRunningWithoutConsent: boolean;
  setAllowRunningWithoutConsent: (consent: boolean) => void;
  currentTool?: ToolName[];
  setCurrentTool: (
    tools: ToolName[] | ((prev: Tool<PERSON>ame[]) => ToolName[]),
  ) => void;
  implementedTools: ToolName[];
}) => {
  const toolLabels: Record<ToolName, string> = {
    get_summary: "Get summarized results",
    get_results: "Get detailed results",
    edit_task: "Edit prompt",
    run_task: "Run eval",
    edit_data: "Edit data",
    continue_execution: "Continue execution",
    get_available_scorers: "Get scorers",
    edit_scorers: "Edit scorers",
    // BTQL-TODO: Re-enable this tool once we have a good tool for BTQLs
    //edit_btql: "Edit BTQL",
    create_llm_scorer: "Create LLM judge scorer",
    create_code_scorer: "Create code scorer",
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="size-6 text-primary-500"
          Icon={Settings2}
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="flex w-fit flex-col gap-0 overflow-hidden"
      >
        <DropdownMenuLabel>Available tools</DropdownMenuLabel>
        <DropdownMenuGroup>
          {implementedTools
            .filter((tool) => tool !== "continue_execution")
            .map((tool) => (
              <DropdownMenuCheckboxItem
                key={tool}
                checked={currentTool.includes(tool)}
                onSelect={(e) => e.preventDefault()}
                onCheckedChange={(checked) => {
                  setCurrentTool(
                    checked
                      ? [...currentTool, tool]
                      : currentTool.filter((t: ToolName) => t !== tool),
                  );
                }}
              >
                {toolLabels[tool] ?? tool}
              </DropdownMenuCheckboxItem>
            ))}
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuLabel>Settings</DropdownMenuLabel>
        <BasicTooltip tooltipContent="Automatically accept the changes without asking for confirmation">
          <DropdownMenuCheckboxItem
            onSelect={(e) => e.preventDefault()}
            checked={allowRunningWithoutConsent}
            onCheckedChange={(checked) => {
              setAllowRunningWithoutConsent(checked);
            }}
          >
            Auto-accept edits
          </DropdownMenuCheckboxItem>
        </BasicTooltip>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
