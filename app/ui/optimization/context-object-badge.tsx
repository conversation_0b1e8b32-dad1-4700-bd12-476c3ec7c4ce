import { type ContextObject } from "#/ui/optimization/use-global-chat-context";
import { cn } from "#/utils/classnames";
import { Button } from "#/ui/button";
import { MessageCircle, Database, Percent } from "lucide-react";
import { X } from "lucide-react";

export const ContextObjectBadge = ({
  contextObjectItem,
  onDelete,
  className,
}: {
  contextObjectItem: ContextObject;
  onDelete?: (contextObjectItem: ContextObject) => void;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        "flex max-w-40 items-center gap-1 h-5 rounded-md py-0.5 border pl-1 bg-primary-100 border-primary-300/50 text-primary-700",
        {
          "p-1": !onDelete,
        },
        className,
      )}
    >
      {contextObjectItem.resource === "task" ? (
        <MessageCircle className="mr-1 size-3 text-cyan-600" />
      ) : contextObjectItem.resource === "scorer" ? (
        <Percent className="mr-1 size-3 text-lime-600" />
      ) : (
        <Database className="mr-1 size-3 text-fuchsia-600" />
      )}
      <span className="flex-1 truncate">{contextObjectItem.name}</span>
      {onDelete && (
        <Button
          variant="ghost"
          size="icon"
          className={cn("ml-1 size-5 bg-transparent hover:bg-primary-200")}
          Icon={X}
          onClick={() => {
            onDelete(contextObjectItem);
          }}
        />
      )}
    </div>
  );
};
