import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
  useContext,
  type ReactNode,
} from "react";
import {
  GlobalChatContext,
  type ParsedMessage,
  type GlobalChatContextType,
  type ChatSession,
  type Mode,
  type UserMessage,
} from "./use-global-chat-context";
import {
  type ChatContext,
  type PageKey,
  buildSystemPrompt,
} from "@braintrust/local/optimization";
import { UIStateUpdatingLogger } from "./use-global-chat-context";
import {
  ALL_TOOL_NAMES,
  type ToolName,
} from "@braintrust/local/optimization/tools";
import {
  useOptimizationContext,
  type TaskEditConfirmationData,
  type DatasetEditConfirmationData,
  type UserConsentConfirmationData,
  type EditScorersConfirmationData,
  type CreateLLMScorerConfirmationData,
  type CreateCodeScorerConfirmationData,
} from "#/utils/optimization/provider";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { type ContextObject } from "#/ui/optimization/use-global-chat-context";
import { usePathname } from "next/navigation";

import { type ModelDetails, useAvailableModels } from "#/ui/prompts/models";
import { useOrg } from "#/utils/user";
import { useFeatureFlags } from "#/lib/feature-flags";
import {
  isDatasetPage,
  isExperimentPage,
  isLogsPage,
  isPlaygroundPage,
} from "#/app/app/[org]/pathname-checker";

export const INITIAL_LOOP_TOOLS: ToolName[] = Array.from(ALL_TOOL_NAMES);

const DEFAULT_CHAT_SESSIONS = {
  sessions: [],
};

interface GlobalChatProviderProps {
  children: ReactNode;
}

// Prioritized list of models to try as default, in order of preference
export const PRIORITIZED_DEFAULT_MODELS = [
  "claude-sonnet-4",
  "claude-opus-4-1",
  "gpt-4.1",
  "o3",
  "o4-mini",
  "claude-3-5-sonnet",
];

const isModelSupported = (
  model: string,
  allowOtherModels: boolean,
  availableModels: ModelDetails[],
) => {
  if (!model) {
    return false;
  }

  //When this feature flag is enabled, we allow any model to be selected -- so this check is against available models.
  if (allowOtherModels) {
    const availableModelNames = new Set();

    availableModels.forEach((model) => {
      availableModelNames.add(model.modelName);
      if (model.children) {
        model.children.forEach((child) => {
          availableModelNames.add(child.modelName);
        });
      }
    });

    return availableModelNames.has(model);
  }

  //this is the code path for when the feature flag is disabled -- we check against the strict prioritized list.
  for (const modelName of PRIORITIZED_DEFAULT_MODELS) {
    if (
      model.includes(modelName) &&
      availableModels.some(
        (availableModel) => availableModel.modelName === model,
      )
    ) {
      return true;
    }
  }

  return false;
};

const modelMaxOutputTokens = (
  model: string,
  availableModels: ModelDetails[],
): number | undefined => {
  let modelDetails = availableModels.find((m) => m.modelName === model);
  if (!modelDetails) {
    for (const parentModel of availableModels) {
      if (parentModel.children) {
        modelDetails = parentModel.children.find(
          (child) => child.modelName === model,
        );
        if (modelDetails) break;
      }
    }
  }

  return modelDetails?.max_output_tokens ?? undefined;
};

export function deriveDefaultModel(availableModels: ModelDetails[]): string {
  if (availableModels.length === 0) {
    return "";
  }

  for (const prioritizedModel of PRIORITIZED_DEFAULT_MODELS) {
    for (const modelDetails of availableModels) {
      if (modelDetails.modelName.includes(prioritizedModel)) {
        return modelDetails.modelName;
      }

      if (modelDetails.children) {
        for (const child of modelDetails.children) {
          if (child.modelName.includes(prioritizedModel)) {
            return child.modelName;
          }
        }
      }
    }
  }

  return "";
}

export function GlobalChatProvider({ children }: GlobalChatProviderProps) {
  const { makeChatContext: makeOptimizationChatContext, tools } =
    useOptimizationContext();
  const projectContext = useContext(ProjectContext);
  const projectId = projectContext.projectId;
  const pathname = usePathname();

  const pageKey: PageKey = useMemo(() => {
    if (isPlaygroundPage(pathname ?? "")) return "playground";
    if (isExperimentPage(pathname ?? "")) return "experiments";
    if (isLogsPage(pathname ?? "")) return "logs";
    if (isDatasetPage(pathname ?? "")) return "dataset";
    return "unknown";
  }, [pathname]);

  // if true, optimization chat will be undocked
  const [screenTooNarrow, setScreenTooNarrow] = useState(false);

  useEffect(() => {
    const shouldAutoUndock = pageKey === "experiments" || pageKey === "dataset";

    //early return for pages that aren't width constrained.
    if (!shouldAutoUndock) return;

    const unDockWidth = shouldAutoUndock ? 1400 : 0;

    let timeoutId: NodeJS.Timeout | null = null;
    const throttledResize = () => {
      if (timeoutId) return;

      timeoutId = setTimeout(() => {
        const width = window.innerWidth;
        setScreenTooNarrow(shouldAutoUndock && width < unDockWidth);
        timeoutId = null;
      }, 100);
    };

    setScreenTooNarrow(shouldAutoUndock && window.innerWidth < unDockWidth);

    window.addEventListener("resize", throttledResize);

    return () => {
      window.removeEventListener("resize", throttledResize);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [pageKey]);

  const { name: orgName } = useOrg();
  const implementedTools = tools.list();
  const { flags } = useFeatureFlags();

  const { configuredModelsByProvider } = useAvailableModels({ orgName });
  const availableModels = useMemo(
    () => Object.values(configuredModelsByProvider).flat(),
    [configuredModelsByProvider],
  );
  const defaultModel = useMemo(
    () => deriveDefaultModel(availableModels),
    [availableModels],
  );
  const chatInstancesRef = useRef<
    Map<
      string,
      {
        chatInstance: ChatContext;
        logger: UIStateUpdatingLogger;
        abortController: AbortController;
      }
    >
  >(new Map());

  const [isChatOpen, setIsChatOpen] = useEntityStorage({
    entityType: "optimization",
    entityIdentifier: "",
    key: "isChatOpen",
    defaultValue: false,
  });

  const [isAwaitingEditConfirmation, setIsAwaitingEditConfirmation] =
    useState(false);
  const [editTaskConfirmationData, setEditTaskConfirmationData] =
    useState<TaskEditConfirmationData | null>(null);
  const [editDatasetConfirmationData, setEditDatasetConfirmationData] =
    useState<DatasetEditConfirmationData | null>(null);
  const [userConsentConfirmationData, setUserConsentConfirmationData] =
    useState<UserConsentConfirmationData | null>(null);
  const [editScorersConfirmationData, setEditScorersConfirmationData] =
    useState<EditScorersConfirmationData | null>(null);
  const [createLLMScorerConfirmationData, setCreateLLMScorerConfirmationData] =
    useState<CreateLLMScorerConfirmationData | null>(null);
  const [
    createCodeScorerConfirmationData,
    setCreateCodeScorerConfirmationData,
  ] = useState<CreateCodeScorerConfirmationData | null>(null);

  const [chatSessions, setChatSessions] = useEntityStorage({
    entityType: "optimization",
    entityIdentifier: `${projectId}-${pageKey}`,
    key: "chatSessions",
    defaultValue: DEFAULT_CHAT_SESSIONS,
  });
  const [currentChatSessionId, setCurrentChatSessionId] = useEntityStorage({
    entityType: "optimization",
    entityIdentifier: `${projectId}-${pageKey}`,
    key: "currentChatSessionId",
    defaultValue: "initial-chat-session",
  });
  const [isDocked, setIsDocked] = useEntityStorage({
    entityType: "optimization",
    entityIdentifier: `${projectId}-${pageKey}`,
    key: "isDocked",
    defaultValue: false,
  });

  const [model, setModel] = useEntityStorage({
    entityType: "optimization",
    entityIdentifier: "",
    key: "model",
    defaultValue: defaultModel,
  });

  const createNewChatSessionObject = useCallback(
    ({
      id,
      name,
      overrides = {},
    }: {
      id?: string;
      name?: string;
      overrides?: Partial<ChatSession>;
    }): ChatSession => {
      return {
        id: id ?? crypto.randomUUID(),
        name: name ?? "New Session",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        messages: [],
        contextObjects: {},
        tools: implementedTools,
        mode: "agent",
        userMessage: "",
        isActive: false,
        ...overrides,
      };
    },
    [implementedTools],
  );

  const getChatInstanceForSession = useCallback(
    (sessionId: string) => {
      if (!makeOptimizationChatContext) return null;
      let instance = chatInstancesRef.current.get(sessionId);
      if (!instance) {
        const logger = new UIStateUpdatingLogger(
          (messages: ParsedMessage[]) => {
            setChatSessions((prev) => ({
              ...prev,
              sessions: prev.sessions.map((session) =>
                session.id === sessionId ? { ...session, messages } : session,
              ),
            }));
          },
        );

        const maxOutputTokens = modelMaxOutputTokens(model, availableModels);
        const chatInstance = makeOptimizationChatContext({
          chatLogger: logger,
          allowed_tools: implementedTools,
          model: model,
          modelParams: {
            max_tokens: maxOutputTokens,
          },
          defaultSystemPrompt: buildSystemPrompt(pageKey),
        });
        const abortController = new AbortController();

        instance = { chatInstance, logger, abortController };
        chatInstancesRef.current.set(sessionId, instance);
      }
      return instance;
    },
    [
      makeOptimizationChatContext,
      model,
      availableModels,
      setChatSessions,
      implementedTools,
      pageKey,
    ],
  );

  const currentChatInstance = useMemo(() => {
    return getChatInstanceForSession(currentChatSessionId);
  }, [getChatInstanceForSession, currentChatSessionId]);

  const createNewSession = useCallback(() => {
    const newSession = createNewChatSessionObject({
      name: "New Session",
    });
    setChatSessions((prevSessions) => ({
      ...prevSessions,
      sessions: [...prevSessions.sessions, newSession],
    }));
    setCurrentChatSessionId(newSession.id);
  }, [setChatSessions, setCurrentChatSessionId, createNewChatSessionObject]);

  const currentSession = useMemo(() => {
    if (chatSessions.sessions.length > 0) {
      return chatSessions.sessions.find(
        (session) => session.id === currentChatSessionId,
      );
    }
    return createNewChatSessionObject({ name: "New Session" });
  }, [chatSessions, currentChatSessionId, createNewChatSessionObject]);

  const currentSessionUserMessage = useMemo(
    () => currentSession?.userMessage || "",
    [currentSession],
  );

  const currentSessionMessages = useMemo(
    () => currentSession?.messages || [],
    [currentSession],
  );
  const currentSessionContextObjects = useMemo(
    () => currentSession?.contextObjects || {},
    [currentSession],
  );

  const currentSessionTools = useMemo(
    () => currentSession?.tools || implementedTools,
    [currentSession, implementedTools],
  );
  const currentSessionMode = useMemo(
    () => currentSession?.mode || "agent",
    [currentSession],
  );
  const currentSessionIsActive = useMemo(
    () => currentSession?.isActive || false,
    [currentSession],
  );

  const setCurrentSessionUserMessage = useCallback(
    (userMessage: string) => {
      if (currentChatSessionId)
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? { ...session, userMessage }
              : session,
          ),
        }));
    },
    [currentChatSessionId, setChatSessions],
  );

  const setCurrentSessionMessages = useCallback(
    (messages: ParsedMessage[]) => {
      if (currentChatSessionId)
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? { ...session, messages }
              : session,
          ),
        }));
    },
    [currentChatSessionId, setChatSessions],
  );

  const setCurrentSessionContextObjects = useCallback(
    (
      sessionContextObjects:
        | Record<string, ContextObject>
        | ((
            prev: Record<string, ContextObject>,
          ) => Record<string, ContextObject>),
    ) => {
      if (currentChatSessionId) {
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? {
                  ...session,
                  contextObjects:
                    typeof sessionContextObjects === "function"
                      ? sessionContextObjects(session.contextObjects)
                      : sessionContextObjects,
                }
              : session,
          ),
        }));
      }
    },
    [currentChatSessionId, setChatSessions],
  );

  const setCurrentSessionTools = useCallback(
    (tools: ToolName[] | ((prev: ToolName[]) => ToolName[])) => {
      if (currentChatSessionId)
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? {
                  ...session,
                  tools:
                    typeof tools === "function" ? tools(session.tools) : tools,
                }
              : session,
          ),
        }));
    },
    [currentChatSessionId, setChatSessions],
  );

  const setCurrentSessionMode = useCallback(
    (mode: Mode) => {
      if (currentChatSessionId)
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? { ...session, mode }
              : session,
          ),
        }));
    },
    [currentChatSessionId, setChatSessions],
  );

  const setCurrentSessionIsActive = useCallback(
    (isActive: boolean) => {
      if (currentChatSessionId)
        setChatSessions((prev) => ({
          ...prev,
          sessions: prev.sessions.map((session) =>
            session.id === currentChatSessionId
              ? { ...session, isActive }
              : session,
          ),
        }));
    },
    [currentChatSessionId, setChatSessions],
  );

  const deleteSession = useCallback(
    (sessionId: string) => {
      setChatSessions((prev) => {
        const isCurrentSessionBeingDeleted = sessionId === currentChatSessionId;
        const filteredSessions = prev.sessions.filter(
          (s) => s.id !== sessionId,
        );

        // If we're deleting the current session and there are other sessions available
        if (isCurrentSessionBeingDeleted && filteredSessions.length > 0) {
          const deletedSessionIndex = prev.sessions.findIndex(
            (s) => s.id === sessionId,
          );

          // Switch to the previous session if possible, otherwise switch to the first session
          if (deletedSessionIndex > 0) {
            setCurrentChatSessionId(prev.sessions[deletedSessionIndex - 1].id);
          } else {
            setCurrentChatSessionId(filteredSessions[0].id);
          }
        }

        return {
          ...prev,
          sessions: filteredSessions,
        };
      });

      chatInstancesRef.current.delete(sessionId);
    },
    [setChatSessions, currentChatSessionId, setCurrentChatSessionId],
  );

  const handleAbort = useCallback(() => {
    const instance = chatInstancesRef.current.get(currentChatSessionId);
    if (instance) {
      instance.abortController.abort();
      // Create new abort controller for this session
      instance.abortController = new AbortController();
    }
    setCurrentSessionIsActive(false);
    setEditTaskConfirmationData(null);
    setEditDatasetConfirmationData(null);
    setUserConsentConfirmationData(null);
  }, [
    currentChatSessionId,
    setCurrentSessionIsActive,
    setEditTaskConfirmationData,
    setEditDatasetConfirmationData,
    setUserConsentConfirmationData,
  ]);

  const abortAllSessions = useCallback(() => {
    chatInstancesRef.current.forEach((instance) => {
      instance.abortController.abort();
      instance.abortController = new AbortController();
    });

    setChatSessions((prev) => ({
      ...prev,
      sessions: prev.sessions.map((session) => ({
        ...session,
        isActive: false,
      })),
    }));

    setEditTaskConfirmationData(null);
    setEditDatasetConfirmationData(null);
    setUserConsentConfirmationData(null);
  }, [
    setChatSessions,
    setEditTaskConfirmationData,
    setEditDatasetConfirmationData,
    setUserConsentConfirmationData,
  ]);

  const handleSendMessage = useCallback(
    async (
      userMessage: UserMessage,
      options?: {
        clearContextObjects?: boolean;
        clearUserMessage?: boolean;
      },
    ) => {
      const sessionInstance = currentChatInstance;
      if (!sessionInstance) {
        return;
      }

      if (!userMessage.message.trim()) {
        return;
      }

      // Set current session active, all others inactive
      setChatSessions((prev) => ({
        ...prev,
        sessions: prev.sessions.map((session) => ({
          ...session,
          isActive: session.id === currentChatSessionId,
        })),
      }));

      // Create new abort controller for this operation
      abortAllSessions();
      sessionInstance.abortController = new AbortController();
      const currentOperationController = sessionInstance.abortController;

      try {
        sessionInstance.logger.addUserMessage(
          userMessage.message,
          userMessage.contextObjects,
        );

        if (options?.clearContextObjects) {
          setCurrentSessionContextObjects({});
        }
        if (options?.clearUserMessage) {
          setCurrentSessionUserMessage("");
        }

        setEditTaskConfirmationData(null);
        setEditDatasetConfirmationData(null);
        setUserConsentConfirmationData(null);
        setCreateLLMScorerConfirmationData(null);
        setCreateCodeScorerConfirmationData(null);

        let prompt = "";

        if (
          userMessage.contextObjects &&
          Object.keys(userMessage.contextObjects).length > 0
        ) {
          prompt = `Here are the tagged context: ${JSON.stringify(
            userMessage.contextObjects,
          )}\n\n`;
        }
        prompt += userMessage.message;

        setCurrentSessionIsActive(true);

        await sessionInstance.chatInstance.turn(prompt, {
          abortController: currentOperationController,
        });
      } catch (error) {
        if (error instanceof Error && error.name === "AbortError") {
          // silent handle
        } else {
          console.error("Error in handleSendMessage during chat.turn:", error);
          // Add error message to chat
          sessionInstance.logger.addSystemMessage(
            `Error: ${error instanceof Error ? error.message : "An unexpected error occurred"}`,
            "error",
          );
        }
      } finally {
        // This check is necessary because if the user kicked off a new turn, the abortcontroller will be different and we don't want to set that to false.
        if (currentOperationController === sessionInstance.abortController) {
          setChatSessions((prev) => ({
            ...prev,
            sessions: prev.sessions.map((session) =>
              session.id === currentChatSessionId
                ? { ...session, isActive: false }
                : session,
            ),
          }));
        }
        sessionInstance.logger.onTurnComplete();
      }
    },
    [
      abortAllSessions,
      currentChatInstance,
      currentChatSessionId,
      setChatSessions,
      setCurrentSessionUserMessage,
      setCurrentSessionContextObjects,
      setCurrentSessionIsActive,
    ],
  );

  useEffect(() => {
    const handlePageHide = () => {
      setChatSessions((prev) => ({
        ...prev,
        sessions: prev.sessions.map((session) => ({
          ...session,
          isActive: false,
        })),
      }));

      chatInstancesRef.current.forEach((instance) => {
        instance.abortController.abort();
      });
      chatInstancesRef.current.clear();
    };

    window.addEventListener("pagehide", handlePageHide);

    return () => {
      window.removeEventListener("pagehide", handlePageHide);
    };
  }, [setChatSessions]);

  useEffect(() => {
    if (
      chatSessions &&
      chatSessions.sessions &&
      chatSessions.sessions.length === 0 &&
      //we check for tools because we don't want to create a session if the tools are not ready yet. and OptimizationProvider will not have tools initially.
      implementedTools.length > 0
    ) {
      createNewSession();
    }
  }, [chatSessions, createNewSession, implementedTools]);

  // Update current session's chat instance configuration when tools/model change
  useEffect(() => {
    const instance = currentChatInstance;
    if (instance) {
      instance.chatInstance.reconfigure({
        allowed_tools: currentSessionTools,
        model: model,
        modelParams: {
          max_tokens: modelMaxOutputTokens(model, availableModels),
        },
      });
    }
  }, [currentChatInstance, currentSessionTools, model, availableModels]);

  useEffect(() => {
    if (availableModels) {
      const modelIsSupported = isModelSupported(
        model,
        flags.loopTryOtherModels,
        availableModels,
      );
      if (!modelIsSupported) {
        const newDefaultModel = deriveDefaultModel(availableModels);
        setModel(newDefaultModel ?? "");
      }
    }
  }, [availableModels, model, setModel, flags.loopTryOtherModels]);

  const globalChatContextValue: GlobalChatContextType = useMemo(
    () => ({
      chat: currentChatInstance?.chatInstance || null,
      isAwaitingEditConfirmation,
      setIsAwaitingEditConfirmation,
      editTaskConfirmationData,
      setEditTaskConfirmationData,
      editDatasetConfirmationData,
      setEditDatasetConfirmationData,
      userConsentConfirmationData,
      setUserConsentConfirmationData,
      editScorersConfirmationData,
      setEditScorersConfirmationData,
      createLLMScorerConfirmationData,
      setCreateLLMScorerConfirmationData,
      createCodeScorerConfirmationData,
      setCreateCodeScorerConfirmationData,
      isChatOpen,
      setIsChatOpen,
      isDocked: isDocked ?? false,
      setIsDocked,
      createNewSession,
      chatSessions,
      setChatSessions,
      currentChatSessionId,
      setCurrentChatSessionId,
      currentSessionMessages,
      currentSessionContextObjects,
      model,
      setModel,
      currentSessionTools,
      currentSessionMode,
      currentSessionIsActive,
      currentSessionUserMessage,
      setCurrentSessionMessages,
      setCurrentSessionContextObjects,
      setCurrentSessionTools,
      setCurrentSessionMode,
      setCurrentSessionIsActive,
      setCurrentSessionUserMessage,
      handleSendMessage,
      handleAbort,
      deleteSession,
      implementedTools,
      pageKey,
      screenTooNarrow,
    }),
    [
      currentChatInstance,
      isAwaitingEditConfirmation,
      setIsAwaitingEditConfirmation,
      editTaskConfirmationData,
      setEditTaskConfirmationData,
      editDatasetConfirmationData,
      setEditDatasetConfirmationData,
      userConsentConfirmationData,
      setUserConsentConfirmationData,
      editScorersConfirmationData,
      setEditScorersConfirmationData,
      createLLMScorerConfirmationData,
      setCreateLLMScorerConfirmationData,
      createCodeScorerConfirmationData,
      setCreateCodeScorerConfirmationData,
      isChatOpen,
      setIsChatOpen,
      isDocked,
      setIsDocked,
      createNewSession,
      chatSessions,
      setChatSessions,
      currentChatSessionId,
      setCurrentChatSessionId,
      currentSessionMessages,
      currentSessionContextObjects,
      model,
      setModel,
      currentSessionTools,
      currentSessionMode,
      currentSessionIsActive,
      currentSessionUserMessage,
      setCurrentSessionMessages,
      setCurrentSessionContextObjects,
      setCurrentSessionTools,
      setCurrentSessionMode,
      setCurrentSessionIsActive,
      setCurrentSessionUserMessage,
      handleSendMessage,
      handleAbort,
      deleteSession,
      implementedTools,
      pageKey,
      screenTooNarrow,
    ],
  );

  return (
    <GlobalChatContext.Provider value={globalChatContextValue}>
      {children}
    </GlobalChatContext.Provider>
  );
}
