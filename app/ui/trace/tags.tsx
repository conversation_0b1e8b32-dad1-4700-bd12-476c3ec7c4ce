import { type ProjectTag } from "@braintrust/core/typespecs";
import { performUpsert as upsertTag } from "#/app/app/[org]/p/[project]/configuration/configuration-client-actions";
import { MERGE_PATHS_FIELD, type TransactionId } from "@braintrust/core";
import { useOptimisticState } from "#/utils/optimistic-update";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Plus, Settings2, TagIcon } from "lucide-react";
import { Tag, TagColor, tagSortFn } from "#/ui/tag";
import { type Schema } from "apache-arrow";
import {
  type AnyClauseSpec,
  type Clause,
  type ClauseChecker,
  type Search,
  addClause,
  makeBubble,
  removeClause,
} from "#/utils/search/search";
import { safeDeserializeUnknown, strMax, updatePathMut } from "#/utils/object";
import { Tags<PERSON>ield, TransactionIdField } from "#/utils/duckdb";
import { Bubble } from "#/ui/table/bubble";
import type { FormatterProps } from "#/ui/arrow-table";
import { type TableSelection } from "#/ui/table/useTableSelection";
import { type DML } from "#/utils/mutable-object";
import { DiffRightField, isDiffObject } from "#/utils/diffs/diff-objects";
import { Combobox } from "#/ui/combobox/combobox";
import { Button, type ButtonProps } from "#/ui/button";
import { type PropsWithChildren } from "react";
import { useParams, useRouter } from "next/navigation";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { z } from "zod";
import { ObjectIdFields } from "@braintrust/local/api-schema";
import { useHotkeys } from "react-hotkeys-hook";
import { cn } from "#/utils/classnames";
import {
  ProjectContext,
  type ProjectContextT,
} from "#/app/app/[org]/p/[project]/projectContext";
import { singleQuote } from "#/utils/sql-utils";
import { useOrg } from "#/utils/user";
import { useSessionToken } from "#/utils/auth/session-token";
import { NullFormatter } from "#/ui/table/formatters/null-formatter";
import { decodeURIComponentPatched } from "#/utils/url";
import { BT_IS_GROUP } from "#/ui/table/grouping/queries";
import {
  RESERVED_TAGS,
  TagDialog,
} from "#/app/app/[org]/p/[project]/configuration/tags/tag-dialog";

const tagsSchema = z.array(z.string());

// for use outside of project provider
export interface TagsProjectInfo {
  projectId: ProjectContextT["projectId"];
  projectName: ProjectContextT["projectName"];
  config: ProjectContextT["config"];
  mutateConfig: ProjectContextT["mutateConfig"];
}

// View model for the items of TagsCombobox.
type TagOption = {
  value: string;
  label: string;
  tagId: string;
  description?: string;
  color?: string;
  // Whether this represents a "Create foo tag" combobox option.
  isCreate?: boolean;
};
export const TagsCombobox = ({
  selectedTags,
  onChange,
  canAddTag,
  children,
  bottomMessage,
  disabled,
  providedProject,
}: PropsWithChildren<{
  canAddTag?: boolean;
  selectedTags: string[] | null;
  onChange: (label: string) => void;
  bottomMessage?: React.ReactNode;
  disabled?: boolean;
  providedProject?: TagsProjectInfo;
}>) => {
  const router = useRouter();
  const [searchValue, setSearchValue] = useState("");
  const [open, setOpen] = useState(false);
  const params = useParams<{ org: string; project: string }>();
  const orgName = decodeURIComponentPatched(params?.org ?? "");
  const projCtx = useContext(ProjectContext);

  // use the provided project if it exists, else use context
  const {
    projectId,
    projectName,
    config: projectConfig,
    mutateConfig: mutateProjectConfig,
  } = providedProject ?? projCtx;

  const tagConfig = projectConfig?.tags ?? [];
  const { api_url: apiUrl } = useOrg();
  const { getOrRefreshToken } = useSessionToken();

  if (!projectId) {
    throw new Error("Cannot instantiate TagsCombobox outside project");
  }

  const options: TagOption[] = tagConfig.map((tag) => ({
    value: tag.name,
    label: tag.name,
    tagId: tag.id,
    description: tag.description ?? undefined,
    color: tag.color ?? undefined,
    position: tag.position ?? null,
  }));

  // For selectedTags that are not in the project tags, add them to the tag combobox too.
  options.push(
    ...(selectedTags ?? [])
      .filter(
        (tagName) =>
          !tagConfig.find((tag) => tag.name == tagName) &&
          !RESERVED_TAGS.includes(tagName),
      )
      .map((tagName) => ({
        value: tagName,
        label: tagName,
        tagId: "",
      })),
  );

  const showCreateTagOption =
    canAddTag &&
    searchValue !== "" &&
    !options.some((o) => o.value === searchValue) &&
    !RESERVED_TAGS.includes(searchValue);

  return (
    <>
      <Combobox<TagOption>
        searchPlaceholder="Find tag"
        options={[
          ...options.sort(tagSortFn),
          ...(showCreateTagOption
            ? [
                {
                  value: searchValue,
                  label: `Create ${searchValue} tag`,
                  tagId: "",
                  isCreate: true,
                },
              ]
            : []),
        ]}
        stayOpenOnChange
        selectedValues={selectedTags ?? undefined}
        variant="button"
        buttonVariant="border"
        onChange={(_, option) => {
          if (disabled) {
            return;
          }
          if (option.isCreate) {
            setOpen(true);
            return;
          }
          onChange(option.label);
        }}
        placeholderLabel="Tags"
        onSearchChange={setSearchValue}
        noResultsLabel={
          <span className="flex h-4 items-center">No tags found</span>
        }
        bottomActions={[
          {
            label: (
              <>
                <Plus className="mr-2 size-3 flex-none" />
                Create tag
              </>
            ),
            onSelect: () => {
              setOpen(true);
            },
            hidden: showCreateTagOption,
          },
          {
            label: (
              <>
                <Settings2 className="mr-2 size-3 flex-none" />
                Manage project tags
              </>
            ),
            onSelect: () => {
              router.push(
                `/app/${orgName}/p/${projectName}/configuration/tags`,
              );
            },
          },
        ]}
        bottomMessage={
          <span className="block text-xs leading-normal text-primary-500">
            {bottomMessage ??
              "Only tags configured for this project will appear here"}
          </span>
        }
        disabled={disabled}
        renderOptionLabel={(tag) => {
          if (tag.isCreate) {
            return (
              <span
                className={cn(
                  "flex h-4 items-center gap-1.5",
                  disabled && "opacity-50 cursor-not-allowed",
                )}
              >
                Create
                <Tag label={searchValue} color={TagColor.Blue} />
              </span>
            );
          }
          return (
            <div
              className={cn(
                "w-full overflow-hidden",
                disabled && "opacity-50 cursor-not-allowed",
              )}
            >
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="max-w-full overflow-hidden">
                    <Tag
                      label={tag.label}
                      className="align-middle"
                      color={tag.color}
                    />
                    {tag.description && (
                      <div className="truncate pt-1 text-xs text-primary-500">
                        {tag.description}
                      </div>
                    )}
                  </div>
                </TooltipTrigger>
                {tag.description && (
                  <TooltipContent side="left">
                    <div>{tag.description}</div>
                  </TooltipContent>
                )}
              </Tooltip>
            </div>
          );
        }}
      >
        {children}
      </Combobox>
      <TagDialog
        projectName={projectName}
        defaultName={searchValue}
        tagConfig={null}
        opened={open}
        setOpened={(o) => {
          setOpen(o);
          setSearchValue("");
        }}
        upsert={async (tag) => {
          const sessionToken = await getOrRefreshToken();
          const upsertResponse = await upsertTag({
            apiUrl,
            sessionToken,
            objectType: "project_tag",
            row: tag,
            projectId,
            mutate: mutateProjectConfig,
          });
          onChange(tag.name);
          return upsertResponse;
        }}
      />
    </>
  );
};

export function Tags({
  tags,
  rowId,
  xactId,
  updateTag,
  buttonVariant,
  buttonClassName,
  bottomMessage,
  disabled,
}: {
  tags: string[];
  rowId: string;
  xactId: TransactionId;
  updateTag: ((tags: string[]) => Promise<TransactionId | null>) | undefined;
  buttonVariant?: ButtonProps["variant"];
  buttonClassName?: string;
  bottomMessage?: React.ReactNode;
  disabled?: boolean;
}) {
  // This pattern should be built into useOptimisticState...
  const [selectedTags, setSelectedTags] = useState(tags);

  const { save } = useOptimisticState({
    xactId,
    value: tags,
    save: updateTag || (() => Promise.resolve(null)),
    rowKey: rowId,
    onUpdatedValue: setSelectedTags,
  });

  const buttonRef = useRef<HTMLButtonElement>(null);
  useHotkeys(
    "t",
    (e) => {
      if (buttonRef.current) {
        e.preventDefault();
        buttonRef.current.click();
      }
    },
    {
      description: "Edit tags",
    },
  );

  const handleChange = useCallback(
    (label: string) => {
      const isSelected = selectedTags.includes(label);
      const newTags = updateTagList(selectedTags, label, !isSelected);
      setSelectedTags(newTags);
      save(newTags);
    },
    [selectedTags, save],
  );

  return (
    <TagsCombobox
      onChange={handleChange}
      selectedTags={selectedTags}
      canAddTag
      bottomMessage={bottomMessage}
      disabled={disabled}
    >
      <div>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              role="combobox"
              aria-controls="combobox-options"
              className={cn(buttonClassName)}
              size="xs"
              ref={buttonRef}
              variant={buttonVariant}
              Icon={TagIcon}
              disabled={disabled}
            />
          </TooltipTrigger>
          <TooltipContent side="bottom" className="text-xs">
            Tags <span className="ml-2.5 inline-block opacity-50">T</span>
          </TooltipContent>
        </Tooltip>
      </div>
    </TagsCombobox>
  );
}

export function TagFilter({
  search,
  setSearch,
}: {
  search: Search;
  setSearch: Dispatch<SetStateAction<Search>>;
}) {
  const selectedTags = useMemo(
    // Only treat "+" tags as selected in the combobox.
    () =>
      search.tag
        ?.filter((t) => t.text[0] === "+")
        .map((t) => t.text.slice(1)) ?? [],
    [search],
  );

  return (
    <TagsCombobox
      selectedTags={selectedTags}
      onChange={(label) => {
        const text = `+${label}`;
        if (!selectedTags.includes(label)) {
          const clause: Clause<"tag"> = {
            type: "tag",
            text,
            bubble: new Bubble({
              type: "tag",
              label: text,
              clear: () => {
                setSearch((s) => removeClause(s, { type: "tag", text }));
              },
            }),
          };
          setSearch((s) => addClause(s, clause));
        } else {
          setSearch((s) => removeClause(s, { type: "tag", text }));
        }
      }}
    >
      <Button size="xs">
        <TagIcon className="size-3" />
      </Button>
    </TagsCombobox>
  );
}

export function updateTagList(tags: string[], tag: string, added: boolean) {
  const oldTags = tags.filter((t) => t !== tag);
  return added ? oldTags.concat(tag) : oldTags;
}

// DEPRECATION_NOTICE: We can remove this once everyone updates their API to have tags
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
export function useHasTags({ schema }: { schema: Schema<any> | null }) {
  const [hasTags, setHasTags] = useState<boolean>(false);
  useEffect(() => {
    if (schema) {
      setHasTags(schema.fields.some((f) => f.name === "tags"));
    }
  }, [schema]);
  return { hasTags };
}

export function useTagsFormatter({
  tagConfig,
  onTagClick,
}: {
  tagConfig: ProjectTag[];
  onTagClick?: (tag: string) => void;
}) {
  return useMemo(() => {
    return TagsFormatterFactory({ tagConfig, onTagClick });
  }, [tagConfig, onTagClick]);
}

export function TagsFormatterFactory({
  tagConfig,
  onTagClick,
}: {
  tagConfig: ProjectTag[];
  onTagClick?: (tag: string) => void;
}) {
  return function TagsFormatter<
    TsTable extends { [BT_IS_GROUP]?: boolean },
    TsValue,
  >({
    value: valueProp,
    cell,
    renderForTooltip,
  }: FormatterProps<TsTable, TsValue>) {
    const valueString = isDiffObject(valueProp)
      ? // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        (valueProp[DiffRightField] as unknown as string)
      : valueProp;

    let value: string[] | null = null;

    // We can't really trust the data coming through here, so try to parse it as the right type
    // and fall back to null if it doesn't work.
    const parsed = tagsSchema.safeParse(
      typeof valueString === "string"
        ? JSON.parse(valueString)
        : valueString && typeof valueString === "object" && valueString.toArray
          ? valueString.toArray()
          : valueString,
    );

    if (parsed.success && parsed.data.length > 0) {
      value = parsed.data;
    }

    const isEmpty =
      value === null ||
      valueString == null ||
      value.filter((t) => !RESERVED_TAGS.includes(t)).length === 0;

    if (isEmpty) {
      return cell.row.original[BT_IS_GROUP] ? null : <NullFormatter />;
    }

    const tags = value!
      .filter((tagName) => !RESERVED_TAGS.includes(tagName))
      .map((tagName) => {
        const tagConfigEntry = tagConfig.find((t) => t.name === tagName);
        return {
          name: tagName,
          config: tagConfigEntry,
        };
      })
      .sort((a, b) =>
        tagSortFn(a.config || { name: a.name }, b.config || { name: b.name }),
      )
      .map((tag, idx) => {
        return (
          <Tag
            key={idx}
            label={tag.name}
            color={tag.config?.color}
            className="mr-1 truncate align-middle text-xs"
            onTagClick={renderForTooltip ? onTagClick : undefined}
          />
        );
      });

    if (renderForTooltip) {
      return (
        <div className="flex flex-wrap">
          <div className="mb-1.5 w-full text-xs text-primary-500">
            Filter by tag
          </div>
          {tags}
        </div>
      );
    }
    return tags;
  };
}

export function setTagSearchFn(
  clauseChecker: ClauseChecker | null,
  setSearch: Dispatch<SetStateAction<Search>>,
) {
  return async (tag: string) => {
    if (!clauseChecker || !setSearch) {
      return;
    }
    const clause = {
      type: "filter" as const,
      text: `tags includes ${singleQuote(tag)}`,
    };
    const checkResult = await clauseChecker(clause);
    if (checkResult.type !== "checked") {
      return;
    }
    setSearch((s) =>
      addClause(s, {
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        ...(clause as AnyClauseSpec),
        ...checkResult.extraFields,
        bubble: makeBubble({
          clause,
          setSearch,
        }),
      }),
    );
  };
}

export function TagBulkEditor({
  dml,
  selectionProps: { selectedRows, getSelectedRowsWithData },
  buttonClassName,
  isPlayground,
}: {
  dml: DML;
  selectionProps: Pick<
    TableSelection,
    "selectedRows" | "getSelectedRowsWithData"
  >;
  buttonClassName?: string;
  isPlayground?: boolean;
}) {
  const [isPending, setIsPending] = useState(false);
  // Hopefully this isn't too expensive. We need to recompute the maximum transaction id each time
  // this component is rendered, because getSelectedRowsWithData itself doesn't change. This computation,
  // however, allows us to figure out if other things should change.
  const lastMaxXactId = useRef<string>("0");
  const maxXactId = strMax(
    getSelectedRowsWithData().reduce(
      (max, row) => strMax(max, row[TransactionIdField] ?? "0"),
      "0",
    ),
    // eslint-disable-next-line react-compiler/react-compiler
    lastMaxXactId.current,
  );
  // eslint-disable-next-line react-compiler/react-compiler
  lastMaxXactId.current = maxXactId;

  const recomputeUnionTags = useCallback(() => {
    const tags = new Set<string>();
    getSelectedRowsWithData().forEach((row) => {
      const rowTags = z
        .array(z.string())
        .safeParse(safeDeserializeUnknown(row[TagsField] ?? "[]"));
      if (rowTags.success) {
        rowTags.data.forEach((tag) => tags.add(tag));
      }
    });
    return Array.from(tags);
  }, [getSelectedRowsWithData]);

  const { unionTags } = useMemo(() => {
    return { unionTags: recomputeUnionTags(), maxXactId };
  }, [recomputeUnionTags, maxXactId]);

  const rowId = useMemo(
    () => Object.keys(selectedRows).join(","),
    [selectedRows],
  );

  const updateTagWithPendingTracking = useCallback(
    async (newTags: string[]) => {
      setIsPending(true);
      try {
        // Compute the delta based on unionTags, so that even if a change hasn't saved yet,
        // we'll save the correct one relative to the optimistic state.
        const unionTags = recomputeUnionTags();
        const addedTags = newTags.filter((tag) => !unionTags.includes(tag));
        const removedTags = unionTags.filter((tag) => !newTags.includes(tag));
        const updatedRows = await Promise.all(
          getSelectedRowsWithData().map(async (row) => {
            const rowTags = tagsSchema.safeParse(
              safeDeserializeUnknown(row[TagsField] ?? "[]"),
            );
            let newTags = rowTags.success ? rowTags.data : [];
            for (const tag of addedTags) {
              newTags = updateTagList(newTags, tag, true);
            }
            for (const tag of removedTags) {
              newTags = updateTagList(newTags, tag, false);
            }

            const updates = [
              {
                path: [TagsField],
                newValue: newTags,
              },
            ];

            // This logic roughly mirrors the implementation of dml.update()
            const updatedRow = (await dml.prepareUpdates([row], updates))[0];

            const newRow = {
              id: updatedRow.id,
              ...Object.fromEntries(
                ObjectIdFields.filter((k) => updatedRow[k]).map((k) => [
                  k,
                  updatedRow[k],
                ]),
              ),
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              [MERGE_PATHS_FIELD]: [] as string[][],
            };
            for (const { path, newValue } of updates) {
              updatePathMut(newRow, path, newValue);
              newRow[MERGE_PATHS_FIELD].push(path);
            }

            return { updatedRow, newRow };
          }),
        );

        const rowsWithUpdates = updatedRows.map((row) => row.updatedRow);
        const pathUpdates = updatedRows.map((row) => row.newRow);

        return await dml.upsert(rowsWithUpdates, { pathUpdates });
      } finally {
        setIsPending(false);
      }
    },
    [dml, getSelectedRowsWithData, recomputeUnionTags],
  );

  return (
    <Tags
      buttonClassName={buttonClassName}
      disabled={isPending}
      updateTag={updateTagWithPendingTracking}
      tags={unionTags}
      xactId={maxXactId}
      rowId={rowId}
      bottomMessage={
        isPlayground ? (
          <>
            <span className="font-medium text-primary-700">
              Tag changes will be applied to the original dataset.
            </span>{" "}
            Only tags configured for this project will appear here.
          </>
        ) : undefined
      }
    />
  );
}
