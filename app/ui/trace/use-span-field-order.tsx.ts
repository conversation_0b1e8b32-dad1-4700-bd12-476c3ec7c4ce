import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import {
  type SpanIFrame,
  type SpanFieldOrderItem,
} from "@braintrust/core/typespecs";
import { type DragEndEvent } from "@dnd-kit/core";
import { useCallback, useContext, useEffect, useMemo, useState } from "react";
import { LexoRank } from "lexorank";
import { toast } from "sonner";
import { type TraceViewParams } from "./trace";
import { type CustomColumnDefinition } from "#/utils/custom-columns/use-custom-columns";

export const useSpanFieldOrder = ({
  defaultFields,
  objectType,
  customColumns,
  spanIframes,
}: {
  defaultFields?: SpanField[];
  objectType: TraceViewParams["objectType"];
  customColumns?: CustomColumnDefinition[];
  spanIframes: SpanIFrame[];
}) => {
  const { projectId, projectSettings, mutateProject } =
    useContext(ProjectContext);

  const saveSpanFieldOrder = useCallback(
    async (order: SpanFieldOrderItem[]) => {
      const resp = await fetch(`/api/project/patch_id`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: projectId,
          settings: {
            ...projectSettings,
            spanFieldOrder: order,
          },
        }),
      });

      if (!resp.ok) {
        toast.error(`Failed to save span field order`, {
          description: `${await resp.text()}`,
        });
      } else {
        mutateProject();
      }
    },
    [projectId, projectSettings, mutateProject],
  );

  const [spanFieldOrderState, setSpanFieldOrderState] = useState<
    SpanFieldOrderItem[]
  >(
    initializeSpanFieldOrder({
      defaultFields,
      savedOrder: projectSettings?.spanFieldOrder ?? [],
      objectType,
      customColumns,
      spanIframes,
    }),
  );

  useEffect(() => {
    setSpanFieldOrderState(
      initializeSpanFieldOrder({
        defaultFields,
        savedOrder: projectSettings?.spanFieldOrder ?? [],
        objectType,
        customColumns,
        spanIframes,
      }),
    );
  }, [defaultFields, projectSettings, objectType, customColumns, spanIframes]);

  const order = useMemo(() => {
    return spanFieldOrderState.sort((a, b) =>
      a.position.localeCompare(b.position),
    );
  }, [spanFieldOrderState]);

  const onDragEnd = useCallback(
    (e: DragEndEvent) => {
      const { active, over } = e;
      if (
        over &&
        active.id !== over.id &&
        typeof active.id === "string" &&
        typeof over.id === "string"
      ) {
        const activeIndex = spanFieldOrderState.findIndex(
          (item) => item.column_id === active.id,
        );
        const overIndex = spanFieldOrderState.findIndex(
          (item) => item.column_id === over.id,
        );
        const overPosition = LexoRank.parse(
          spanFieldOrderState[overIndex].position,
        );

        const newRank =
          overIndex > activeIndex
            ? overIndex === spanFieldOrderState.length - 1
              ? overPosition.genNext()
              : overPosition.between(
                  LexoRank.parse(spanFieldOrderState[overIndex + 1].position),
                )
            : overIndex === 0
              ? overPosition.genPrev()
              : overPosition.between(
                  LexoRank.parse(spanFieldOrderState[overIndex - 1].position),
                );

        const newState = spanFieldOrderState.map((item, i) =>
          i === activeIndex ? { ...item, position: newRank.toString() } : item,
        );
        setSpanFieldOrderState(newState);
        saveSpanFieldOrder(newState).catch(console.error);
      }
    },
    [spanFieldOrderState, saveSpanFieldOrder],
  );

  const onToggleLayout = useCallback(
    (fieldId: string) => {
      const newState = spanFieldOrderState.map((item) =>
        item.column_id === fieldId
          ? {
              ...item,
              layout:
                item.layout === "full"
                  ? ("two_column" as const)
                  : ("full" as const),
            }
          : item,
      );
      setSpanFieldOrderState(newState);
      saveSpanFieldOrder(newState).catch(console.error);
    },
    [spanFieldOrderState, saveSpanFieldOrder],
  );

  return {
    order,
    onDragEnd,
    onToggleLayout,
  };
};

function initializeSpanFieldOrder({
  defaultFields = DEFAULT_FIELDS,
  savedOrder,
  objectType,
  customColumns,
  spanIframes,
}: {
  defaultFields?: SpanField[];
  savedOrder: SpanFieldOrderItem[];
  objectType: TraceViewParams["objectType"];
  customColumns?: CustomColumnDefinition[];
  spanIframes: SpanIFrame[];
}) {
  const base = savedOrder.filter((item) => item.object_type === objectType);

  const initialPosition = base.reduce((acc, item) => {
    if (acc.localeCompare(item.position) < 0) {
      return item.position;
    }
    return acc;
  }, LexoRank.middle().toString());
  let lastPosition = LexoRank.parse(initialPosition);

  for (const field of defaultFields) {
    if (!base.some((item) => item.column_id === field)) {
      lastPosition = lastPosition.genNext();
      base.push({
        object_type: objectType,
        column_id: field,
        position: lastPosition.toString(),
        layout: "full",
      });
    }
  }

  for (const customColumn of customColumns ?? []) {
    if (
      !base.some((item) => item.column_id === `customColumn-${customColumn.id}`)
    ) {
      lastPosition = lastPosition.genNext();
      base.push({
        object_type: objectType,
        column_id: `customColumn-${customColumn.id}`,
        position: lastPosition.toString(),
      });
    }
  }
  for (const spanIframe of spanIframes) {
    if (
      !base.some((item) => item.column_id === `spanIframe-${spanIframe.id}`)
    ) {
      lastPosition = lastPosition.genNext();
      base.push({
        object_type: objectType,
        column_id: `spanIframe-${spanIframe.id}`,
        position: lastPosition.toString(),
        layout: "full",
      });
    }
  }

  return base;
}

export enum SpanField {
  METRICS = "metrics",
  SCORES = "scores",
  HUMAN_REVIEW = "humanReview",
  ERROR = "error",
  INPUT = "input",
  OUTPUT_VS_EXPECTED = "outputVsExpected",
  OUTPUT = "output",
  EXPECTED = "expected",
  METADATA = "metadata",
  ACTIVITY = "activity",
}

const DEFAULT_FIELDS = [
  SpanField.METRICS,
  SpanField.SCORES,
  SpanField.HUMAN_REVIEW,
  SpanField.ERROR,
  SpanField.INPUT,
  SpanField.OUTPUT_VS_EXPECTED,
  SpanField.OUTPUT,
  SpanField.EXPECTED,
  SpanField.METADATA,
];
