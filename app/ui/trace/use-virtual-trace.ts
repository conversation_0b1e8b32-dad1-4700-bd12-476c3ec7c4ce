import { type DataObjectType } from "#/utils/btapi/btapi";
import { fetchBtqlPaginated, useFetchBtqlOptions } from "#/utils/btql/btql";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  type BtqlQueryBuilder,
  useBtqlQueryBuilder,
} from "#/utils/btql/use-query-builder";
import { type AliasExpr } from "@braintrust/btql/parser";
import { z } from "zod";
import { useMemo } from "react";
import {
  type Span,
  spanAttributesSchema,
  spanMetricNames,
  type SpanMetrics,
} from "@braintrust/local";
import {
  fillSpanScores,
  fillSpanMetrics,
  parseScoreData,
  type PreviewSpan,
  sortSpanChildren,
  topSortSpans,
  type PreviewTrace,
} from "./graph";
import { objectReferenceSchema } from "@braintrust/core/typespecs";
import { type RealtimeState } from "@braintrust/local/app-schema";
import { type ModelCosts } from "#/ui/prompts/models";

/**
 *  {
 *    end?: number | null;
 *    start?: number | null;
 *    ...
 *    retries?: number | null;
 *    [k: string]: number | null | undefined;   // fallback
 *  }
 */
export const metricsSchema = z
  .object(
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    Object.fromEntries(
      spanMetricNames.map((m) => [m, z.number().nullish()]),
    ) as unknown as {
      [K in (typeof spanMetricNames)[number]]: z.ZodType<
        number | null | undefined
      >;
    },
  )
  .catchall(z.any())
  // filter out any non-number values
  .transform((metrics) => {
    return Object.fromEntries(
      Object.entries(metrics).filter(
        ([_, v]) => typeof v === "number" || v === null || v === undefined,
      ),
    );
  });

export const baseSpanOverviewSchema = z.object({
  id: z.string(),
  _xact_id: z.string(),
  _pagination_key: z.string().nullish(),
  created: z
    .string()
    .datetime()
    // This is for Clickhouse's demented datetime format
    .or(z.string().regex(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)),
  root_span_id: z.string(),
  span_id: z.string(),
  tags: z.array(z.string()).nullish(),
  origin: objectReferenceSchema.nullish(),
  model: z.string().nullish(), // metadata.model
});
export const spanOverviewSchema = baseSpanOverviewSchema.extend({
  span_parents: z.array(z.string()).nullish(),
  error: z.unknown().nullish(),
  metrics: metricsSchema.nullish(),
  scores: z.record(z.number().nullable()).nullish(),
  span_attributes: spanAttributesSchema.partial().nullish(),
  metadata: z.unknown().nullish(),
});
export type SpanOverviewRow = z.infer<typeof spanOverviewSchema>;

const spanOverviewExprs = new Map([["model", "metadata.model"]]);

export function makeSpanOverviewProjection({
  builder,
  objectType,
}: {
  builder: BtqlQueryBuilder;
  objectType: DataObjectType;
}): AliasExpr[] {
  const schema =
    objectType === "dataset" ? baseSpanOverviewSchema : spanOverviewSchema;
  const aliases = Object.keys(schema.shape).filter(
    (name) => !spanOverviewExprs.has(name),
  );
  return aliases
    .map(
      (alias): AliasExpr => ({
        alias,
        expr: builder.ident(alias),
      }),
    )
    .concat(
      Array.from(spanOverviewExprs).map(
        ([alias, expr]): AliasExpr => ({
          alias,
          expr: { btql: expr },
        }),
      ),
    );
}

export function useVirtualTrace({
  rowId,
  objectId,
  objectType,
  isFastSummaryEnabled,
  allowEmpty,
  modelCosts,
}: {
  rowId: string | null;
  objectId: string | null;
  objectType: DataObjectType;
  allowEmpty?: boolean;
  isFastSummaryEnabled?: boolean;
  modelCosts?: Record<string, ModelCosts>;
}): {
  trace: PreviewTrace | null;
  buildTraceError: Error | null;
  hasNoData: boolean;
  comparisonKey: string;
  hasLoaded: boolean | undefined;
  isPending: boolean;
  isQueryLoading: boolean;
  realtimeState: RealtimeState | undefined;
} {
  const builder = useBtqlQueryBuilder({});
  const btqlOptions = useFetchBtqlOptions();

  const queryClient = useQueryClient();
  const isQueryEnabled = !!rowId && !!objectId;

  // Fetch the full list of spans in a trace up front. Eventually, we might want to virtualize
  // this too, but for now, we assume that it's fast enough.
  const {
    data: spanTreeData,
    isLoading,
    isPending,
    isPlaceholderData,
  } = useQuery(
    {
      queryKey: makeTraceQueryKey(objectType, objectId, rowId),
      enabled: isQueryEnabled,
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
        const selectProjection = makeSpanOverviewProjection({
          builder,
          objectType,
        });

        return await fetchBtqlPaginated(
          {
            args: {
              query: {
                filter: {
                  op: "eq",
                  left: { btql: "id" },
                  right: {
                    op: "literal",
                    value: rowId,
                  },
                },
                from: builder.from(
                  objectType,
                  objectId ? [objectId] : [],
                  "traces",
                ),
                select: selectProjection,
                limit: 1,
              },
              brainstoreRealtime: true,
            },
            ...btqlOptions,
            schema: spanOverviewSchema,
            signal,
          },
          1000,
        );
      },
      throwOnError: false,
      staleTime: Infinity,
    },
    queryClient,
  );

  const { trace, error: buildTraceError } = useMemo(() => {
    if (!isQueryEnabled && allowEmpty) {
      return { trace: null, error: null };
    }
    return buildVirtualTrace(spanTreeData?.data ?? [], modelCosts);
  }, [spanTreeData?.data, isQueryEnabled, allowEmpty, modelCosts]);

  return {
    trace,
    buildTraceError,
    hasNoData: !!spanTreeData && spanTreeData.data.length === 0,
    isQueryLoading: isPlaceholderData || isLoading,
    comparisonKey: "", // XXX TODO
    hasLoaded: !isLoading,
    isPending,
    realtimeState: spanTreeData?.realtime_state,
  };
}

export function buildVirtualTrace(
  rows: SpanOverviewRow[],
  modelCosts: Record<string, ModelCosts> | undefined,
): { trace: PreviewTrace | null; error: Error | null } {
  if (rows.length === 0) {
    return { trace: null, error: null };
  }

  let root: PreviewSpan | null = null;
  for (const row of rows) {
    if ((row.span_parents ?? []).length === 0) {
      console.assert(root == null, "root already set");
      root = makePreviewSpanFromRow(row);
    }
  }

  if (!root) {
    console.warn("root span not found", rows);
    return { trace: null, error: new Error("Root span not found") };
  }

  const spans: Record<string, PreviewSpan> = { [root.span_id]: root };
  for (const row of rows) {
    if (root.root_span_id !== row.root_span_id) {
      // This should not be possible because the caller of this function should be
      // filtering for rows that match one of the root span rows, so the root
      // span row itself must exist.
      // However, we have seen this happen in the wild, so we handle it here.
      console.warn(
        `root span id ${row.root_span_id} for span ${row.span_id} does not match root span ${root.span_id}. All span ids: ${JSON.stringify(Object.keys(spans), null, 2)}`,
      );
      continue;
    }
    if (row.span_id === root.span_id) {
      continue;
    }

    const span = makePreviewSpanFromRow(row);
    spans[row.span_id] = span;
  }

  const sortedSpans = topSortSpans(spans);
  const topSortIndex: Map<string, number> = new Map();
  for (const [idx, span] of sortedSpans.entries()) {
    topSortIndex.set(span.span_id, idx);
  }

  for (const row of rows) {
    const span = spans[row.span_id];
    if (!span) {
      continue;
    }
    const span_parents = span.span_parents ?? [];
    if (span_parents.length > 0) {
      // Even though a span can technically have multiple parents, this
      // functionality is not used anywhere so we only handle the case of a
      // single parent.
      if (span_parents.length > 1) {
        console.warn("Only using first parent of span", row.span_id);
      }

      if (
        topSortIndex.get(span_parents[0])! > topSortIndex.get(span.span_id)! ||
        span.span_id === span_parents[0]
      ) {
        console.warn(
          `Cycle detected in span graph at span ${span.span_id}. Skipping its parent.`,
        );
        continue;
      }

      // If we find the actual parent span, use it and set `span.parent_span_id`
      // to point to it. Otherwise, we have an orphan span which we put
      // underneath the root_span for display purposes, but we don't set
      // `span.parent_span_id` to indicate that it's an orphan.
      const parentSpan = (() => {
        const parent_span_id = span_parents[0];
        if (parent_span_id in spans) {
          const parentSpan = spans[parent_span_id];
          span.parent_span_id = parentSpan.span_id;
          return parentSpan;
        } else {
          console.warn(
            `parent span ${parent_span_id} not found for span ${span.span_id}. Using root span ${root.span_id} instead`,
          );
          return root;
        }
      })();
      parentSpan.children.push(span);
    }
  }

  const colored: Record<string, boolean> = {};
  for (const span of Object.values(spans)) {
    fillSpanScores(span, colored);
  }

  // Fill metrics for all spans (token aggregation only, no cost calculation)
  const metricsColored: Record<string, boolean> = {};
  for (const span of Object.values(spans)) {
    fillSpanMetrics(span, metricsColored, modelCosts);
  }

  // For each span, sort its children by start time
  for (const span of Object.values(spans)) {
    sortSpanChildren(span);
  }

  return { trace: { root, spans }, error: null };
}

export function makeTraceQueryKey(
  objectType: DataObjectType,
  objectId: string | null,
  traceRowId: string | null,
) {
  return ["virtualTraceSpanTree", objectType, objectId, traceRowId];
}

export function makePreviewSpanFromRow(row: SpanOverviewRow): PreviewSpan {
  return {
    id: row.id,
    span_id: row.span_id,
    root_span_id: row.root_span_id,
    span_parents: row.span_parents ?? [],
    parent_span_id: null,
    scores: parseScores({
      span: { span_id: row.span_id },
      scores: row?.scores,
    }),
    data: {
      ...row,
      span_attributes: {
        ...row.span_attributes,
        name: row.span_attributes?.name ?? "",
      },
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      metrics: row.metrics as
        | (SpanMetrics & Record<string, number>)
        | undefined,
      scores: row.scores ?? {},
      tags: row.tags ?? [],
      error: row.error ?? undefined,
      origin: row.origin ?? undefined,
      model: row.model ?? undefined,
    },
    children: [],
  };
}

const rawScoreSchema = z.record(z.number().nullable());

export function parseScores({
  span,
  scores,
}: {
  span: { span_id: string; scores?: Span["scores"] };
  scores: unknown;
}) {
  const scoresObj =
    typeof scores === "string" ? JSON.parse(scores || "null") : scores;
  const parsedScores = rawScoreSchema.safeParse(scoresObj);
  const newScores = parsedScores.success
    ? parseScoreData(span.span_id, parsedScores.data, true)
    : undefined;

  // Keep the old scores because some of the span's scores were computed via child spans
  // while building the trace
  return { ...span.scores, ...newScores };
}
