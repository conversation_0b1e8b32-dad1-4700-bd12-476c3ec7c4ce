import type { BTQLResponse, InferSchema } from "#/utils/btql/btql";
import { isReservedMetadataPath } from "#/utils/metadata";
import { type Type } from "@braintrust/btql/binder";

export interface InferTreeNode {
  name: string;
  type?: Type; // no value if no type
  children: InferTreeNode[];
}

export interface InferField {
  namePath: string[];
  topValues: { count: number; value?: unknown }[];
  type: Type;
}

export const inferDataToInferFields = (
  inferData: BTQLResponse<InferSchema> | null | undefined,
): InferField[] => {
  return (
    inferData?.data.reduce<InferField[]>((acc, r) => {
      const namePath = r.name.slice(0);
      if (isReservedMetadataPath(namePath)) {
        return acc;
      }
      acc.push({
        namePath,
        topValues: r.top_values.slice(0),
        type: r.type,
      });
      return acc;
    }, []) || []
  ).toSorted((a, b) =>
    a.namePath.join("|").localeCompare(b.namePath.join("|")),
  );
};

export const getInferField = (fields: InferField[], path: string[]) => {
  return fields.find((f) => f.namePath.join("|") === path.join("|"));
};
