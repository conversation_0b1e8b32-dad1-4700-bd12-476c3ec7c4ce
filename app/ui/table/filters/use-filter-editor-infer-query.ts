import { fetchInferBtql } from "#/utils/btql/btql";
import { useBtqlFlags, useIsFeatureEnabled } from "#/lib/feature-flags";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import * as Query from "#/utils/btql/query-builder";
import { useQuery } from "@tanstack/react-query";

const SUPPORTED_FIELDS = new Set([
  "metadata",
  "input",
  "output",
  "expected",
  "id",
  "metrics",
  "scores",
]);

export function useFilterEditorInferQuery({
  objectType,
  objectId,
  fieldName,
}: {
  objectType?: string | null;
  objectId?: string | null;
  fieldName: string;
}) {
  const org = useOrg();
  const btqlFlags = useBtqlFlags();
  const schemaInference = useIsFeatureEnabled("schemaInference");
  const { getOrRefreshToken } = useSessionToken();

  const hasSchemaInference = schemaInference;

  const query = useQuery({
    queryKey: ["filterEditorInferData", objectType, objectId, fieldName],
    queryFn: async ({ signal }: { signal: AbortSignal }) =>
      objectId
        ? await fetchInferBtql({
            args: {
              query: {
                from: Query.from(String(objectType), [objectId]),
                filter: {
                  op: "eq",
                  left: { op: "ident", name: ["is_root"] },
                  right: { op: "literal", value: true },
                },
                infer: [{ op: "ident", name: [fieldName] }],
                limit: 500,
              },
              brainstoreRealtime: true,
            },
            btqlFlags,
            apiUrl: org.api_url,
            getOrRefreshToken,
            signal,
          })
        : null,
    enabled:
      !!objectType &&
      !!objectId &&
      hasSchemaInference &&
      SUPPORTED_FIELDS.has(fieldName),
    gcTime: 1000 * 30,
  });
  return query;
}
