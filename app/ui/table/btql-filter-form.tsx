import { <PERSON><PERSON> } from "#/ui/button";
import { Check } from "lucide-react";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { cn } from "#/utils/classnames";
import Link from "next/link";
import useFilterSortBarSearch from "#/ui/use-filter-sort-search";
import {
  type ClauseType,
  type ClauseChecker,
  type Search,
  type ClauseSpec,
} from "#/utils/search/search";
import { type ClientOptions } from "openai";
import { type AISearchResult } from "@braintrust/local";
import { inputClassName } from "#/ui/input";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useQueryFunc } from "#/utils/react-query";
import { useOrg } from "#/utils/user";
import { type getProjectSummary } from "#/app/app/[org]/org-actions";
import { BtqlEditor } from "#/app/app/[org]/btql/btql-editor";

export function BTQLFilterForm({
  clauseChecker,
  setSearch,
  runAISearch,
  clauseToReplace,
  setOpen,
  value,
  setValue,
  experimentId,
}: {
  clauseChecker: ClauseChecker | null;
  setSearch: Dispatch<SetStateAction<Search>>;
  runAISearch?: (
    openAIOpts: ClientOptions,
    query: string,
  ) => Promise<AISearchResult>;
  clauseToReplace?: ClauseSpec<ClauseType>;
  setOpen: (open: boolean) => void;
  value: string;
  setValue: (value: string) => void;
  experimentId?: string;
}) {
  const { applySearch, loading, isValidBTQL } = useFilterSortBarSearch({
    runAISearch,
    clauseChecker,
    setSearch,
  });

  const supportsAISearch = Boolean(runAISearch);

  const [isBTQLValid, setBTQLValid] = useState(false);

  useEffect(() => {
    if (!value) {
      setBTQLValid(false);
      return;
    }
    const check = async () => {
      const result = await isValidBTQL(value);
      setBTQLValid(result.valid);
    };
    check();
  }, [value, isValidBTQL]);

  const disabled = useMemo(
    () => !value || (!supportsAISearch && !isBTQLValid),
    [value, isBTQLValid, supportsAISearch],
  );

  const onSubmitBTQL = useCallback(async () => {
    if (disabled) return;
    await applySearch(value, clauseToReplace, {
      originType: "btql",
      label: value,
      ...(experimentId
        ? {
            comparison: {
              experimentId,
            },
          }
        : {}),
    });
    setOpen(false);
  }, [applySearch, value, setOpen, clauseToReplace, experimentId, disabled]);

  const project = useContext(ProjectContext);
  const org = useOrg();
  const { data: projects } = useQueryFunc<typeof getProjectSummary>({
    fName: "getProjectSummary",
    args: { org_name: org.name },
  });

  const projectSummary = useMemo(() => {
    return projects?.find((p) => p.project_id === project?.projectId);
  }, [projects, project]);

  return (
    <>
      <div className="p-4">
        <BtqlEditor
          projectData={projectSummary ? [projectSummary] : []}
          mode="expr"
          onValueChange={setValue}
          value={value}
          autoFocus
          placeholder={
            supportsAISearch
              ? "Enter BTQL or natural language query"
              : "Enter BTQL"
          }
          onMetaEnter={onSubmitBTQL}
          className={cn(inputClassName, "h-auto")}
        />
      </div>
      <div className="flex flex-none items-center gap-3 border-t px-4 py-2 border-primary-100">
        <Button
          size="xs"
          variant="primary"
          isLoading={loading}
          disabled={disabled}
          onClick={onSubmitBTQL}
          Icon={isBTQLValid ? Check : undefined}
        >
          {isBTQLValid ? (clauseToReplace ? "Update" : "Apply") : "Submit"}
        </Button>
        <div className="w-full text-balance text-right text-xs text-primary-500">
          {supportsAISearch ? "Enter natural language to convert to " : ""}
          <Link
            className="font-medium text-accent-600"
            href="/docs/reference/btql"
            target="_blank"
          >
            BTQL
          </Link>
        </div>
      </div>
    </>
  );
}
