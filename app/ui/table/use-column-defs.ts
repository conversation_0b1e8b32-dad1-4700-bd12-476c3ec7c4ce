import { type TypeMap, type Field, DataType } from "apache-arrow";
import { useMemo, useRef } from "react";
import {
  RowComparisonColumnDefFactory,
  RowSelectionColumnDefFactory,
  RowStarColumnDefFactory,
} from "./display-columns";
import { type UpdateRowFn } from "#/utils/mutable-object";
import { type RowComparisonFormatterProps } from "./formatters/row-comparison-formatter";
import { type MultilineRowProps } from "#/ui/virtual-table-body";
import { type SummaryBreakdownData } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(charts)/(SummaryBreakdown)/use-summary-breakdown";
import { type RowData, type ColumnDef } from "@tanstack/react-table";
import { fieldToColumn, type FormatterMap } from "#/ui/field-to-column";
import { type DuckDBJSONStruct } from "#/utils/schema";
import { type PathTree } from "#/utils/display-paths";
import { btGroupColumnNames } from "./grouping/queries";
import { getDiffFieldIndex } from "#/utils/diffs/diff-objects";
import { type CustomColumnDefinition } from "#/utils/custom-columns/use-custom-columns";

export interface FieldInfo {
  field: Field;
  path: string[];
  indexColumnId?: string;
}

export type FieldMap = { [key: string]: FieldInfo };

export interface SizeConstraintsMap {
  [key: string]: {
    minSize?: number;
    size?: number;
  };
}

type Args<TData extends TypeMap, TsData, TsValue> = {
  fields: Field<TData[keyof TData]>[] | undefined;
  columnReorderer?: (
    columns: ColumnDef<TsData, TsValue>[],
  ) => ColumnDef<TsData, TsValue>[];
  customColumnsColumns?: CustomColumnDefinition[];
  displayPaths?: PathTree;
  enableStarColumn?: boolean;
  formatters?: FormatterMap<TsData, TsValue>;
  multilineRow?: MultilineRowProps;
  neverVisibleColumns?: Set<string>;
  rowComparisonProps?: RowComparisonFormatterProps;
  rowSelection?: Record<string, boolean> | null;
  showRowNumber?: boolean;
  sizeConstraintsMap?: SizeConstraintsMap;
  summaryBreakdownData?: SummaryBreakdownData;
  typeHints?: DuckDBJSONStruct;
  updateRow?: UpdateRowFn;
  refetchTooltipContentData?: (
    fieldName: string,
    rowId: string,
    previewLength?: number,
  ) => Promise<unknown>;
};

export default function useColumnDefs<
  TData extends TypeMap,
  TsData extends RowData,
  TsValue,
>({
  fields: fieldsProp,
  columnReorderer,
  customColumnsColumns,
  displayPaths,
  enableStarColumn,
  formatters,
  multilineRow,
  neverVisibleColumns,
  rowComparisonProps,
  rowSelection,
  showRowNumber,
  sizeConstraintsMap,
  summaryBreakdownData,
  typeHints,
  updateRow,
  refetchTooltipContentData,
}: Args<TData, TsData, TsValue>) {
  const lastSelectedRowId = useRef<string | undefined>(undefined);

  // The following column definitions are memoized individually since they aren't dependent on
  // formatters/arrowTable schema fields/etc. Memoizing them with only their own dependencies
  // prevents extraneous column re-mounts.
  const rowSelectionColumnDef = useMemo(
    () =>
      // eslint-disable-next-line react-compiler/react-compiler -- linter bug? not accessing a ref during render here...
      RowSelectionColumnDefFactory<TsData, TsValue>({
        showRowNumber,
        lastSelectedRowId,
      }),
    [showRowNumber, lastSelectedRowId],
  );
  const rowStarColumnDef = useMemo(
    () => updateRow && RowStarColumnDefFactory<TsData, TsValue>({ updateRow }),
    [updateRow],
  );
  const rowComparisonColumnDef = useMemo(
    () =>
      rowComparisonProps &&
      RowComparisonColumnDefFactory<TsData, TsValue>({ rowComparisonProps }),
    [rowComparisonProps],
  );

  const hasRowSelection = !!rowSelection;
  const moreText = !!multilineRow;
  const hasSummary = !!summaryBreakdownData;
  // Likewise, splitting arrow table schema field derived columns into their
  // own memo ensures that if display column definitions change, the field column
  // definitions remain stable.
  const [fieldColumns, fieldMap] = useMemo(() => {
    const columns: ColumnDef<TsData, TsValue>[] = [];
    const fields: FieldMap = {};
    if (fieldsProp) {
      const comparisonFields = parseComparisonFields(fieldsProp);

      const fieldToColumnOptions = {
        path: [],
        displayPaths,
        typeHints,
        formatters: formatters || {},
        fieldMap: fields,
        sizeConstraintsMap,
        moreText,
        hasSummary,
        customColumns: customColumnsColumns,
        comparisonFields,
        refetchTooltipContentData,
      };

      columns.push(
        ...fieldsProp
          .flatMap((field, i) => {
            return fieldToColumn({
              field,
              id_prefix: `${i}`,
              ...fieldToColumnOptions,
            });
          })
          .filter(
            (field) =>
              (!neverVisibleColumns ||
                !neverVisibleColumns.has(field.meta?.name ?? "")) &&
              !btGroupColumnNames.has(field.meta?.name ?? ""),
          ),
      );
    }
    return [columns, fields];
  }, [
    fieldsProp,
    displayPaths,
    typeHints,
    formatters,
    sizeConstraintsMap,
    moreText,
    hasSummary,
    customColumnsColumns,
    neverVisibleColumns,
    refetchTooltipContentData,
  ]);

  const columns = useMemo(() => {
    const columns = [...fieldColumns];
    if (rowComparisonColumnDef && !rowComparisonProps?.gridLayout) {
      columns.unshift(rowComparisonColumnDef);
    }
    if (enableStarColumn && rowStarColumnDef) {
      columns.unshift(rowStarColumnDef);
    }
    if (hasRowSelection) {
      columns.unshift(rowSelectionColumnDef);
    }

    const reorderedColumns = columnReorderer
      ? columnReorderer(columns)
      : columns;

    return reorderedColumns;
  }, [
    columnReorderer,
    enableStarColumn,
    fieldColumns,
    hasRowSelection,
    rowComparisonColumnDef,
    rowComparisonProps?.gridLayout,
    rowSelectionColumnDef,
    rowStarColumnDef,
  ]);

  return { columns, fieldMap };
}

function parseComparisonFields<T extends TypeMap>(fields: Field<T[keyof T]>[]) {
  const btInternalField = fields.find((f) => f.name === "__bt_internal");
  if (!btInternalField) {
    return undefined;
  }

  const comparisonFields: (Field<T[keyof T]>[] | undefined)[] = [];
  btInternalField.type.children.forEach((f) => {
    const diffFieldIndex = getDiffFieldIndex(f.name);
    if (diffFieldIndex == null || !DataType.isStruct(f.type)) {
      return;
    }

    const dataField = f.type.children.find((cf) => cf.name === "data");
    if (dataField == null || !DataType.isStruct(dataField.type)) {
      return;
    }
    comparisonFields[diffFieldIndex] = dataField.type.children;
  });
  return comparisonFields;
}
