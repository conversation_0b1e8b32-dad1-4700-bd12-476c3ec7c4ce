import { Button } from "#/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "#/ui/dialog";
import { PlainInput } from "#/ui/plain-input";
import { useRef, useState } from "react";

export function OneLineTextPrompt({
  onSubmit,
  title,
  description,
  submitLabel,
  fieldName,
  defaultValue,
  open,
  onOpenChange,
  placeholder,
  children,
  validate,
}: {
  onSubmit: (newValue: string) => void;
  submitLabel?: string;
  title: string;
  description?: string;
  fieldName: React.ReactNode;
  defaultValue?: string;
  open: boolean;
  onOpenChange: (open: boolean, reason?: "submit" | "close") => void;
  placeholder?: string;
  children?: React.ReactNode;
  validate?: (value: string) => string | null;
}) {
  const [error, setError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        setError(null);
        onOpenChange(open, "close");
      }}
    >
      <DialogContent className="sm:max-w-[600px]">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            const error = validate?.(inputRef.current?.value ?? "");
            if (error) {
              setError(error);
              return;
            }
            const inputValue = inputRef.current?.value;
            if (inputValue) {
              onSubmit(inputValue);
            }
            onOpenChange(false, "submit");
          }}
        >
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            {description && (
              <DialogDescription>{description}</DialogDescription>
            )}
            {children}
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <label htmlFor="prompt_name" className="text-right">
                {fieldName}
              </label>
              <PlainInput
                id="prompt_name"
                defaultValue={defaultValue}
                className="col-span-3"
                ref={inputRef}
                autoComplete="off"
                placeholder={placeholder}
                onChange={(e) => {
                  setError(null);
                }}
              />
              {error && (
                <div className="col-span-3 col-start-2 text-xs text-red-500">
                  {error}
                </div>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button type="submit">{submitLabel || "Save"}</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
