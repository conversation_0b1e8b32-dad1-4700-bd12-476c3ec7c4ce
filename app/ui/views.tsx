import { useCallback, useEffect } from "react";
import { useMemo, useState } from "react";
import {
  type View,
  type ViewParams,
  type ViewProps,
  makeRequiredPatchParams,
  useViewQuery,
  useViewsQueryKey,
} from "#/utils/view/use-view";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useOrg } from "#/utils/user";
import { MinVersion, useFeatureFlags } from "#/lib/feature-flags";
import { apiDelete, apiPatch, apiPostCors } from "#/utils/btapi/fetch";
import { BT_FOUND_EXISTING_HEADER } from "@braintrust/core";
import { useSessionToken } from "#/utils/auth/session-token";
import { toastAndLogError } from "#/utils/view/view-utils";
import { ViewDropdown } from "./views/view-dropdown";
import { CreateViewDialog } from "./views/create-view-dialog";
import { RenameViewDialog } from "./views/rename-view-dialog";
import { DeleteViewDialog } from "./views/delete-view-dialog";
import { useInitializeView } from "./views/use-initialize-view";
import { normalizeSearch } from "#/utils/search/search";
import {
  viewOptionsSchema,
  type ViewData,
  type ViewOptions,
} from "@braintrust/core/typespecs";
import useEvent from "react-use-event-hook";

function makeDuplicateViewError(name: string) {
  return `A view with the name ${name} already exists`;
}

export function Views<ExperimentsCharting extends boolean = false>({
  pageIdentifier,
  viewParams,
  defaultViewName = "All rows",
  viewProps,
}: {
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  defaultViewName?: string;
  viewProps: ViewProps<ExperimentsCharting>;
}) {
  const { getOrRefreshToken } = useSessionToken();
  const { api_url: apiUrl } = useOrg();

  const {
    flags: { views: areViewsEnabled },
  } = useFeatureFlags();

  useEffect(() => {
    if (!areViewsEnabled) {
      console.warn(
        `Views API endpoint not available. Please upgrade your API server to a version >= ${MinVersion.views} to access table views.`,
      );
    }
  }, [areViewsEnabled]);

  const queryKey = useViewsQueryKey({
    pageIdentifier,
    getViewArgs: {
      apiUrl,
      getOrRefreshToken,
      viewParams,
    },
  });
  const { data, isPending } = useViewQuery({
    viewParams,
    pageIdentifier,
  });

  const defaultView = useMemo(
    () => ({ id: null, builtin: true, name: defaultViewName }),
    [defaultViewName],
  );

  const views: View[] | undefined = useMemo(() => {
    return [defaultView, ...(data ?? [])];
  }, [data, defaultView]);

  const { search: _, resetState } = viewProps;

  const onViewInitialized = useCallback(
    (view: View | null) => {
      resetState(view, { initialize: true });
    },
    [resetState],
  );
  const [viewName, setViewName] = useInitializeView({
    pageIdentifier,
    viewParams,
    onViewInitialized,
  });
  const selectedView: View | undefined =
    data?.find((v) => v.name === viewName) ?? defaultView;

  const [isCreateViewDialogOpen, setIsCreateViewDialogOpen] = useState(false);
  const [isRenameViewDialogOpen, setIsRenameViewDialogOpen] = useState(false);
  const [isDeleteViewDialogOpen, setIsDeleteViewDialogOpen] = useState(false);
  const deleteViewMutationFn = async (id: string | null) => {
    if (!id) {
      throw new Error("Cannot delete default view");
    }
    const sessionToken = await getOrRefreshToken();
    const resp = await apiDelete({
      url: `${apiUrl}/v1/view/${id}`,
      sessionToken,
      payload: makeRequiredPatchParams(viewParams),
      alreadySerialized: false,
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    return await resp.json();
  };

  const loadView = (view: View | null) => {
    setViewName(view?.name ?? null);
    resetState(view);
  };

  const queryClient = useQueryClient();
  const { mutate: deleteView } = useMutation({
    mutationFn: deleteViewMutationFn,
    onMutate: async (id: string | null) => {
      await queryClient.cancelQueries({ queryKey });
      const previousViews = queryClient.getQueryData<View[]>(queryKey);
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.filter((v) => v.id !== id),
      );
      loadView(null);
      return previousViews;
    },
    onError: (error, _, previousViews) => {
      toastAndLogError("delete", error);
      queryClient.setQueryData<View[]>(queryKey, previousViews);
    },
    onSuccess: async (view: View) => {
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.filter((v) => v.id !== view.id),
      );
    },
  });

  const createViewMutationFn = async (name: string) => {
    if (!viewParams) {
      throw new Error("viewParams not set");
    }
    const sessionToken = await getOrRefreshToken();
    if (views?.some((v) => v.name === name)) {
      throw new Error(makeDuplicateViewError(name));
    }

    const parsedOptions = viewOptionsSchema.safeParse(
      viewProps.viewOptionsToSave,
    );
    if (!parsedOptions.success) {
      throw new Error(`Invalid view options: ${parsedOptions.error.message}`);
    }
    const payload: Omit<View, "id"> = {
      object_type: viewParams.objectType,
      object_id: viewParams.objectId,
      view_type: viewParams.viewType,
      name,
      view_data: { search: normalizeSearch(viewProps.search) },
      options: viewProps.viewOptionsToSave,
    };
    const resp = await apiPostCors({
      url: `${apiUrl}/v1/view`,
      sessionToken,
      payload,
      alreadySerialized: false,
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    if (resp.headers.has(BT_FOUND_EXISTING_HEADER)) {
      throw new Error(makeDuplicateViewError(name));
    }
    return await resp.json();
  };

  const {
    mutate: createView,
    variables: optimisticCreatedName,
    isPending: isCreatePending,
  } = useMutation({
    mutationFn: createViewMutationFn,
    onMutate: async () => await queryClient.cancelQueries({ queryKey }),
    onError: (error) => toastAndLogError("create", error),
    onSuccess: async (result: View) => {
      queryClient.setQueryData<View[]>(queryKey, (prev) => [
        ...(prev ?? []),
        result,
      ]);
      setViewName(result.name ?? null);
      // reset to clear url params
      resetState(result);
    },
  });

  const saveViewMutationFn = async ({
    viewId,
    viewOptionsToSave,
    viewDataToSave,
  }: {
    viewId: string | undefined | null;
    viewOptionsToSave?: ViewOptions;
    viewDataToSave?: ViewData;
  }) => {
    const sessionToken = await getOrRefreshToken();
    if (!viewId) {
      throw new Error("Invalid view");
    }
    const payload = {
      ...makeRequiredPatchParams(viewParams),
      options: viewOptionsToSave,
      view_data: viewDataToSave,
    };
    const resp = await apiPatch({
      url: `${apiUrl}/v1/view/${viewId}`,
      sessionToken,
      payload,
      alreadySerialized: false,
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    return await resp.json();
  };

  const { mutate: saveViewMutation } = useMutation({
    mutationKey: queryKey,
    mutationFn: saveViewMutationFn,
    onMutate: async () => await queryClient.cancelQueries({ queryKey }),
    onError: (error) => toastAndLogError("save", error),
    onSuccess: async (view: View) => {
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.map((v) => (v.id === view.id ? view : v)),
      );
      viewProps.resetState(view);
    },
  });

  const saveView = useEvent(async () => {
    if (!selectedView || selectedView.builtin) {
      return;
    }
    saveViewMutation({
      viewId: selectedView.id,
      viewOptionsToSave: viewProps.viewOptionsToSave,
      viewDataToSave: {
        search: normalizeSearch(viewProps.search),
      },
    });
  });

  const renameViewMutationFn = async ({
    viewId,
    name,
  }: {
    viewId: string | null;
    name: string;
  }) => {
    const payload = { ...makeRequiredPatchParams(viewParams), name };
    if (!viewId) {
      throw new Error("Cannot rename default view");
    }
    const sessionToken = await getOrRefreshToken();
    if (views?.some((v) => v.name === name)) {
      throw new Error(makeDuplicateViewError(name));
    }
    const resp = await apiPatch({
      url: `${apiUrl}/v1/view/${viewId}`,
      sessionToken,
      payload,
      alreadySerialized: false,
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    if (resp.headers.has(BT_FOUND_EXISTING_HEADER)) {
      throw new Error(makeDuplicateViewError(name));
    }
    return await resp.json();
  };

  const {
    mutate: renameView,
    variables: renameViewVariables,
    isPending: isRenamePending,
  } = useMutation({
    mutationFn: renameViewMutationFn,
    onMutate: async ({
      viewId,
      name,
    }: {
      viewId: string | null;
      name: string;
    }) => {
      await queryClient.cancelQueries({ queryKey });
      const previousViews = queryClient.getQueryData<View[]>(queryKey);
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.map((v) => (v.id === viewId ? { ...v, name } : v)),
      );
      return previousViews;
    },
    onError: (error, _, previousViews) => {
      toastAndLogError("rename", error);
      queryClient.setQueryData<View[]>(queryKey, previousViews);
    },
    onSuccess: async (view: View) => {
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.map((v) => (v.id === view.id ? view : v)),
      );
      setViewName(view.name ?? null);
    },
  });

  if (!areViewsEnabled) {
    return null;
  }

  return (
    <>
      <ViewDropdown
        views={views}
        selectedView={selectedView}
        viewNameOverride={
          isCreatePending
            ? optimisticCreatedName
            : isRenamePending
              ? renameViewVariables?.name
              : undefined
        }
        loadView={loadView}
        isLoadingViews={isPending}
        setCreateViewDialogOpen={setIsCreateViewDialogOpen}
        setRenameViewDialogOpen={setIsRenameViewDialogOpen}
        setDeleteViewDialogOpen={setIsDeleteViewDialogOpen}
        pageIdentifier={pageIdentifier}
        saveView={saveView}
        isDirty={viewProps.isDirty}
        resetView={() => resetState(selectedView, { reset: true })}
      />
      <CreateViewDialog
        isCreateViewDialogOpen={isCreateViewDialogOpen}
        setIsCreateDatasetDialogOpen={setIsCreateViewDialogOpen}
        createView={createView}
      />
      {selectedView && (
        <>
          <RenameViewDialog
            isRenameViewDialogOpen={isRenameViewDialogOpen}
            setIsRenameViewDialogOpen={setIsRenameViewDialogOpen}
            viewId={selectedView.id}
            viewName={selectedView.name}
            renameView={renameView}
          />
          <DeleteViewDialog
            isDeleteViewDialogOpen={isDeleteViewDialogOpen}
            setIsDeleteViewDialogOpen={setIsDeleteViewDialogOpen}
            viewName={selectedView.name}
            deleteView={() => deleteView(selectedView.id)}
          />
        </>
      )}
    </>
  );
}
