import { useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "#/ui/dialog";
import { PlainInput } from "#/ui/plain-input";
import { Button } from "#/ui/button";

export function RenameViewDialog({
  isRenameViewDialogOpen,
  setIsRenameViewDialogOpen,
  viewId,
  viewName,
  renameView,
}: {
  isRenameViewDialogOpen: boolean;
  setIsRenameViewDialogOpen: (open: boolean) => void;
  viewId: string | null;
  viewName: string | undefined;
  renameView: ({
    viewId,
    name,
  }: {
    viewId: string | null;
    name: string;
  }) => void;
}) {
  const inputRef = useRef<HTMLInputElement>(null);
  return (
    <Dialog
      open={isRenameViewDialogOpen}
      onOpenChange={() => setIsRenameViewDialogOpen(false)}
    >
      <DialogContent className="sm:max-w-[600px]">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            const inputValue = inputRef.current?.value;
            if (inputValue && viewId) {
              renameView({ viewId, name: inputValue });
            }
            setIsRenameViewDialogOpen(false);
          }}
        >
          <DialogHeader>
            <DialogTitle>Rename view</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-4 items-center gap-4 py-4">
            <label htmlFor="view_name" className="text-right">
              Name
            </label>
            <PlainInput
              id="view_name"
              className="col-span-3"
              ref={inputRef}
              autoComplete="off"
              defaultValue={viewName}
            />
          </div>
          <DialogFooter>
            <Button type="submit">Save</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
