import { ConfirmationDialog } from "#/ui/dialogs/confirmation";

export function DeleteViewDialog({
  isDeleteViewDialogOpen,
  setIsDeleteViewDialogOpen,
  viewName,
  deleteView,
}: {
  isDeleteViewDialogOpen: boolean;
  setIsDeleteViewDialogOpen: (open: boolean) => void;
  viewName: string | undefined;
  deleteView: () => void;
}) {
  return (
    <ConfirmationDialog
      open={isDeleteViewDialogOpen}
      onOpenChange={setIsDeleteViewDialogOpen}
      title={"Delete view"}
      description={`Are you sure you want to delete view "${viewName}"?`}
      confirmText="Delete"
      onConfirm={() => {
        setIsDeleteViewDialogOpen(false);
        deleteView();
      }}
    />
  );
}
