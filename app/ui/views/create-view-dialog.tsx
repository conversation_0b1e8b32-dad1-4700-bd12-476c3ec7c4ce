import { OneLineTextPrompt } from "#/ui/dialogs/one-line-text-prompt";

export function CreateViewDialog({
  isCreateViewDialogOpen,
  setIsCreateDatasetDialogOpen,
  createView,
}: {
  isCreateViewDialogOpen: boolean;
  setIsCreateDatasetDialogOpen: (open: boolean) => void;
  createView: (name: string) => void;
}) {
  return (
    <OneLineTextPrompt
      title="Create view"
      description="Save the current view configurations (filters, sorts, and columns) for quick access"
      fieldName="Name"
      validate={(name) => {
        if (name.length === 0) {
          return "View name cannot be empty";
        }
        return null;
      }}
      onSubmit={(name) => {
        createView(name);
        setIsCreateDatasetDialogOpen(false);
      }}
      onOpenChange={() => setIsCreateDatasetDialogOpen(false)}
      open={isCreateViewDialogOpen}
      submitLabel="Create"
      placeholder="Enter view name"
    />
  );
}
