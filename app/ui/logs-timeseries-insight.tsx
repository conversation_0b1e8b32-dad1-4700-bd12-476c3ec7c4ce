"use client";

import { MonitorCard } from "#/app/app/[org]/monitor/card/monitor-card";
import { useMemo, useState } from "react";
import { type Clause } from "#/utils/search/search";
import { type DataObjectSearch } from "#/utils/btapi/btapi";
import { type MonitorCardConfig } from "#/app/app/[org]/monitor/card/monitor-card-config.types";
import { type CardState } from "#/app/app/[org]/monitor/monitor-cards";
import { type TimeRangeFilter } from "#/utils/view/use-view";
import { TIME_RANGE_TO_MILLISECONDS } from "#/app/app/[org]/monitor/time-controls/time-range";
import { parseDateString } from "./logs-time-range";

interface LogsTimeseriesInsightProps {
  projectId: string;
  filters: DataObjectSearch["filters"] | undefined;
  title?: string;
  onBucketClick: (bucket: Clause<"filter">) => void;
  timeRangeFilter?: TimeRangeFilter;
  onTimeRangeChange?: (timeRange: TimeRangeFilter) => void;
}

const EMPTY_EXPERIMENT_IDS: string[] = [];

export function LogsTimeseriesInsight({
  projectId,
  filters,
  onBucketClick,
  timeRangeFilter,
  onTimeRangeChange,
}: LogsTimeseriesInsightProps) {
  const [_cardState, setCardState] = useState<CardState>({
    isLoading: false,
    hasData: false,
    hasError: false,
  });

  const cardConfig = useMemo(
    (): MonitorCardConfig => ({
      name: "logs-timeseries",
      idName: "logs",
      vizType: "bars",
      measures: [
        {
          alias: "spans",
          displayName: "Spans",
          btql: "count(1)",
        },
      ],
      unitType: "count",
    }),
    [],
  );

  const chartTimeFrame = useMemo(() => {
    const now = Date.now();

    if (!timeRangeFilter) {
      // Default to 7 days if no filter provided
      return {
        start: now - 7 * 24 * 60 * 60 * 1000,
        end: now,
      };
    }

    if (typeof timeRangeFilter === "string") {
      // Handle predefined time ranges like "3d", "7d", etc.
      const duration =
        TIME_RANGE_TO_MILLISECONDS[
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          timeRangeFilter as keyof typeof TIME_RANGE_TO_MILLISECONDS
        ];
      if (duration) {
        return {
          start: now - duration,
          end: now,
        };
      }
      // Special case for "all"
      if (timeRangeFilter === "all") {
        return {
          start: now - 365 * 24 * 60 * 60 * 1000, // 1 year for "all"
          end: now,
        };
      }
    } else {
      // Handle custom date range
      const fromDate = parseDateString(timeRangeFilter.from);
      const toDate = parseDateString(timeRangeFilter.to);
      return {
        start: fromDate.getTime(),
        end: toDate.getTime() + 24 * 60 * 60 * 1000 - 1, // End of day
      };
    }

    // Fallback to 7 days
    return {
      start: now - 7 * 24 * 60 * 60 * 1000,
      end: now,
    };
  }, [timeRangeFilter]);

  const handleBrush = (range: [number, number] | null) => {
    if (!range || !onTimeRangeChange) {
      return;
    }

    const [startTimestamp, endTimestamp] = range;

    const startDate = new Date(startTimestamp);
    const endDate = new Date(endTimestamp);

    const fromString = startDate.toISOString().split("T")[0];
    const toString = endDate.toISOString().split("T")[0];

    const timeRangeFilter: TimeRangeFilter = {
      from: fromString,
      to: toString,
    };

    onTimeRangeChange(timeRangeFilter);
  };

  const projectIds = useMemo(() => (projectId ? [projectId] : []), [projectId]);

  // Select appropriate time bucket based on the time range
  const timeBucket = useMemo(() => {
    const duration = chartTimeFrame.end - chartTimeFrame.start;
    if (duration <= 2 * 60 * 60 * 1000) {
      return "minute" as const;
    } else if (duration <= 7 * 24 * 60 * 60 * 1000) {
      return "hour" as const;
    } else {
      return "day" as const;
    }
  }, [chartTimeFrame]);

  return (
    <MonitorCard
      cardConfig={cardConfig}
      setCardState={setCardState}
      chartTimeFrame={chartTimeFrame}
      timeBucket={timeBucket}
      from="project_logs"
      projectIds={projectIds}
      experimentIds={EMPTY_EXPERIMENT_IDS}
      onBrush={handleBrush}
      filters={filters?.btql}
      hasBottomLegend={false}
      hasYAxis={false}
    />
  );
}
