import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  commonFilterParamsSchema,
  paginationParamsSchema,
  splitPaginationParams,
  getObjects,
} from "../_object_crud_util";
import { isAllowedSysadmin } from "#/utils/derive-error-context";
import { projectAutomationSchema } from "@braintrust/core/typespecs";

const paramsSchema = paginationParamsSchema.merge(
  commonFilterParamsSchema.pick({
    name: true,
    project_name: true,
    project_id: true,
    org_name: true,
    org_id: true,
    id: true,
  }),
);

const outputSchema = projectAutomationSchema.array();

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async (params, authLookup) => {
      const isSysadmin = await isAllowedSysadmin(authLookup);
      return getObjects({
        authLookup,
        priorObjectTables: ["project_automation"],
        permissionInfo: {
          aclObjectType: "project",
          aclPermission: "read",
        },
        ...splitPaginationParams(params),
        fullResultsSize: undefined,
        isSysadminAutomationLookup: isSysadmin,
      });
    },
    {
      paramsSchema,
      postprocessOutput: (rows) => {
        // strip the generated columns from the table
        return outputSchema.parse(rows);
      },
      outputSchema,
    },
  );
}
