---
title: "Brainstore is now on by default"
description: "Brainstore is now the default in both our UI and API. Learn what's changing and coming next."
author: "<PERSON><PERSON><PERSON> Goyal"
date: "31 March 2025"
---

import { Twitter } from "#/ui/docs/twitter";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "#/ui/blog/blog-author";
import Image from 'next/image';

# Brainstore is now the default

<BlogAuthor author="Ankur Goyal" date="31 March 2025" />

Earlier this month, we publicly announced [Brainstore](./brainstore), a database built for high-scale AI workloads. It's 80x faster on real-world benchmarks
with median query times under one second, even across terabytes of data. We've been running Brainstore in production for several months now, allowing
customers to opt-in via a feature flag in the UI and via the `use_brainstore` parameter in the API.

At this point, almost every customer with a significant volume of logs has moved to Brainstore. The response has been overwhelmingly positive:

> I flipped it on and then loaded the UI — ~5s load time difference. Individual logs also load a lot faster.

> Running with Brainstore now. Wow that was ... instant.

> Brainstore is awesome, well done team!

Based on this response, we're excited to share that Brainstore is now the default
in both our UI and API. Let's walk through what that means and a bit about what's coming next.

## Changes to expect

Braintrust's API exposes a special [`/btql` endpoint](/docs/reference/btql) which is the work-horse behind all of the REST API endpoints as well as the
UI. This endpoint now defaults to using Brainstore. If you'd like to fall back to querying via Postgres (the previous default), you
can explicitly set `use_brainstore=false` in your request.

```bash
curl -X POST https://api.braintrust.dev/btql \
  -H "Authorization: Bearer <YOUR_API_KEY>" \
  -H "Content-Type: application/json" \
  -d '{"use_brainstore": false, "query": "from: experiment(\"my_experiment\") | select: *"}'
```

The Brainstore feature flag in the UI will now also default to on. If you'd like to disable Brainstore, you can do so
by toggling the feature flag off in the UI.

![Brainstore switch](/blog/meta/brainstore/toggle-on.png)

### A breaking API change

Prior to Brainstore, BTQL queries against logs and experiments would return every span from traces that matched the search.
Although this is a powerful feature, our users have consistently found it to be confusing, so Brainstore allows you to explicitly
specify whether you'd like to see spans or traces.

```sql #btql
-- This will return only the spans that match the filter
select: *
from: project_logs('my-project-id') spans
filter: metadata.user_id = '123'
limit: 10
```

```sql #btql
-- This will return every span from traces that match the filter
select: *
from: project_logs('my-project-id') traces
filter: metadata.user_id = '123'
limit: 10
```

In Brainstore, the default is `spans`, not `traces`, but since this is a breaking change, for the next 30 days, if you do not
specify `use_brainstore` explicitly, we will fall back to the old behavior of returning traces by default.

## Self-hosted users

If you are self-hosting Braintrust, then these changes to the defaults have no effect on you. Whenever you're ready, you can
enable Brainstore as the default for your users by setting the `BRAINSTORE_DEFAULT` environment variable to `true`. Your support
contact at Braintrust can help you navigate this when you're ready.

Once you set `BRAINSTORE_DEFAULT` to `true`, your team will experience exactly the same behavior as outlined above.

## Looking ahead

Rolling out a large infrastructure change like Brainstore is not an overnight task, but we're excited about the progress we've
made and what's ahead. Here's what you can expect:

**Users of our hosted service**

* As early as April 28, 2025, we will make `spans` the default in the API for all users.

**Self-hosted users**

* As of today, no new self-hosted deployments should install ClickHouse. We will still support customers running ClickHouse for the
  foreseeable future, but we expect you to enable Brainstore as soon as feasible. Note that Brainstore is both cheaper and faster
  in the context of Braintrust's product.
* Sometime this year, we will make Brainstore a required component of the Braintrust stack. If you haven't
  planned enabling it yet, please reach out to your Braintrust support contact so we can help you plan the transition.

---

We're grateful to all of our customers who have gave us early feedback to help us develop the best-in-class database for AI workloads. If you're interested in getting your team on Braintrust, please [reach out](mailto:<EMAIL>), and if you want to help build the future of LLM observability software, [we're hiring](https://www.braintrust.dev/careers?ashby_jid=8b9cfa26-627f-442c-a358-783b0e4ef930).
