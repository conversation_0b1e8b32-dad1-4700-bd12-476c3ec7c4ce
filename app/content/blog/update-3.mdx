---
title: Weekly update 10/23/23
description: "Auto input variables in the playground, duration metrics, performance optimizations, partner releases."
author: "<PERSON><PERSON><PERSON>yal"
date: "23 October 2023"
---

import <PERSON><PERSON><PERSON> from "#/ui/script";
import SignUpLink from "#/ui/docs/signup";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "#/app/(landing)/cta-button";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "#/ui/blog/blog-author";

# Braintrust Weekly Update

<BlogAuthor author="An<PERSON><PERSON> Goyal" date="23 October 2023" />

It’s been another busy week for us at Braintrust. It’s been exciting to see many users start to ship their LLM apps into production and evaluate them with Braintrust. Here’s some of the new features we shipped this week:

#### Prompt playground variable improvements

<figure>![Dataset UI](/blog/img/prompt-variables.gif)</figure>

Managing and writing evaluation test cases is tedious because they are usually in a JSON format. This means manually editing JSON strings all the time. This week we improved the playground so you can quickly create input variables and edit them for your prompts and test cases without needing to edit raw JSON. Everything is visualized and accessible through our UI. The input names also autofill when you are typing out the prompt.

#### Time duration summary metrics for experiments

<figure>![Dataset UI](/blog/img/TimeDurationExperiments.png)</figure>

When iterating on LLM apps there is a tradeoff between time to execute and accuracy. For example, running more context retrievals can take longer but can improve the quality of the answer for a RAG app. We added in time duration metrics to Braintrust experiments so that you can compare how long each test case changes between experiments and compare it to overall accuracy.

#### [Release notes](https://www.braintrustdata.com/docs/release-notes):

- Improved prompt playground variable handling and visualization
- Added time duration statistics per row to experiment summaries
- Multiple performance optimizations and bug fixes

#### Fun links and mentions:

- [Madrona wrote an excellent report on the GenAI development stack including Braintrust](https://twitter.com/MadronaVentures/status/1716464307678933394)
- Ironclad updated their prompt engineering tool: [Rivet launched plugins](https://twitter.com/rivet_ts/status/1714084276919308615) including a Braintrust plugin
- Ankur spoke on the [Bits and Bots podcast](https://twitter.com/DKossnick/status/1716435066337788169) with Parcha about AI agents in production
- We received our [first community contribution to our autoevals library](https://twitter.com/ankrgyl/status/1714001422138917219) (our model graded evals library). Huge thank you to Edward Atkins @ecatkins for authoring the PR.

Braintrust is the enterprise-grade stack for building AI products. From evaluations, to prompt playground, to data management, we take uncertainty and tedium out of incorporating AI into your business.

<SignUpLink>Sign up</SignUpLink> now, or check out our [pricing page](/pricing) for
more details.

<div className="mt-12">
  <CtaButton className="w-full sm:w-auto sm:h-12" cta="Get started for free" />
</div>
