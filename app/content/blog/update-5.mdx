---
title: Weekly update 11/06/23
description: "Perplexity models support, new OpenAI models, reworked diff selector in experiment view."
author: "<PERSON> Song"
date: "06 November 2023"
---

import <PERSON><PERSON><PERSON> from "#/ui/script";
import SignUpLink from "#/ui/docs/signup";
import <PERSON><PERSON><PERSON><PERSON>on from "#/app/(landing)/cta-button";
import { Blog<PERSON>uthor } from "#/ui/blog/blog-author";

# Braintrust Weekly Update

<BlogAuthor author="David Song" date="06 November 2023" />

We’ve been heads down at Braintrust working on an exciting new feature. We’ll announce it soon! In the meantime, we’ve shipped some user experience improvements and fixes this week:

#### New OpenAI and Open Source models in playground

<figure>
  ![Dataset UI](/docs/release-notes/ReleaseNotes-2023-11-playground-models.gif)
</figure>

We wanted to try out open source models like Mistral, Codellama, Llama2, etc. and compare them to OpenAI's new models. So, we added them to the playground and also used Perplexity's API for the OS models. They are so fast!

#### [Release notes](https://www.braintrustdata.com/docs/release-notes):

- Improved selectors for diffing and comparison modes on experiment view
- Added support for new OpenAI models (GPT4 preview, 3.5turbo-1106) in playground
- Added support for open source models (Mistral, Codellama, Llama2,etc.) in the playground using [Perplexity's APIs](https://docs.perplexity.ai/docs)

#### Fun links and mentions:

- [OpenAI's reproducible outputs makes evals and testing 10x better](https://twitter.com/braintrustdata/status/1721987922520580331)
- [Have fun building AI apps with your team](https://twitter.com/braintrustdata/status/1719791476954284489)

Braintrust is the enterprise-grade stack for building AI products. From evaluations, to prompt playground, to data management, we take uncertainty and tedium out of incorporating AI into your business.

<SignUpLink>Sign up</SignUpLink> now, or check out our [pricing page](/pricing) for
more details.

<div className="mt-12">
  <CtaButton className="w-full sm:w-auto sm:h-12" cta="Get started for free" />
</div>
