---
title: Weekly update 10/09/23
description: "Performance improvements, fine tuning tutorial, Alpaca Evals, autocomplete in the playground."
author: "<PERSON><PERSON><PERSON> Goyal"
date: "09 October 2023"
---

import <PERSON><PERSON><PERSON> from "#/ui/script";
import SignUpLink from "#/ui/docs/signup";
import C<PERSON>Button from "#/app/(landing)/cta-button";
import { Blog<PERSON><PERSON><PERSON> } from "#/ui/blog/blog-author";

# Braintrust Weekly Update

<BlogAuthor author="An<PERSON>r Goyal" date="09 October 2023" />

It’s been a busy week for us at Braintrust. Here’s some of the new features we shipped this week:

- <span className="font-bold">
    All experiment loading HTTP requests are 100-200ms faster
  </span>
- <span className="font-bold">We released a new tutorial:</span> [finetune GPT3.5
  to write SQL queries](https://colab.research.google.com/drive/10KIXBHjZ0VUc-zN79_cuVeKy9ZiUQy4M?usp=sharing)

<figure>![Dataset UI](/blog/img/sql-tutorial.png)</figure>

You can quickly finetune GPT3.5 to generate SQL queries using OpenAI and then evaluate how the fine tuned model compares to the base model using Braintrust. Check out the [Jupyter Notebook example here](https://colab.research.google.com/drive/10KIXBHjZ0VUc-zN79_cuVeKy9ZiUQy4M?usp=sharing) to get started.

##### We evaluated the Alpaca evals leaderboard in Braintrust

<figure>![Dataset UI](/blog/img/alpaca-evals.png)</figure>

The Alpaca evals use Claude and GPT4 to rank how different LLMs perform on a variety of tasks. You can see the aggregated rankings and also dig into individual models and better understand their strengths and weaknesses. Check out the [Alpaca Evals braintrust project](https://www.braintrustdata.com/app/braintrustdata.com/p/Alpaca-Evals) on Braintrust to dig in further—no login required.

##### We improved Datasets. See when they were last edited and the version number from the UI.

<figure>![Dataset UI](/blog/img/dataset-improvements.png)</figure>

Easily see when a dataset was last changed from the UI by hovering over the ID. We also provide example code so you can quickly use the current dataset version in your project. Learn more on our [datasets guide](https://www.braintrustdata.com/docs/guides/datasets).

##### Release notes

- All experiment loading HTTP requests are 100-200ms faster
- The prompt playground now supports autocomplete
- Dataset versions are now displayed on the datasets page
- Projects in the summary page are now sorted alphabetically
- [Long text fields in logged data can be expanded into scrollable blocks](https://share.descript.com/view/wxHgbLZesmw)

Braintrust is the enterprise-grade stack for building AI products. From evaluations, to prompt playground, to data management, we take uncertainty and tedium out of incorporating AI into your business.

<SignUpLink>Sign up</SignUpLink> now, or check out our [pricing page](/pricing) for
more details.

<div className="mt-12">
  <CtaButton className="w-full sm:w-auto sm:h-12" cta="Get started for free" />
</div>
