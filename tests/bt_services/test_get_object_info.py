import braintrust
from braintrust_local.constants import ANON_USER_ID

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class GetObjectInfoTest(BraintrustAppTestBase):
    def test_get_object_info_permisions(self):
        def request_org_object_info(headers):
            return self.run_request(
                "post",
                f"{LOCAL_API_URL}/api/self/get_object_info",
                json=dict(object_type="organization", object_ids=[self.org_id]),
                headers=headers,
            ).json()

        self_headers = dict(Authorization=f"Bearer {self.org_api_key}")
        resp = request_org_object_info(self_headers)
        self.assertEqual(len(resp), 1)
        self.assertEqual(
            {k: v for k, v in resp[0].items() if k != "permissions"},
            dict(
                object_id=self.org_id,
                object_name=self.org_name,
                parent_cols={},
            ),
        )
        self.assertGreater(len(resp[0]["permissions"]), 0)

        # Non-org owner in the same org should be able to get object info with
        # no permissions.
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        user_headers = dict(Authorization=f"Bearer {user_api_key}")
        resp = request_org_object_info(user_headers)
        self.assertEqual(
            resp, [dict(object_id=self.org_id, object_name=self.org_name, parent_cols={}, permissions=[])]
        )

        # Member of a different org should not be able to get object info.
        other_org_id, _ = self.createOrg()
        other_user_id, _, other_user_api_key = self.createUserInOrg(other_org_id)
        other_user_headers = dict(Authorization=f"Bearer {other_user_api_key}")
        resp = request_org_object_info(other_user_headers)
        self.assertEqual(resp, [])

        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="test0")).json()
        experiment = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json=dict(project_id=project["id"], name="test0")
        ).json()

        def request_experiment_object_info(headers):
            return self.run_request(
                "post",
                f"{LOCAL_API_URL}/api/self/get_object_info",
                json=dict(object_type="experiment", object_ids=[experiment["id"]]),
                headers=headers,
            ).json()

        # Before we make the experiment public, the non-owner should have empty,
        # and the non-org member should not be able to get any info.
        base_experiment_object_info = dict(
            object_id=experiment["id"],
            object_name=experiment["name"],
            parent_cols=dict(
                project=dict(id=project["id"], name=project["name"]),
                organization=dict(id=self.org_id, name=self.org_name),
            ),
            permissions=[],
        )

        resp = request_experiment_object_info(user_headers)
        self.assertEqual(resp, [base_experiment_object_info])
        resp = request_experiment_object_info(other_user_headers)
        self.assertEqual(resp, [])

        # After public, both of them should have access with a read permission.
        self.grant_acl(
            dict(object_type="experiment", object_id=experiment["id"], user_id=ANON_USER_ID, permission="read"),
        )
        modified_experiment_object_info = {**base_experiment_object_info, "permissions": ["read"]}
        resp = request_experiment_object_info(user_headers)
        self.assertEqual(resp, [modified_experiment_object_info])
        resp = request_experiment_object_info(other_user_headers)
        self.assertEqual(resp, [modified_experiment_object_info])

    def test_get_object_info_empty_project_name(self):
        experiment = braintrust.init("")
        self.assertEqual(experiment.project.name, "")
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/get_object_info",
            json=dict(object_type="experiment", object_ids=[experiment.id], accept_arbitrary_acl_object_types=True),
        ).json()[0]
        self.assertEqual(resp["object_id"], experiment.id)
        self.assertEqual(resp["object_name"], experiment.name)
        self.assertEqual(
            resp["parent_cols"],
            {
                "project": dict(id=experiment.project_id, name=""),
                "org_project": dict(id=self.org_id, name=self.org_name),
                "organization": dict(id=self.org_id, name=self.org_name),
            },
        )
