import {
  login,
  loadPrompt,
  initDataset,
  initLogger,
  traced,
  currentLogger,
  init,
  startSpan,
  INTERNAL_BTQL_LIMIT,
} from "braintrust";
import { expect, test, describe } from "vitest";
import { LOCAL_APP_URL } from "./setup";

function makeEnvironmentUrl(...args: string[]) {
  let ret = "environment";
  for (const arg of args) {
    ret += `/${arg}`;
  }
  return ret;
}

function makeEnvironmentObjectUrl(...args: string[]) {
  let ret = "environment-object";
  for (const arg of args) {
    ret += `/${arg}`;
  }
  return ret;
}

test("loadPromptCached", async ({ orgApiKey }) => {
  const state = await login({
    appUrl: LOCAL_APP_URL,
    apiKey: orgApiKey,
    forceLogin: true,
  });
  const project = await state
    .apiConn()
    .post("v1/project", {
      name: "load-prompt-cached",
    })
    .then((res) => res.json());

  await state.apiConn().post("v1/prompt", {
    project_id: project.id,
    prompt_data: {
      prompt: {
        type: "chat",
        messages: [
          {
            role: "system",
            content: "You are a calculator. Return results in JSON",
          },
          { role: "user", content: "{{formula}}" },
        ],
      },
      options: {
        params: {
          response_format: { type: "json_object" },
        },
      },
    },
    name: "calculator",
    slug: "calculator",
  });

  const original = await loadPrompt({
    projectName: project.name,
    slug: "calculator",
  });
  state.apiConn().setFetch(async () => {
    throw new Error("fail");
  });
  const cached = await loadPrompt({
    projectName: project.name,
    slug: "calculator",
  });
  expect(cached).toBe(original);
});

test("loadPromptById", async ({ orgApiKey }) => {
  const state = await login({
    appUrl: LOCAL_APP_URL,
    apiKey: orgApiKey,
    forceLogin: true,
  });
  const project = await state
    .apiConn()
    .post("v1/project", {
      name: "load-prompt-by-id",
    })
    .then((res) => res.json());

  const promptResponse = await state
    .apiConn()
    .post("v1/prompt", {
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            {
              role: "system",
              content: "You are a helpful assistant.",
            },
          ],
        },
        options: {
          model: "gpt-3.5-turbo",
        },
      },
      name: "Test Prompt for ID Loading",
      slug: "prompt-by-id-test",
    })
    .then((res) => res.json());

  const promptId = promptResponse.id;

  // Load prompt by ID (without project or slug)
  const promptById = await loadPrompt({ id: promptId, state });
  expect(promptById.id).toBe(promptId);
  expect(promptById.name).toBe("Test Prompt for ID Loading");
  expect(promptById.build({})).toMatchObject({
    messages: [
      {
        role: "system",
        content: "You are a helpful assistant.",
      },
    ],
    model: "gpt-3.5-turbo",
  });

  // Load prompt by slug for comparison
  const promptBySlug = await loadPrompt({
    projectName: project.name,
    slug: "prompt-by-id-test",
    state,
  });
  expect(promptBySlug.id).toBe(promptId);
  expect(JSON.stringify(promptBySlug.build({}))).toBe(
    JSON.stringify(promptById.build({})),
  );

  // Test that ID takes precedence over other parameters
  const promptWithBoth = await loadPrompt({
    id: promptId,
    projectName: "wrong-project",
    slug: "wrong-slug",
    state,
  });
  expect(promptWithBoth.id).toBe(promptId);
  expect(promptWithBoth.name).toBe("Test Prompt for ID Loading");

  // Test loading non-existent prompt by ID
  await expect(
    loadPrompt({ id: "non-existent-id-12345", state }),
  ).rejects.toThrow(/not found/);
});

test("loadPrompt environment parameter validation", async ({ orgApiKey }) => {
  const state = await login({
    appUrl: LOCAL_APP_URL,
    apiKey: orgApiKey,
    forceLogin: true,
  });
  const project = await state
    .apiConn()
    .post("v1/project", {
      name: "env-param-test",
    })
    .then((res) => res.json());

  const promptResponse = await state
    .apiConn()
    .post("v1/prompt", {
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [{ role: "user", content: "Environment parameter test" }],
        },
        options: {
          model: "gpt-3.5-turbo",
        },
      },
      name: "Environment Parameter Test",
      slug: "env-param-test",
    })
    .then((res) => res.json());

  // Test that environment and version cannot be used together
  await expect(
    loadPrompt({
      projectName: project.name,
      slug: "env-param-test",
      version: promptResponse._xact_id,
      environment: "dev",
      state,
    }),
  ).rejects.toThrow(/Cannot specify both 'version' and 'environment'/);

  // Test happy path: create environment association and load prompt with environment
  const envResponse = await state
    .apiConn()
    .post(makeEnvironmentUrl(), {
      name: "Test Environment",
      slug: "test-env",
      description: "Test Environment for testing",
    })
    .then((res) => res.json());

  // Update the prompt to create a new version
  const updatedPromptResponse = await state
    .apiConn()
    .post("v1/prompt", {
      project_id: project.id,
      slug: "env-param-test", // Same slug to update existing prompt
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [{ role: "user", content: "Environment-specific content" }],
        },
        options: {
          model: "gpt-4",
        },
      },
      name: "Environment Parameter Test", // Same name
    })
    .then((res) => res.json());

  // Associate the new version with the environment
  await state
    .apiConn()
    .post(makeEnvironmentObjectUrl("prompt", promptResponse.id), {
      environment_slug: envResponse.slug,
      object_version: updatedPromptResponse._xact_id,
    });

  // Load prompt with environment parameter - should get the environment-specific version
  const envPrompt = await loadPrompt({
    projectName: project.name,
    slug: "env-param-test",
    environment: envResponse.slug,
    state,
  });

  expect(envPrompt.version).toBe(updatedPromptResponse._xact_id);
  expect(envPrompt.build({})).toMatchObject({
    messages: [{ role: "user", content: "Environment-specific content" }],
    model: "gpt-4",
  });

  // Test that environment parameter is passed through for nonexistent environment (will fail due to no association, but validates param is sent)
  await expect(
    loadPrompt({
      projectName: project.name,
      slug: "env-param-test",
      environment: "nonexistent-env",
      state,
    }),
  ).rejects.toThrow(/not found/i);
});

test("loadPromptByIdCached", async ({ orgApiKey }) => {
  const state = await login({
    appUrl: LOCAL_APP_URL,
    apiKey: orgApiKey,
    forceLogin: true,
  });
  const project = await state
    .apiConn()
    .post("v1/project", {
      name: "load-prompt-by-id-cached",
    })
    .then((res) => res.json());

  const promptResponse = await state
    .apiConn()
    .post("v1/prompt", {
      project_id: project.id,
      prompt_data: {
        prompt: {
          type: "chat",
          messages: [
            {
              role: "system",
              content: "You are a math tutor.",
            },
            { role: "user", content: "What is {{num1}} + {{num2}}?" },
          ],
        },
        options: {
          model: "gpt-3.5-turbo",
        },
      },
      name: "math-tutor",
      slug: "math-tutor",
    })
    .then((res) => res.json());

  const promptId = promptResponse.id;

  // Load prompt by ID to populate cache
  const original = await loadPrompt({ id: promptId, state });
  expect(original.id).toBe(promptId);
  expect(original.name).toBe("math-tutor");

  // Mock API failure
  state.apiConn().setFetch(async () => {
    throw new Error("Server unavailable");
  });

  // Should load from cache when server fails
  const cached = await loadPrompt({ id: promptId, state });
  expect(cached).toBe(original);

  // Test that non-cached ID fails appropriately
  await expect(
    loadPrompt({ id: "non-cached-id-12345", state }),
  ).rejects.toThrow(/not found.*cache/);
});

test("Prompt.build", async ({ orgApiKey }) => {
  const state = await login({
    appUrl: LOCAL_APP_URL,
    apiKey: orgApiKey,
    forceLogin: true,
  });

  const project = await (
    await state.apiConn().post("v1/project", {
      name: "dataset_in_eval",
    })
  ).json();

  const slug = "image-url-6980";

  await state.apiConn().post("v1/prompt", {
    project_id: project.id,
    prompt_data: {
      prompt: {
        type: "chat",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `{{context}}

                         {{query}}`.trim(),
              },
              {
                type: "image_url",
                image_url: { url: "{{image_url}}" },
              },
            ],
          },
        ],
      },
      options: { params: { model: "gpt-4o" } },
    },
    name: "Image URL",
    slug,
  });

  const prompt = await loadPrompt({
    projectName: project.name,
    slug,
  });

  const result = prompt.build({
    context: `what is this
 an image of`.trim(),
    query: "foo",
    image_url:
      "https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg",
  });

  expect(result).toEqual({
    messages: [
      {
        content: [
          {
            text: "what is this\n an image of\n\n                         foo",
            type: "text",
          },
          {
            image_url: {
              url: "https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg",
            },
            type: "image_url",
          },
        ],
        role: "user",
      },
    ],
    model: "gpt-4o",
    span_info: {
      metadata: {
        prompt: {
          id: expect.any(String),
          project_id: project.id,
          variables: {
            context: `what is this
 an image of`.trim(),
            image_url:
              "https://gratisography.com/wp-content/uploads/2024/11/gratisography-augmented-reality-800x525.jpg",
            query: "foo",
          },
          version: expect.any(String),
        },
      },
    },
  });
});

describe("initDataset", async () => {
  test("filters via btql", async ({ orgApiKey }) => {
    await login({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      forceLogin: true,
    });
    const dataset = initDataset({
      project: "basic_dataset_ts",
    });

    for (let i = 0; i < 10; i++) {
      dataset.insert({
        input: i * 20,
        expected: { result: i + 1, error: null },
        metadata: {
          foo: i % 2,
          bar: "baz",
        },
      });
    }

    await dataset.flush();

    const filteredDataset = initDataset({
      project: "basic_dataset_ts",
      dataset: await dataset.name,
      _internal_btql: {
        filter: {
          op: "ilike",
          left: {
            op: "ident",
            name: ["input"],
          },
          right: {
            op: "literal",
            value: "%1%",
          },
        },
      },
    });

    const rows = [];
    for await (const data of filteredDataset) {
      expect(`${data.input}`.includes("1")).toBeTruthy();
      rows.push(data);
    }

    expect(rows.length).toBe(5);
  });

  test("pages through all rows with btql", async ({ orgApiKey }) => {
    await login({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      forceLogin: true,
    });
    const dataset = initDataset({
      project: "basic_dataset_ts",
    });

    const expectedRowCount = INTERNAL_BTQL_LIMIT + 10;
    for (let i = 0; i < expectedRowCount; i++) {
      dataset.insert({
        input: `${i}`,
        expected: null,
      });
    }

    await dataset.flush();

    const filteredDataset = initDataset({
      project: "basic_dataset_ts",
      dataset: await dataset.name,
      _internal_btql: {},
    });

    let rowCount = 0;
    for await (const _ of filteredDataset) {
      rowCount++;
    }
    expect(rowCount).toBe(expectedRowCount);
  });
});

describe("Logger asyncFlush behavior", () => {
  test("with default asyncFlush=true, log returns a string ID", async ({
    orgApiKey,
  }) => {
    const logger = initLogger({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      projectName: "p",
    });

    const result = logger.log({ input: "foo", output: "bar" });
    expect(result).toBeTypeOf("string");
  });

  test("with asyncFlush=false, log returns a Promise", async ({
    orgApiKey,
  }) => {
    const logger = initLogger({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      projectName: "p",
      asyncFlush: false,
    });

    const result = logger.log({ input: "foo", output: "bar" });
    expect(result).toBeInstanceOf(Promise);
  });

  test("traced function returns original result with asyncFlush=true", async ({
    orgApiKey,
  }) => {
    const logger = initLogger({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      projectName: "p",
    });

    const result = traced(() => traced(() => "hello"), {
      parent: await logger.export(),
    });
    expect(result).toBe("hello");
  });

  test("traced function returns Promise with asyncFlush=false", async ({
    orgApiKey,
  }) => {
    const logger = initLogger({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      projectName: "p",
      asyncFlush: false,
    });

    const result = traced(() => traced(() => "hello", { asyncFlush: false }), {
      parent: await logger.export(),
      asyncFlush: false,
    });
    expect(result).toBeInstanceOf(Promise);
  });

  test("currentLogger respects asyncFlush setting", async ({ orgApiKey }) => {
    initLogger({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      projectName: "p",
    });

    const result1 = currentLogger()!.log({ input: "foo", output: "bar" });
    expect(result1).toBeTypeOf("string");

    const result2 = currentLogger({ asyncFlush: true })!.log({
      input: "foo",
      output: "bar",
    });
    expect(result2).toBeTypeOf("string");
  });

  test("currentLogger throws when asyncFlush doesn't match initialization", async ({
    orgApiKey,
  }) => {
    initLogger({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      projectName: "p",
      asyncFlush: false,
    });

    const result1 = currentLogger()!.log({ input: "foo", output: "bar" });
    expect(result1).toBeInstanceOf(Promise);

    const result2 = currentLogger({ asyncFlush: false })!.log({
      input: "foo",
      output: "bar",
    });
    expect(result2).toBeInstanceOf(Promise);

    expect(() => currentLogger({ asyncFlush: true })).toThrow();
  });
});

describe("span propagation", () => {
  test("propagated span attributes", async ({ orgApiKey }) => {
    await login({
      appUrl: LOCAL_APP_URL,
      apiKey: orgApiKey,
      forceLogin: true,
    });

    const experiment = init("p");

    const spanASlug = await experiment
      .startSpan({
        name: "a",
        propagatedEvent: { span_attributes: { foo: "bar" } },
      })
      .export();

    // Should not raise.
    await startSpan({
      name: "b",
      parent: spanASlug,
    });
  });
});
