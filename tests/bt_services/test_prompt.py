import braintrust
import psycopg2
import requests
from braintrust.db_fields import TRANSACTION_ID_FIELD
from braintrust.xact_ids import prettify_xact

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase, make_v1_url


def make_environment_url(*args):
    """Create URL for environment endpoints"""
    ret = f"{LOCAL_API_URL}/environment"
    for arg in args:
        ret += f"/{arg}"
    return ret


def make_environment_object_url(*args):
    """Create URL for environment object endpoints"""
    ret = f"{LOCAL_API_URL}/environment-object"
    for arg in args:
        ret += f"/{arg}"
    return ret


class PromptTestBase(BraintrustAppTestBase):
    def _get_request_headers(self):
        return dict(Authorization=f"Bearer {self.org_api_key}")

    def _insert_prompt(self, **kwargs):
        resp = requests.post(make_v1_url("prompt"), headers=self._get_request_headers(), json=kwargs)
        if not resp.ok:
            raise ValueError(resp.text)
        else:
            return resp.json()

    def _update_prompt(self, id, **kwargs):
        resp = requests.patch(make_v1_url("prompt", id), headers=self._get_request_headers(), json=kwargs)
        if not resp.ok:
            raise ValueError(resp.text)
        else:
            return resp.json()

    def _load_prompt(self, **kwargs):
        resp = requests.get(make_v1_url("prompt"), headers=self._get_request_headers(), params=kwargs)
        if not resp.ok:
            raise ValueError(resp.text)
        else:
            return resp.json()

    def _create_environment(self, slug, name):
        """Helper to create a test environment"""
        env_data = {"name": name, "slug": slug, "description": f"{name} for testing"}
        resp = requests.post(make_environment_url(), headers=self._get_request_headers(), json=env_data)
        if not resp.ok:
            raise ValueError(resp.text)
        return resp.json()

    def _create_environment_association(self, prompt_id, environment_slug, version):
        """Helper to create environment-prompt association"""
        association_data = {"object_version": version}
        resp = requests.post(
            make_environment_object_url("prompt", prompt_id),
            headers=self._get_request_headers(),
            json={"environment_slug": environment_slug, **association_data},
        )
        if not resp.ok:
            raise ValueError(resp.text)
        return resp.json()


class PromptTest(PromptTestBase):
    def setUp(self):
        super().setUp()
        self.logger = braintrust.init_logger(project="prompt test")
        self.project = self.logger.project

    def test_basic_create(self):
        slug = "my-prompt"
        prompt_record = self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 1+1"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name="my prompt",
            slug=slug,
        )

        prompt = braintrust.load_prompt(project=self.project.name, slug=slug)
        self.assertEqual(prompt_record["id"], prompt.id)

        self.assertEqual(prompt_record["prompt_data"]["prompt"]["messages"], prompt.build()["messages"])

        # Now, update the prompt with a new version
        prompt_record_v2 = self._update_prompt(
            id=prompt_record["id"],
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 1+2"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
        )

        # Make sure the new version does not match the old version
        self.assertNotEqual(prompt_record_v2["prompt_data"]["prompt"]["messages"], prompt.build()["messages"])

        # And that if we reload the prompt it does match
        prompt_v2 = braintrust.load_prompt(project=self.project.name, slug=slug)
        self.assertEqual(prompt_record_v2["prompt_data"]["prompt"]["messages"], prompt_v2.build()["messages"])

        # But that the old version still does not
        prompt_v1 = braintrust.load_prompt(
            project=self.project.name, slug=slug, version=prompt_record[TRANSACTION_ID_FIELD]
        )

        self.assertEqual(prompt_record["prompt_data"]["prompt"]["messages"], prompt_v1.build()["messages"])
        self.assertNotEqual(prompt_record_v2["prompt_data"]["prompt"]["messages"], prompt_v1.build()["messages"])

        # Now, update the prompt with a new name
        prompt_record_v3 = self._update_prompt(id=prompt_record["id"], name="Foo bar")
        prompt_v3 = braintrust.load_prompt(project=self.project.name, slug=slug)
        self.assertEqual(prompt_record_v3["name"], prompt_v3.name)
        self.assertEqual(prompt_record_v2["prompt_data"]["prompt"]["messages"], prompt_v3.build()["messages"])
        self.assertGreater(prompt_v3.version, prompt_v2.version)

        # Update the slug, and make sure that load_prompt works with the new name
        self._update_prompt(id=prompt_record["id"], slug="foo-bar")
        prompt_v4 = braintrust.load_prompt(project=self.project.name, slug="foo-bar")
        self.assertEqual(prompt_v4.name, prompt_v3.name)
        self.assertEqual(prompt_v4.build()["messages"], prompt_v3.build()["messages"])
        self.assertGreater(prompt_v4.version, prompt_v3.version)

        upserted = self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 1+1"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name="my prompt",
            slug="foo-bar",
        )
        self.assertEqual(upserted["id"], prompt_record["id"])

        # Now create a new prompt with a different slug
        baz_prompt = self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 1+1"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name="my prompt",
            slug="baz",
        )
        self.assertNotEqual(baz_prompt["id"], prompt_record["id"])

        # And try to patch the original prompt with the new one's slug, and make sure it fails
        self.assertRaises(Exception, lambda: self._update_prompt(id=prompt_record["id"], slug="baz"))

    def test_no_tools(self):
        slug = "no-tools"
        prompt_record = self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 1+1"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name=slug,
            slug=slug,
        )
        prompt = braintrust.load_prompt(project=self.project.name, slug=slug)
        self.assertEqual(prompt_record["id"], prompt.id)
        self.assertNotIn("tools", prompt.build())

    def test_empty_tools(self):
        slug = "empty-tools"
        prompt_record = self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 1+1"}],
                    "tools": "",
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name=slug,
            slug=slug,
        )
        prompt = braintrust.load_prompt(project=self.project.name, slug=slug)
        self.assertEqual(prompt_record["id"], prompt.id)
        self.assertNotIn("tools", prompt.build())

    def test_blank_space_tools(self):
        slug = "blank-space-tools"
        prompt_record = self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 1+1"}],
                    "tools": " ",
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name=slug,
            slug=slug,
        )
        prompt = braintrust.load_prompt(project=self.project.name, slug=slug)
        self.assertEqual(prompt_record["id"], prompt.id)
        self.assertNotIn("tools", prompt.build())

    def test_prompt_does_not_exist(self):
        self.assertRaises(
            ValueError,
            lambda: braintrust.load_prompt(
                project=self.project.name, slug="this prompt does not exist", version="foo"
            ).version,
        )

    def test_pretty_version(self):
        slug = "pretty-version"
        prompt_record = self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 1+1"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name="my prompt",
            slug=slug,
        )

        version = prompt_record[TRANSACTION_ID_FIELD]

        prompt = braintrust.load_prompt(project=self.project.name, slug=slug, version=version)
        self.assertEqual(prompt_record["prompt_data"]["prompt"]["messages"], prompt.build()["messages"])

        pretty_xact = prettify_xact(version)
        self.assertEqual(len(pretty_xact), 16, pretty_xact)
        prompt_pretty = braintrust.load_prompt(project=self.project.name, slug=slug, version=prettify_xact(version))
        self.assertEqual(prompt_record["prompt_data"]["prompt"]["messages"], prompt_pretty.build()["messages"])

    def test_invalid_prompt(self):
        slug = "invalid-prompt"
        self.assertRaises(
            Exception,
            lambda: self._insert_prompt(
                project_id=self.project.id,
                prompt_data={"prompt": {"foo": "bar"}},
                name="my prompt",
                slug=slug,
            ),
        )

        self.assertRaises(
            ValueError,
            lambda: braintrust.load_prompt(project=None, slug=None),
        )
        self.assertRaises(
            ValueError,
            lambda: braintrust.load_prompt(project=self.project.name, slug=None),
        )
        self.assertRaises(
            ValueError,
            lambda: braintrust.load_prompt(project=None, slug=slug),
        )

    def test_fallback(self):
        slug = "fallback-prompt"
        self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "What is 1+1"}],
                }
            },
            name="my prompt",
            slug=slug,
        )

        prompt = braintrust.load_prompt(project=self.project.name, slug=slug)

        # Should fail because there is no model specified
        self.assertRaises(ValueError, lambda: prompt.build(formula="1+1"))

        prompt_with_model = braintrust.load_prompt(
            project=self.project.name, slug=slug, defaults=dict(model="gpt-3.5-turbo")
        )
        self.assertEqual("gpt-3.5-turbo", prompt_with_model.build(formula="1+1").get("model"))

    def test_org_prompt_log_rows(self):
        loggers = [braintrust.init_logger(project=f"prompt test {i}") for i in range(5)]
        prompt_records = [
            self._insert_prompt(
                project_id=logger.project.id,
                prompt_data={
                    "prompt": {
                        "type": "chat",
                        "messages": [{"role": "user", "content": "What is 1+1"}],
                    },
                    "options": {
                        "model": "gpt-3.5-turbo",
                    },
                },
                name=f"my prompt {i}",
                slug=f"my-prompt-{i}",
            )
            for i, logger in enumerate(loggers)
        ]

        with BraintrustAppTestBase.connect_api_db() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    select logs.data
                    from logs join org_prompt_log_rows on logs.sequence_id = org_prompt_log_rows.logs_sequence_id
                    where org_prompt_log_rows.org_id = %s
                """,
                    (self.org_id,),
                )
                org_prompt_rows = [r[0] for r in cursor.fetchall()]

        self.assertEqual(set([r["id"] for r in org_prompt_rows]), set([r["id"] for r in prompt_records]))

    def test_choice_scores_presence(self):
        resp = self.registerProject({"project_name": "p"})
        project_id = resp["project"]["id"]
        tests = [
            (
                {
                    "project_id": project_id,
                    "name": "Scorer",
                    "slug": "scorer-0",
                    "prompt_data": {
                        "prompt": {
                            "type": "chat",
                            "messages": [
                                {"role": "user", "content": "Return 'A'."},
                            ],
                        },
                        "options": {"model": "gpt-4o"},
                        "parser": {
                            "type": "llm_classifier",
                            "use_cot": True,
                        },
                    },
                    "function_type": "scorer",
                },
                True,
            ),
            (
                {
                    "project_id": project_id,
                    "name": "Scorer",
                    "slug": "scorer-0",
                    "prompt_data": {
                        "prompt": {
                            "type": "chat",
                            "messages": [
                                {"role": "user", "content": "Return 'A'."},
                            ],
                        },
                        "options": {"model": "gpt-4o"},
                        "parser": {
                            "type": "llm_classifier",
                            "use_cot": True,
                            "choice_scores": {},
                        },
                    },
                    "function_type": "scorer",
                },
                True,
            ),
            (
                {
                    "project_id": project_id,
                    "name": "Scorer",
                    "slug": "scorer-0",
                    "prompt_data": {
                        "prompt": {
                            "type": "chat",
                            "messages": [
                                {"role": "user", "content": "Return 'A'."},
                            ],
                        },
                        "options": {"model": "gpt-4o"},
                        "parser": {
                            "type": "llm_classifier",
                            "use_cot": True,
                            "choice_scores": {"A": 1, "B": 0},
                        },
                    },
                    "function_type": "scorer",
                },
                False,
            ),
        ]
        for req, expect_error in tests:
            with self.subTest(req=req, expect_error=expect_error):
                self.run_request(
                    "post",
                    make_v1_url("prompt"),
                    json=req,
                    expect_error=expect_error,
                )

    def test_load_prompt_by_id(self):
        """Test loading prompts by ID"""
        slug = "prompt-by-id-test"

        # Create a prompt
        prompt_record = self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "system", "content": "You are a helpful assistant."}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name="Test Prompt for ID Loading",
            slug=slug,
        )

        prompt_id = prompt_record["id"]

        # Load prompt by ID (without project or slug)
        prompt_by_id = braintrust.load_prompt(id=prompt_id)
        self.assertEqual(prompt_by_id.id, prompt_id)
        self.assertEqual(prompt_by_id.name, "Test Prompt for ID Loading")
        self.assertEqual(prompt_by_id.build()["messages"], prompt_record["prompt_data"]["prompt"]["messages"])

        # Load prompt by slug for comparison
        prompt_by_slug = braintrust.load_prompt(project=self.project.name, slug=slug)
        self.assertEqual(prompt_by_slug.id, prompt_id)
        self.assertEqual(prompt_by_slug.build(), prompt_by_id.build())

        # Update the prompt
        updated_record = self._update_prompt(
            id=prompt_id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "system", "content": "You are an updated assistant."}],
                },
                "options": {
                    "model": "gpt-4",
                },
            },
        )

        # Load updated prompt by ID
        updated_prompt = braintrust.load_prompt(id=prompt_id)
        self.assertEqual(updated_prompt.build()["messages"], updated_record["prompt_data"]["prompt"]["messages"])
        self.assertEqual(updated_prompt.build()["model"], "gpt-4")

        # Test that ID takes precedence over other parameters
        prompt_with_both = braintrust.load_prompt(id=prompt_id, project="wrong-project", slug="wrong-slug")
        self.assertEqual(prompt_with_both.id, prompt_id)
        self.assertEqual(prompt_with_both.name, "Test Prompt for ID Loading")

        # Test loading non-existent prompt by ID
        with self.assertRaises(ValueError) as cm:
            prompt = braintrust.load_prompt(id="non-existent-id-12345")
            # Access an attribute that triggers lazy metadata loading
            _ = prompt.id
        self.assertIn("not found", str(cm.exception))

    def test_load_prompt_environment_parameter_validation(self):
        """Test that environment parameter is properly validated and passed through"""
        slug = "env-param-test"

        # Create a prompt
        prompt_record = self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "Environment parameter test"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name="Environment Parameter Test",
            slug=slug,
        )

        # Test that environment and version cannot be used together
        with self.assertRaises(ValueError) as cm:
            braintrust.load_prompt(
                project=self.project.name, slug=slug, version=prompt_record[TRANSACTION_ID_FIELD], environment="dev"
            )
        self.assertIn("Cannot specify both 'version' and 'environment'", str(cm.exception))

        # Test happy path: create environment association and load prompt with environment
        env = self._create_environment("test-env", "Test Environment")

        # Update the prompt to create a new version
        updated_record = self._update_prompt(
            id=prompt_record["id"],
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "Environment-specific content"}],
                },
                "options": {
                    "model": "gpt-4",
                },
            },
        )
        env_version = updated_record[TRANSACTION_ID_FIELD]

        # Associate the new version with the environment
        self._create_environment_association(prompt_record["id"], env["slug"], env_version)

        # Load prompt with environment parameter - should get the environment-specific version
        env_prompt = braintrust.load_prompt(project=self.project.name, slug=slug, environment=env["slug"])
        self.assertEqual(env_prompt.version, env_version)
        self.assertIn("Environment-specific content", str(env_prompt.build()["messages"]))
        self.assertEqual(env_prompt.build()["model"], "gpt-4")

        # Test that environment parameter is passed through for nonexistent environment
        # Create a separate prompt without any environment associations for this test
        no_env_prompt = self._insert_prompt(
            project_id=self.project.id,
            prompt_data={
                "prompt": {
                    "type": "chat",
                    "messages": [{"role": "user", "content": "No environment test"}],
                },
                "options": {
                    "model": "gpt-3.5-turbo",
                },
            },
            name="No Environment Test",
            slug="no-env-test-slug",
        )

        with self.assertRaises(ValueError) as cm:
            prompt = braintrust.load_prompt(
                project=self.project.name, slug="no-env-test-slug", environment="nonexistent-env"
            )
            # Access an attribute that triggers lazy metadata loading
            _ = prompt.version
        # Should get server error, proving environment param was sent to server
        self.assertIn("not found", str(cm.exception).lower())
