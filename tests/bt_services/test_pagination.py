from typing import Any, Dict
from uuid import uuid4

import braintrust
import requests
from braintrust.db_fields import TRANSACTION_ID_FIELD
from braintrust_local.api_db_util import log_raw
from parameterized import parameterized

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase

# We can no longer test query backends other than brainstore because we would
# prefer to test the row-ref-based inserts which make it impossible to use other
# query backends.
PARAMETERS = []
BRAINSTORE_PARAMETERS = PARAMETERS if BraintrustAppTestBase.skip_brainstore() else [*PARAMETERS, ("brainstore",)]


class PaginationTest(BraintrustAppTestBase):
    def _run_btql(self, mode, disable_audit_log=False, skip_relaxed_mode_check=False, headers=None, **json_payload):
        json_args = {**self.make_mode_args(mode), **json_payload}
        headers = headers if headers is not None else {"Authorization": f"Bearer {self.org_api_key}"}
        REQUEST_URL = f"{LOCAL_API_URL}/btql"

        resp = self.run_request(
            "post",
            REQUEST_URL,
            headers=headers,
            json=json_args,
        )
        contents = resp.json()
        data, cursor = contents["data"], contents.get("cursor")

        # brainstore doesn't support relaxed search mode
        if not (json_payload.get("relaxed_search_mode") or skip_relaxed_mode_check or mode == "brainstore"):
            relaxed_resp = requests.post(
                REQUEST_URL,
                headers=headers,
                json={**json_args, "relaxed_search_mode": True},
            )
            self.assertTrue(relaxed_resp.ok, relaxed_resp.text)
            relaxed_contents = resp.json()
            relaxed_data, relaxed_cursor = relaxed_contents["data"], relaxed_contents.get("cursor")
            self.assertEqual(data, relaxed_data)
            self.assertEqual(cursor, relaxed_cursor)

        disable_audit_log = (
            disable_audit_log or mode == "brainstore" or "filter:" in json_payload.get("query", "")
        )  # XXX Re-enable this once we support the audit log
        audit_log_data_grouped = {}
        if not disable_audit_log:
            resp = requests.post(
                REQUEST_URL,
                headers=headers,
                json={**json_args, "audit_log": True},
            )
            self.assertTrue(resp.ok, resp.text)
            audit_log_contents = resp.json()
            audit_log_data, audit_log_cursor = audit_log_contents["data"], audit_log_contents.get("cursor")
            self.assertEqual(cursor, audit_log_cursor)

            # Make sure we have audit log entries for all returned rows.
            for r in audit_log_data:
                audit_log_data_grouped.setdefault(r["origin"]["id"], {})[r["origin"][TRANSACTION_ID_FIELD]] = r
            for r in data:
                self.assertTrue(
                    r["id"] in audit_log_data_grouped and r[TRANSACTION_ID_FIELD] in audit_log_data_grouped[r["id"]], r
                )

        return data, audit_log_data_grouped, cursor

    @parameterized.expand(BRAINSTORE_PARAMETERS, skip_on_empty=True)
    def test_trace_expansion(self, mode):
        logger = braintrust.init_logger("p")
        with logger.start_span(name="root") as root_span:
            root_span.log(input="foo")
            with root_span.start_span(name="subspan") as subspan:
                subspan.log(input="bar")
        braintrust.flush()

        rows, _, _ = self._run_btql(
            mode,
            query=f"select: * from: project_logs('{logger.id}') traces | filter: input = 'bar'",
        )
        self.assertEqual(len(rows), 2)
        names = set([r["span_attributes"]["name"] for r in rows])
        self.assertEqual(names, {"root", "subspan"})

    @parameterized.expand(BRAINSTORE_PARAMETERS, skip_on_empty=True)
    def test_no_return_deleted_rows(self, mode):
        logger = braintrust.init_logger("p")
        # First log two rows. Log them in separate transactions so we're
        # guaranteed the second row will be considered the latest one.
        logger.log(id="row0", input="foo", output="bar", scores=dict())
        braintrust.flush()
        logger.log(id="row1", input="baz", output="qux", scores=dict())
        braintrust.flush()

        # Now delete the earlier row.
        log_raw(dict(log_id="g", project_id=logger.id, id="row0", _is_merge=True, _object_delete=True))

        # When fetching the full dataset, we should only have the second row.
        rows, _, _ = self._run_btql(mode, query=f"select: * from: project_logs('{logger.id}')")
        self.assertEqual(len(rows), 1)
        self.assertEqual(rows[0]["id"], "row1")

        # If we fetch one row at a time, we should get the second row, and then
        # an empty result.
        QUERY_STR = f"select: * from: project_logs('{logger.id}') limit: 1"
        rows, _, cursor = self._run_btql(mode, query=QUERY_STR)
        self.assertEqual(len(rows), 1)
        self.assertEqual(rows[0]["id"], "row1")
        rows, _, cursor = self._run_btql(mode, query=f"{QUERY_STR} cursor: '{cursor}'")
        self.assertEqual(len(rows), 0, rows)
        self.assertIsNone(cursor)

    @parameterized.expand(BRAINSTORE_PARAMETERS, skip_on_empty=True)
    def test_no_match_deleted_subspan(self, mode):
        logger = braintrust.init_logger("p")
        with logger.start_span(id="root") as root_span:
            root_span.log(input="foo")
            with root_span.start_span(id="subspan") as subspan:
                subspan.log(input="bar")
        braintrust.flush()

        # If we fetch the object with a filter matching the subspan row, we
        # should get the whole trace.
        QUERY_STR = f"select: * | from: project_logs('{logger.id}') traces | filter: input = 'bar' limit: 1"
        rows, _, _ = self._run_btql(mode, query=QUERY_STR)
        self.assertEqual(set(r["id"] for r in rows), set(["root", "subspan"]), rows)

        # Now delete the subspan row only.
        log_raw(dict(log_id="g", project_id=logger.id, id="subspan", _is_merge=True, _object_delete=True))

        # Running the same fetch again should return an empty result in standard
        # mode.
        rows, _, cursor = self._run_btql(mode, query=QUERY_STR, skip_relaxed_mode_check=True)
        self.assertEqual(len(rows), 0, rows)
        self.assertIsNone(cursor)

        # In relaxed mode, we should match against the deleted row and get the
        # currently-live trace (which is just one row).
        if mode == "postgres":
            rows, _, cursor = self._run_btql(mode, query=QUERY_STR, relaxed_search_mode=True)
            self.assertEqual(set(r["id"] for r in rows), set(["root"]), rows)
            self.assertIsNotNone(cursor)

    @parameterized.expand(BRAINSTORE_PARAMETERS, skip_on_empty=True)
    def test_no_return_same_trace(self, mode):
        logger = braintrust.init_logger("p")

        with braintrust._internal_with_custom_background_logger() as custom_logger:
            # Control the flushes so we can guarantee which batches of rows
            # appear in which transactions.
            custom_logger.sync_flush = True
            custom_logger.default_batch_size = 1000

            with logger.start_span() as root_span:
                root_span.log(input="foo")
                # Log a bunch of subspans.
                for i in range(100):
                    with root_span.start_span() as subspan:
                        subspan.log(input="bar")
                braintrust.flush()

                # Log a different trace.
                logger.log(id="qux", input="qux", output="qux", scores=dict(), allow_concurrent_with_spans=True)
                braintrust.flush()

                # Now log a bunch more subspans, which should show up in a later
                # transaction than the first one.
                for i in range(100):
                    with root_span.start_span() as subspan:
                        subspan.log(input="bar")
            braintrust.flush()

        import time

        # If we fetch the object with a limit of 1, we should get 201 rows of
        # the first trace (200 subspans plus one root span).
        QUERY_STR = f"select: * from: project_logs('{logger.id}') traces limit: 1"
        rows, _, cursor = self._run_btql(mode, query=QUERY_STR)
        self.assertEqual(len([r for r in rows if r["input"] == "bar"]), 200)
        self.assertEqual(len([r for r in rows if r["input"] == "foo"]), 1)

        # Searching for the next page should yield the individual intermediate
        # trace.
        rows, _, cursor = self._run_btql(mode, query=f"{QUERY_STR} cursor: '{cursor}'")
        self.assertEqual(len(rows), 1)
        self.assertEqual(rows[0]["id"], "qux")

        # The next page should yield an empty result set with no cursor in
        # standard mode. In relaxed mode, it should return the full trace again
        # and then empty.
        prev_cursor = cursor
        rows, _, cursor = self._run_btql(mode, query=f"{QUERY_STR} cursor: '{cursor}'", skip_relaxed_mode_check=True)
        self.assertEqual(len(rows), 0)
        self.assertIsNone(cursor)

        if mode != "brainstore":
            rows, _, cursor = self._run_btql(
                mode, query=f"{QUERY_STR} cursor: '{prev_cursor}'", relaxed_search_mode=True
            )
            self.assertEqual(len([r for r in rows if r["input"] == "bar"]), 200)
            self.assertEqual(len([r for r in rows if r["input"] == "foo"]), 1)

            # The next page should yield an empty result set with no cursor.
            rows, _, cursor = self._run_btql(mode, query=f"{QUERY_STR} cursor: '{cursor}'")
            self.assertEqual(len(rows), 0)
            self.assertIsNone(cursor)

    @parameterized.expand(BRAINSTORE_PARAMETERS, skip_on_empty=True)
    def test_filter_for_latest_rows_bug(self, mode):
        # Test that the logic in the pagination query which filters out rows
        # that are not the newest version is correct. We found a bug where it
        # would not properly consider the "object_id" part of the row when
        # matching, so we test pagination on an object after writing to a second
        # object with equivalent row IDs.

        logger1 = braintrust.init_logger("p1")
        logger2 = braintrust.init_logger("p2")

        logger1.log(id="foo", input="bar0", output="baz0", scores=dict())
        # Ensure that the logger2 row has a greater transaction ID.
        braintrust.flush()
        logger2.log(id="foo", input="bar1", output="baz1", scores=dict())
        braintrust.flush()

        # If we fetch the object with a limit of 1, we should get one row
        QUERY_STR = f"select: * from: project_logs('{logger1.id}') limit: 1"
        rows, _, cursor = self._run_btql(mode, query=QUERY_STR)
        self.assertEqual(len(rows), 1)
        self.assertEqual(
            {k: v for k, v in rows[0].items() if k in ["id", "input", "output"]},
            dict(id="foo", input="bar0", output="baz0"),
        )
        rows, _, cursor = self._run_btql(mode, query=f"{QUERY_STR} cursor: '{cursor}'")
        self.assertEqual(len(rows), 0)
        self.assertIsNone(cursor)

    @parameterized.expand(BRAINSTORE_PARAMETERS, skip_on_empty=True)
    def test_pagination_cursor_repeat_rows_bug(self, mode):
        # There was a bug in the returned pagination cursor, which would cause
        # it to reconsider already-returned rows, because it only looked at root
        # span results. We can exercise this by paginating with a filter that
        # only matches a subspan.

        with braintrust._internal_with_custom_background_logger() as logger:
            logger.sync_flush = True

            logger = braintrust.init_logger("p")
            project_logs_id = logger.id

            # This row is unrelated to the trace and has the lowest transaction ID.
            logger.log(id="other_row", input="x", output="x", scores=dict())
            logger.flush()

            with logger.start_span(id="root_span") as root_span:
                with root_span.start_span(id="subspan") as subspan:
                    subspan.log(input="x")
                # After this, the subspan should be completely flushed and have
                # the intermediate transaction ID. There is incidentally also an
                # old version of the root span in this transaction.
                logger.flush()

            # The final flush will log the final update to the root span, with
            # the latest transaction ID.
            logger.flush()

        # Now if we fetch the object with filter matching the subspan and limit
        # 1, we should get the full trace.
        QUERY_STR = f"select: * from: project_logs('{project_logs_id}') traces filter: input='x' limit: 1"
        rows, _, cursor = self._run_btql(mode, query=QUERY_STR)
        self.assertEqual(set(r["id"] for r in rows), set(["root_span", "subspan"]))

        # If we fetch the next page, we should just get the lone row we logged
        # originally.
        rows, _, cursor = self._run_btql(mode, query=f"{QUERY_STR} cursor: '{cursor}'")
        self.assertEqual(set(r["id"] for r in rows), set(["other_row"]))

        rows, _, cursor = self._run_btql(mode, query=f"{QUERY_STR} cursor: '{cursor}'")
        self.assertEqual(len(rows), 0)
        self.assertIsNone(cursor)

    @parameterized.expand(BRAINSTORE_PARAMETERS, skip_on_empty=True)
    def test_pagination_cursor_skip_rows_bug(self, mode):
        # There was a bug in the returned pagination cursor, which would cause
        # us to skip over rows that were logged in the middle of a single trace,
        # unrelated to that trace.

        with braintrust._internal_with_custom_background_logger() as logger:
            logger.sync_flush = True

            logger = braintrust.init_logger("p")
            project_logs_id = logger.id

            with logger.start_span(id="root_span") as root_span:
                # First log the subspan (and the original version of the root
                # span).
                with root_span.start_span(id="subspan") as subspan:
                    pass
                logger.flush()
                # Then log an unrelated row.
                logger.log(id="other_row", input="x", output="x", scores=dict(), allow_concurrent_with_spans=True)
                logger.flush()

            # Finally log the final version of the root span row. So the
            # unrelated row is sandwiched in between the span rows.
            logger.flush()

        first_fetch_row_ids = set(["root_span", "subspan"])
        second_fetch_row_ids = set(["other_row"])
        if mode == "brainstore":
            # In brainstore, the unrelated row will be the first row returned,
            # because we order by pagination key, and the root span's pagination
            # key is preserved over updates.
            first_fetch_row_ids, second_fetch_row_ids = second_fetch_row_ids, first_fetch_row_ids

        # Now if we fetch the object with limit 1, we should get the
        # first_fetch_row_ids.
        QUERY_STR = f"select: * from: project_logs('{project_logs_id}') traces limit: 1"
        rows, _, cursor = self._run_btql(mode, query=QUERY_STR)
        self.assertEqual(set(r["id"] for r in rows), first_fetch_row_ids)

        # If we fetch the next page, we should get the second_fetch_row_ids.
        rows, _, cursor = self._run_btql(mode, query=f"{QUERY_STR} cursor: '{cursor}'")
        self.assertEqual(set(r["id"] for r in rows), second_fetch_row_ids)

        # The next page should be empty in standard mode. In relaxed search
        # mode, we should get the original trace one more time.
        prev_cursor = cursor
        rows, _, cursor = self._run_btql(mode, query=f"{QUERY_STR} cursor: '{cursor}'", skip_relaxed_mode_check=True)
        self.assertEqual(len(rows), 0)
        self.assertIsNone(cursor)

        if mode != "brainstore":
            rows, _, cursor = self._run_btql(
                mode, query=f"{QUERY_STR} cursor: '{prev_cursor}'", relaxed_search_mode=True
            )
            self.assertEqual(set(r["id"] for r in rows), first_fetch_row_ids)
            self.assertIsNotNone(cursor)

            rows, _, cursor = self._run_btql(mode, query=f"{QUERY_STR} cursor: '{cursor}'", relaxed_search_mode=True)
            self.assertEqual(len(rows), 0)
            self.assertIsNone(cursor)

    @parameterized.expand(PARAMETERS, skip_on_empty=True)
    def test_audit_log_contains_all_row_versions(self, mode):
        with braintrust._internal_with_custom_background_logger() as logger:
            logger.sync_flush = True

            logger = braintrust.init_logger("p")
            row_data: Dict[str, Any] = dict(input="foo", output="bar", expected="baz", scores=dict())

            for i in range(5):
                logger.log(id=f"row{i}", **row_data)
                logger.flush()
            for i in range(5):
                logger.log(id=f"row{i}", **row_data)
                logger.flush()
            for i in range(5):
                log_raw(dict(log_id="g", project_id=logger.id, id=f"row{i}", _object_delete=True))
                logger.flush()
            for i in range(5):
                logger.log(id=f"row{i}", **row_data)
                logger.flush()

        # If we paginate, we should get the rows in decreasing order of ids. The
        # audit log should contain four entries for each row.
        QUERY_STR = f"select: * from: project_logs('{logger.id}') limit: 1"
        cursor = None
        for i in reversed(list(range(5))):
            query_str = f"{QUERY_STR} cursor: '{cursor}'" if cursor else QUERY_STR
            rows, audit_log_grouped, cursor = self._run_btql(mode, query=query_str)
            self.assertEqual(len(rows), 1)
            self.assertEqual(rows[0]["id"], f"row{i}")
            self.assertEqual(len(audit_log_grouped), 1)
            self.assertIn(rows[0]["id"], audit_log_grouped)
            audit_log_xact_id_to_data = sorted(
                [(int(key), value) for key, value in audit_log_grouped[rows[0]["id"]].items()]
            )
            self.assertEqual(len(audit_log_xact_id_to_data), 4)
            self.assertEqual(len(set(x[0] for x in audit_log_xact_id_to_data)), 4)
            audit_data_actions = [x[1]["audit_data"]["action"] for x in audit_log_xact_id_to_data]
            self.assertEqual(audit_data_actions, ["upsert", "upsert", "delete", "upsert"])

    @parameterized.expand(BRAINSTORE_PARAMETERS, skip_on_empty=True)
    def test_cross_project_logs_pagination(self, mode):
        # Make sure that pagination works across project logs, including when logs have the same
        # ids as values.

        ids = ["foo", "bar", "baz", "qux"]
        delete_ids = ["foo", "baz"]

        l1 = braintrust.init_logger("p1")
        l2 = braintrust.init_logger("p2")

        for logger in [l1, l2]:
            for id in ids:
                logger.log(id=id, input=id, output=id, scores=dict())
                braintrust.flush()

        for logger in [l1, l2]:
            for id in delete_ids:
                log_raw(dict(project_id=logger.project.id, log_id="g", id=id, _object_delete=True))

        # If we fetch the object with a limit of 1, we should get one row
        QUERY_STR = f"select: * | from: project_logs('{l1.project.id}', '{l2.project.id}') limit: 1"
        logs = []
        cursor = None
        while True:
            rows, _, cursor = self._run_btql(mode, query=QUERY_STR + (f" cursor: '{cursor}'" if cursor else ""))
            if not rows:
                break
            logs.extend(rows)

        self.assertEqual(len(logs), 4)  # 8 original - 4 deleted = 4 remaining

    @parameterized.expand(BRAINSTORE_PARAMETERS, skip_on_empty=True)
    def test_cross_dataset_pagination_with_version(self, mode):
        # Make sure that pagination works across datasets, including version filtering

        ids = ["foo", "bar", "baz", "qux"]
        delete_ids = ["foo", "baz"]

        d1 = braintrust.init_dataset("d1")
        d2 = braintrust.init_dataset("d2")

        for dataset in [d1, d2]:
            for id in ids:
                dataset.insert(id=id, input=id, output=id)
                braintrust.flush()

        # Snapshot the version of the logs
        MAX_XACT_QUERY = f"measures: max(_xact_id) AS foo | from: dataset('{d1.id}', '{d2.id}')"
        rows, _, _ = self._run_btql(mode, query=MAX_XACT_QUERY, disable_audit_log=True)
        max_xact_id = rows[0]["foo"]

        for dataset in [d1, d2]:
            for id in delete_ids:
                log_raw(dict(dataset_id=dataset.id, id=id, _object_delete=True))

        # If we fetch the object with a limit of 1, we should get one row
        QUERY_STR = f"select: * | from: dataset('{d1.id}', '{d2.id}') limit: 1"
        logs = []
        cursor = None
        while True:
            rows, _, cursor = self._run_btql(
                mode, query=QUERY_STR + (f" cursor: '{cursor}'" if cursor else ""), version=max_xact_id
            )
            if not rows:
                break
            logs.extend(rows)

        self.assertEqual(len(logs), 8)  # Version filter means we see all original rows

    @parameterized.expand(BRAINSTORE_PARAMETERS, skip_on_empty=True)
    def test_org_prompt_pagination(self, mode):
        def make_org_prompt(api_key):
            headers = dict(Authorization=f"Bearer {api_key}")
            project_name = str(uuid4())
            project = self.run_request(
                "post", f"{LOCAL_API_URL}/v1/project", headers=headers, json=dict(name=project_name)
            ).json()
            slug = str(uuid4())
            prompt = self.run_request(
                "post",
                f"{LOCAL_API_URL}/v1/prompt",
                headers=headers,
                json=dict(
                    project_id=project["id"],
                    prompt_data=dict(prompt=dict(type="chat", messages=[])),
                    name=slug,
                    slug=slug,
                ),
            ).json()
            return prompt

        prompts = [make_org_prompt(self.org_api_key) for _ in range(2)]

        # Grab all prompts in the org.
        ORG_PROMPTS_QUERY = f"""select: * from: org_prompts("{self.org_id}")"""
        rows, _, _ = self._run_btql(mode, query=ORG_PROMPTS_QUERY)
        self.assertEqual(set(r["id"] for r in rows), set(p["id"] for p in prompts))

        # Create a separate org which includes our user and add some prompts.
        other_org_id, _ = self.createOrg()
        self.addUserToOrg(self.user_id, other_org_id)
        other_org_api_key = self.createUserOrgApiKey(self.user_id, other_org_id)
        other_prompts = [make_org_prompt(other_org_api_key) for _ in range(2)]

        # Grab all prompts across both orgs.
        cross_org_api_key = self.createUserOrgApiKey(self.user_id, None)
        cross_org_headers = dict(Authorization=f"Bearer {cross_org_api_key}")
        MULTI_ORG_FUNCTIONS_QUERY = f"""select: * from: org_functions("{self.org_id}", "{other_org_id}")"""
        rows, _, _ = self._run_btql(mode, headers=cross_org_headers, query=MULTI_ORG_FUNCTIONS_QUERY)
        self.assertEqual(set(r["id"] for r in rows), set(p["id"] for p in (prompts + other_prompts)))

    @parameterized.expand(BRAINSTORE_PARAMETERS, skip_on_empty=True)
    def test_query_without_from_clause_error(self, mode):
        """Test that queries without from clause return proper error, not internal server error"""
        with self.assertRaises(Exception) as context:
            self._run_btql(mode, query="select: id")

        # Should get a BadRequestError (400), not InternalServerError (500)
        error_message = str(context.exception)
        self.assertTrue(
            "400" in error_message or "BadRequestError" in error_message,
            f"Expected BadRequestError but got: {error_message}",
        )
