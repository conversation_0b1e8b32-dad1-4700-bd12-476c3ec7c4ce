{"name": "@braintrust/proxy", "version": "0.0.9", "description": "A proxy server that load balances across AI providers.", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "watch": "tsup --watch", "clean": "rm -r dist/*", "test": "vitest run"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "module": "./dist/index.mjs", "require": "./dist/index.js"}, "./edge": {"types": "./edge/dist/index.d.ts", "import": "./edge/dist/index.mjs", "module": "./edge/dist/index.mjs", "require": "./edge/dist/index.js"}, "./schema": {"types": "./schema/dist/index.d.ts", "import": "./schema/dist/index.mjs", "module": "./schema/dist/index.mjs", "require": "./schema/dist/index.js"}, "./utils": {"types": "./utils/dist/index.d.ts", "import": "./utils/dist/index.mjs", "module": "./utils/dist/index.mjs", "require": "./utils/dist/index.js"}, "./types": {"types": "./types/dist/index.d.ts", "import": "./types/dist/index.mjs", "module": "./types/dist/index.mjs", "require": "./types/dist/index.js"}}, "files": ["dist/**/*", "edge/dist/**/*", "schema/dist/**/*", "types/dist/**/*"], "license": "MIT", "publishConfig": {"access": "public"}, "homepage": "https://www.braintrust.dev/docs/guides/proxy", "repository": {"type": "git", "url": "git+https://github.com/braintrustdata/braintrust-proxy.git"}, "bugs": {"url": "https://github.com/braintrustdata/braintrust-proxy/issues"}, "keywords": ["ai", "proxy", "vercel", "cloudflare", "workers", "edge", "openai", "lambda", "express"], "devDependencies": {"@types/content-disposition": "^0.5.8", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^8.21.0", "esbuild": "^0.19.10", "msw": "^2.8.2", "npm-run-all": "^4.1.5", "skott": "^0.35.4", "tsup": "^8.4.0", "typescript": "5.5.4", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.1.9", "yargs": "^17.7.2"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@apidevtools/json-schema-ref-parser": "^11.9.1", "@aws-sdk/client-bedrock-runtime": "^3.806.0", "@braintrust/core": "^0.0.93", "@breezystack/lamejs": "^1.2.7", "@google/genai": "^0.13.0", "@opentelemetry/api": "^1.7.0", "@opentelemetry/core": "^1.19.0", "@opentelemetry/resources": "^1.19.0", "@opentelemetry/sdk-metrics": "^1.19.0", "ai": "2.2.37", "cache-control-parser": "^2.0.6", "content-disposition": "^0.5.4", "date-fns": "^4.1.0", "eventsource-parser": "^1.1.1", "jose": "^5.9.6", "jsonwebtoken": "^9.0.2", "openai": "4.89.0", "uuid": "^9.0.1", "zod": "^3.25.34"}}